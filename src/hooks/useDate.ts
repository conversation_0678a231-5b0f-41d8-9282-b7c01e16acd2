import { useEffect, useMemo } from 'react';
import { useLanguage } from './useLanguage';
import { DateUtils } from '@/utils/dateUtils';

/**
 * 日期时间处理 Hook
 * 自动根据当前语言设置日期格式化
 */
export const useDate = () => {
  const { language: currentLanguage } = useLanguage();

  // 当语言改变时，更新 dayjs 的本地化设置
  const { formatDateTime, formatByLanguage } = useMemo(() => {
    DateUtils.setLocale(currentLanguage);

    return {
      formatDateTime: (timestamp: number | string, format?: string) =>
        DateUtils.formatTime(timestamp, format, currentLanguage),

      // formatRelativeTime: (timestamp: number | string) =>
      //   DateUtils.formatRelativeTime(timestamp, currentLanguage),

      formatByLanguage: (timestamp: number | string) =>
        DateUtils.formatByLanguage(timestamp, currentLanguage),
    };
  }, [currentLanguage]);
  return {
    formatDateTime,
    // formatRelativeTime,
    formatByLanguage,
    currentLanguage,
  };
};

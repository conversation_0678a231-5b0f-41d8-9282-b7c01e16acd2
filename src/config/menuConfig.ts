import i18n from '@/locales';
/**
 * 菜单配置
 * 定义所有用户都能访问的固定菜单项
 */

export interface MenuItem {
  key: string;
  name: string;
  url: string;
  icon?: string;
  children?: MenuItem[];
}

/**
 * 主菜单配置 - 所有用户都能访问
 */
export const MAIN_MENU_ITEMS: MenuItem[] = [
  {
    key: 'music-market',
    name: i18n.t('common.menu.musicMarket'),
    url: '/music-market',
    icon: 'music',
  },
  {
    key: 'my-assets',
    name: i18n.t('common.menu.myAssets'),
    url: '/my-assets',
    icon: 'folder',
  },
  {
    key: 'my-balance',
    name: i18n.t('common.menu.myBalance'),
    url: '/my-balance',
    icon: 'wallet',
  },
  {
    key: 'my-orders',
    name: i18n.t('common.menu.myOrders'),
    url: '/my-orders',
    icon: 'shopping',
  },
  {
    key: 'submit-music',
    name: i18n.t('common.menu.submitMusic'),
    url: '/submit-music',
    icon: 'upload',
  },
];

/**
 * 获取所有菜单URL（扁平化）
 */
export const getAllMenuUrls = (): string[] => {
  const urls: string[] = [];

  const traverse = (items: MenuItem[]) => {
    items.forEach(item => {
      urls.push(item.url);
      if (item.children) {
        traverse(item.children);
      }
    });
  };

  traverse(MAIN_MENU_ITEMS);
  return urls;
};

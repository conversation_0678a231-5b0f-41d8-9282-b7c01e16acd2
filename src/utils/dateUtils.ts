import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
// import localizedFormat from 'dayjs/plugin/localizedFormat';
// import utc from 'dayjs/plugin/utc';
// import timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';

// 加载插件
dayjs.extend(relativeTime);
// dayjs.extend(localizedFormat);
// dayjs.extend(utc);
// dayjs.extend(timezone);

/**
 * 日期时间工具类
 */
export class DateUtils {
  /**
   * 根据语言设置 dayjs 的本地化
   * @param language 语言代码 ('zh' | 'en')
   */
  static setLocale(language: string) {
    const locale = language === 'zh' ? 'zh-cn' : 'en';
    console.log('locale-----', locale);

    dayjs.locale(locale);
  }

  /**
   * 格式化时间戳为本地化日期时间
   * @param timestamp 时间戳（秒或毫秒）
   * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
   * @param language 语言代码
   * @returns 格式化后的日期字符串
   */
  static formatTime(
    timestamp: number | string,
    format: string = 'YYYY-MM-DD HH:mm:ss',
    language?: string
  ): string {
    if (language) {
      this.setLocale(language);
    }

    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    // 判断是秒还是毫秒时间戳
    const date = ts > 1e12 ? dayjs(ts) : dayjs.unix(ts);

    return date.format(format);
  }

  /**
   * 格式化为相对时间（如：2小时前、3天前）
   * @param timestamp 时间戳
   * @param language 语言代码
   * @returns 相对时间字符串
   */
  static formatRelativeTime(
    timestamp: number | string,
    language?: string
  ): string {
    if (language) {
      this.setLocale(language);
    }

    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    const date = ts > 1e12 ? dayjs(ts) : dayjs.unix(ts);

    return date.fromNow();
  }

  /**
   * 格式化为美国格式的日期时间
   * @param timestamp 时间戳
   * @returns 美国格式的日期字符串 (MM/DD/YYYY HH:mm:ss)
   */
  static formatUSDateTime(timestamp: number | string): string {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    const date = ts > 1e12 ? dayjs(ts) : dayjs.unix(ts);

    // 强制使用英文格式，不受全局语言设置影响
    // return date.locale('en').format('MM/DD/YYYY hh:mm:ss A');
    return date.format('MM/DD/YYYY hh:mm:ss A');
  }

  /**
   * 格式化为中国格式的日期时间
   * @param timestamp 时间戳
   * @returns 中国格式的日期字符串 (YYYY年MM月DD日 HH:mm:ss)
   */
  static formatCNDateTime(timestamp: number | string): string {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
    const date = ts > 1e12 ? dayjs(ts) : dayjs.unix(ts);
    // 强制使用中文格式，不受全局语言设置影响
    // return date.locale('zh-cn').format('YYYY年MM月DD日 HH:mm:ss');
    return date.format('YYYY年MM月DD日 HH:mm:ss');
  }

  /**
   * 根据语言自动选择合适的日期格式
   * @param timestamp 时间戳
   * @param language 语言代码
   * @returns 格式化后的日期字符串
   */
  static formatByLanguage(
    timestamp: number | string,
    language: string = 'en'
  ): string {
    if (language === 'zh') {
      return this.formatCNDateTime(timestamp);
    } else {
      return this.formatUSDateTime(timestamp);
    }
  }
}

// 导出默认实例方法
export const dateUtils = DateUtils;

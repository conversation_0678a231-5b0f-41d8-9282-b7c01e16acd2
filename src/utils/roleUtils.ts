import { DEFAULT_ROLES } from '@/types/api';
import type { User } from '@/types';

/**
 * 角色相关工具函数
 */

/**
 * 获取用户的主要角色ID
 * @param user 用户信息
 * @returns 角色ID字符串
 */
export const getUserPrimaryRole = (user: User | null): string | null => {
  if (!user || !user.roles || user.roles.length === 0) {
    return null;
  }

  // 返回第一个角色的ID（通常用户只有一个主要角色）
  return user.roles[0].id;
};

/**
 * 检查用户是否具有指定角色
 * @param user 用户信息
 * @param roleId 角色ID
 * @returns 是否具有该角色
 */
export const hasRole = (user: User | null, roleId: string): boolean => {
  if (!user || !user.roles) {
    return false;
  }

  return user.roles.some(role => role.id === roleId);
};

/**
 * 根据用户角色获取默认页面路径
 * @param user 用户信息
 * @returns 默认页面路径
 */
export const getDefaultPageByRole = (user: User | null): string => {
  const primaryRole = getUserPrimaryRole(user);

  switch (primaryRole) {
    case DEFAULT_ROLES.ARTIST:
      return '/submit-music';
    case DEFAULT_ROLES.INVESTOR:
      return '/music-market';
    default:
      return '/music-market'; // 默认进入音乐市场
  }
};

/**
 * 检查用户是否为艺人
 * @param user 用户信息
 * @returns 是否为艺人
 */
export const isArtist = (user: User | null): boolean => {
  return hasRole(user, DEFAULT_ROLES.ARTIST);
};

/**
 * 检查用户是否为投资者
 * @param user 用户信息
 * @returns 是否为投资者
 */
export const isInvestor = (user: User | null): boolean => {
  return hasRole(user, DEFAULT_ROLES.INVESTOR);
};

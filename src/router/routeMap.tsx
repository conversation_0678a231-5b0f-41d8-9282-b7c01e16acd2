import React from 'react';
import MusicMarket from '@/pages/MusicMarket';
import SubmitMusic from '@/pages/SubmitMusic';
import MyAssets from '@/pages/MyAssets';
import MyBalance from '@/pages/MyBalance';
import MyOrders from '@/pages/MyOrders';

/**
 * 页面组件的查找表
 * key: 后端返回的 URL 路径
 * value: 前端对应的 React 组件
 */
export const pageMap: Record<string, React.ComponentType> = {
  // 音乐市场
  '/music-market': () => <MusicMarket />,
  '/music-market/browse': () => <div>浏览音乐</div>,
  '/music-market/purchase': () => <div>购买音乐</div>,

  // 我的资产
  '/my-assets': () => <MyAssets />,
  '/my-assets/library': () => <div>我的音乐库</div>,
  '/my-assets/licenses': () => <div>我的许可证</div>,

  // 我的余额
  '/my-balance': () => <MyBalance />,
  '/my-balance/overview': () => <div>余额概览</div>,
  '/my-balance/transactions': () => <div>交易记录</div>,

  // 我的订单
  '/my-orders': () => <MyOrders />,
  '/my-orders/list': () => <div>订单列表</div>,
  '/my-orders/history': () => <div>订单历史</div>,

  // 提交音乐
  '/submit-music': () => <SubmitMusic />,
  '/submit-music/upload': () => <div>上传音乐</div>,
  '/submit-music/manage': () => <div>管理我的音乐</div>,
};

/**
 * 后端返回的权限对象类型定义
 */
export interface Permission {
  code: string;
  url: string;
  type: number;
  name: string;
  children?: Permission[];
  parentCode?: string | null;
  icon?: string | null;
  levels?: number;
  appCode?: string;
  remarks?: string | null;
  stem?: string;
  createTime?: string | null;
  updateTime?: string | null;
  i18nDefs?: any;
}

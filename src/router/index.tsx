import React, { useMemo } from 'react';
import {
  createHashRouter,
  RouterProvider,
  type RouteObject,
} from 'react-router-dom';
import { pageMap } from './routeMap';
import Layout from '@/components/Layout';
import Login from '../pages/Login';
import NotFound from '../pages/NotFound';
// 路由守卫组件 用于保护需要认证的路由 如果用户未登录，会重定向到登录页面
import RequireAuth from '../components/RequireAuth';
// 认证重定向守卫组件 如果用户已登录，则重定向到指定页面
import RedirectIfAuthenticated from '../components/RedirectIfAuthenticated';
// 注册流程页面
import Register from '../pages/Register';
import EmailVerification from '../pages/Register/EmailVerification';
import AliasSetup from '../pages/Register/AliasSetup';
import PersonalInfo from '../pages/Register/PersonalInfo';
import PasswordSetup from '../pages/Register/PasswordSetup';
import { getAllMenuUrls } from '@/config/menuConfig';
import HomeHandler from '@/components/HomeHandler';

/**
 * 生成固定的应用路由
 * 所有用户都能访问这些页面
 */
const buildAppRoutes = (): RouteObject[] => {
  const menuUrls = getAllMenuUrls();
  const routes: RouteObject[] = menuUrls
    .map(url => {
      if (pageMap[url]) {
        return {
          path: url,
          element: (
            <RequireAuth>{React.createElement(pageMap[url])}</RequireAuth>
          ),
        };
      }
    })
    .filter(Boolean) as RouteObject[];

  return routes;
};

const AppRouter: React.FC = () => {
  const appRoutes = buildAppRoutes();

  const router = useMemo(() => {
    return createHashRouter([
      {
        path: '/login',
        element: (
          <RedirectIfAuthenticated>
            <Login />
          </RedirectIfAuthenticated>
        ),
      },
      // 注册流程路由
      {
        path: '/register',
        element: (
          <RedirectIfAuthenticated>
            <Register />
          </RedirectIfAuthenticated>
        ),
        children: [
          {
            path: 'email',
            element: <EmailVerification />,
          },
          {
            path: 'alias',
            element: <AliasSetup />,
          },
          {
            path: 'personal-info',
            element: <PersonalInfo />,
          },
          {
            path: 'password',
            element: <PasswordSetup />,
          },
        ],
      },
      // 主应用布局
      {
        path: '/',
        element: <Layout />,
        children: [
          {
            index: true,
            element: <HomeHandler />, // 未登录显示Home页面，已登录重定向到默认页面
          },
          // 固定的应用路由
          ...appRoutes,
        ],
      },
      // 通配符路由 - 404 页面
      {
        path: '*',
        element: (
          <RequireAuth>
            <NotFound />
          </RequireAuth>
        ),
      },
    ]);
  }, [appRoutes]);

  return <RouterProvider router={router} />;
};

export default AppRouter;

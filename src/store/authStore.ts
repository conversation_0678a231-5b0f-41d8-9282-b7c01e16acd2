import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { User, ExtendedLoginRequest } from '@/types';
import type { Permission } from '@/router/routeMap';
import type { UserProfileResponse } from '@/types/api';
import { api } from '@/services';
import { isArtist, isInvestor } from '@/utils/roleUtils';

export type { User };

interface AuthState {
  user: User | null;
  profile: UserProfileResponse | null; // 用户详细资料
  token: string | null;
  isAuthenticated: boolean; // 登录状态
  profileLoading: boolean; // profile加载状态
  profileError: string | null; // profile错误信息
  isArtist: boolean;
  isInvestor: boolean;
  login: (loginData: ExtendedLoginRequest) => Promise<void>;
  setAuthData: (token: string, user: User) => void; // 直接设置认证数据
  fetchUserInfo: () => void;
  fetchUserProfile: () => Promise<void>; // 获取用户详细资料
  updateProfileData: (profile: UserProfileResponse) => void; // 更新profile数据
  initializeAuth: () => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        profile: null,
        token: null,
        isAuthenticated: false,
        profileLoading: false,
        profileError: null,

        // 计算属性
        get isArtist() {
          // console.log('isAuthenticated---', get()?.isAuthenticated);

          // console.log('get().user----', get()?.user);

          return isArtist(get()?.user);
        },
        get isInvestor() {
          return isInvestor(get()?.user);
        },
        login: async loginData => {
          try {
            // 1. 判断是密码登录还是验证码登录
            let result;
            if (loginData.verificationCode) {
              // 验证码登录
              result = await api.auth.loginWithOtp({
                username: loginData.username,
                verificationCode: loginData.verificationCode,
              });
            } else {
              // 密码登录
              result = await api.auth.login({
                username: loginData.username,
                password: loginData.password,
              });
            }

            if (result.code === 200) {
              const authData = result.body;

              // 构建用户对象
              const user: User = {
                accountId: authData.accountId,
                email: '', // 登录接口不返回email，需要从profile接口获取
                alias: authData.alias,
                firstName: '',
                lastName: '',
                displayName: authData.displayName,
                avatarUrl: authData.avatarUrl || null,
                stageName: authData.stageName,
                roles: authData.roles,
              };

              set({
                token: authData.token,
                isAuthenticated: true,
                user: user,
              });
              localStorage.setItem('token', authData.token);

              // 2. 尝试获取完整用户信息（可选）
              try {
                await get().fetchUserProfile();
                console.log('获取完整用户数据成功');
              } catch (userInfoError) {
                console.error(
                  '获取完整用户信息失败，但登录成功:',
                  userInfoError
                );
                // 不抛出错误，允许登录继续
              }
            } else {
              throw new Error(result.message);
            }
          } catch (error: any) {
            console.error('登录失败----', error);

            // 如果是网络错误或其他非API错误
            if (!error.message || error.message.includes('fetch')) {
              throw new Error('网络连接失败，请检查网络设置');
            }

            throw error;
          }
        },
        // 直接设置认证数据（用于注册成功后）
        setAuthData: (token: string, user: User) => {
          set({
            token,
            user,
            isAuthenticated: true,
          });
          localStorage.setItem('token', token);
        },
        // 获取用户信息
        fetchUserInfo: () => {
          try {
            // 如果需要获取更多用户信息，可以在这里实现
            console.log('fetchUserInfo: 不再需要获取权限信息');
          } catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
          }
        },

        // 获取用户详细资料
        fetchUserProfile: async () => {
          try {
            set({ profileLoading: true, profileError: null });
            const response = await api.user.getProfile();
            if (response.code === 200 && response.body) {
              const profileData = response.body;

              // 同时更新user数据，确保头像等信息在整个应用中保持同步
              const currentState = get();
              if (currentState.user) {
                const updatedUser: User = {
                  ...currentState.user,
                  email: profileData.email,
                  firstName: profileData.firstName,
                  lastName: profileData.lastName,
                  displayName: profileData.displayName,
                  avatarUrl: profileData.avatarUrl,
                  stageName: profileData.stageName,
                  alias: profileData.alias,
                  mobile: profileData.mobile,
                  addressLine1: profileData.addressLine1,
                  addressLine2: profileData.addressLine2,
                  stateProvince: profileData.stateProvince,
                  countryCode: profileData.countryCode,
                  postalZipCode: profileData.postalZipCode,
                  bio: profileData.bio,
                };

                set({
                  profile: profileData,
                  user: updatedUser,
                  profileLoading: false,
                  profileError: null,
                });
              } else {
                set({
                  profile: profileData,
                  profileLoading: false,
                  profileError: null,
                });
              }
            } else {
              const errorMsg = response.message || '加载用户资料失败';
              set({
                profileError: errorMsg,
                profileLoading: false,
              });
              throw new Error(errorMsg);
            }
          } catch (error) {
            console.error('获取用户资料失败:', error);
            const errorMsg =
              error instanceof Error ? error.message : '加载用户资料失败';
            set({
              profileError: errorMsg,
              profileLoading: false,
            });
            throw error;
          }
        },

        // 更新profile数据（用于编辑后刷新）
        updateProfileData: (profile: UserProfileResponse) => {
          const currentState = get();
          if (currentState.user) {
            const updatedUser: User = {
              ...currentState.user,
              email: profile.email,
              firstName: profile.firstName,
              lastName: profile.lastName,
              displayName: profile.displayName,
              avatarUrl: profile.avatarUrl,
              stageName: profile.stageName,
              alias: profile.alias,
              mobile: profile.mobile,
              addressLine1: profile.addressLine1,
              addressLine2: profile.addressLine2,
              stateProvince: profile.stateProvince,
              countryCode: profile.countryCode,
              postalZipCode: profile.postalZipCode,
              bio: profile.bio,
            };
            set({ profile, user: updatedUser });
          } else {
            set({ profile });
          }
        },
        initializeAuth: () => {
          const token = localStorage.getItem('token');
          if (token) {
            set({ token, isAuthenticated: true });
            // 初始化时获取用户profile信息
            get()
              .fetchUserProfile()
              .catch(error => {
                console.error('初始化时获取用户信息失败:', error);
                // 如果获取失败，可能token已失效，清除登录状态
                get().logout();
              });
          } else {
            // Token 无效，清除登录
            get().logout();
          }
        },
        logout: () => {
          set({
            user: null,
            profile: null,
            token: null,
            isAuthenticated: false,
            profileLoading: false,
            profileError: null,
          });
          localStorage.removeItem('token');
        },
        // isArtist: () => {
        //   // 判断用户是否为艺人
        //   return isArtist(get().user);
        // },
        // isInvestor: () => {
        //   // 判断用户是否为投资人
        //   return isInvestor(get().user);
        // },
      }),
      {
        name: 'auth-store', // localStorage持久化的名称
      }
    ),
    {
      name: 'AuthStore', // DevTools 中显示的名称
    }
  )
);

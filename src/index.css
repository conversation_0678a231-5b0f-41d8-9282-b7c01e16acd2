/* UnoCSS 样式将在 main.tsx 中导入 */

/* 自定义颜色变量和字体变量 */
:root {
  /* 页面背景色 */
  --color-page-bg: #0d0d0d;
  /* 主色调 */
  --color-primary: #ff5e13;
  --color-primary-light: #fd8439;
  /*主按钮文字颜色*/
  --color-primary-text: #000000;
  /* 主按钮激活颜色 */
  --color-primary-active: #e5541a;
  /* 主按钮禁用颜色 */
  --color-primary-disabled: rgba(255, 94, 19, 0.45);
  /*主按钮禁用文字颜色*/
  --color-primary-disabled-text: rgba(0, 0, 0, 0.45);

  /* form表单label颜色 */
  --color-label: #656565;
  /* form表单项颜色 */
  --color-form-item: #c9c9c9;
  /* 按钮文字颜色 */
  --color-button-text: #000000;
  /* 字体颜色 白色 */
  --color-font-white: #ffffff;
  /* 提交音乐页面的输入框颜色 */
  --color-submit-input-bg: #282828;
  /* 999字体颜色变量 */
  --color-999: #999999;

  /* 字体变量 优先Arial */
  --font-family: Arial, 'Segoe UI', sans-serif;
}

.yue-layout-content {
  min-width: 1200px;
  margin: 0 auto;
}
/* 全局最小宽度设置和字体设置 */
html,
body {
  /* min-width: 1200px; */
  background-color: var(--color-page-bg);
  font-family: var(--font-family);
}

#root {
  /* min-width: 1200px; */
  /* 确保根容器也有最小宽度 */
}
/* 通用：纵向滚动条宽度 */
::-webkit-scrollbar {
  width: 4px; /* 垂直滚动条 */
  height: 4px; /* 横向滚动条 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: var(--color-page-bg);
  border-radius: 4px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: var(--color-label);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #888888;
}

import React, { useState, useEffect, useMemo } from 'react';
import { Input, DatePicker, Switch, Pagination } from 'antd';
import {
  CaretUpOutlined,
  CaretDownOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';
import { api } from '@/services';
import type { Track, Genre } from '@/types/api';
import { musicUtils } from '@/services/music';
import rectangle1 from '@/assets/images/rectangle-1.png';

const { RangePicker } = DatePicker;

// 排序字段类型
type SortField = 'title' | 'genre' | 'totalRevenue' | 'totalStreams';
type SortOrder = 'asc' | 'desc' | null;

const MusicMarket: React.FC = () => {
  const { t, language } = useLanguage();

  // 状态管理
  const [tracks, setTracks] = useState<Track[]>([]);
  const [genres, setGenres] = useState<Genre[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedGenre, setSelectedGenre] = useState<string>('');
  const [dateRange, setDateRange] = useState<any>(null);
  const [sharesAvailableOnly, setSharesAvailableOnly] = useState(false);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 排序状态
  const [sortField, setSortField] = useState<SortField | null>('totalRevenue');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // 获取音乐类型列表
  const fetchGenres = async () => {
    try {
      const response = await api.music.getDefaultGenres();
      if (response.code === 200) {
        setGenres(response.body || []);
      }
    } catch (error) {
      console.error('Failed to fetch genres:', error);
    }
  };

  // 获取音乐列表 - 只获取一次数据，排序和筛选在本地进行
  const fetchTracks = async () => {
    try {
      setLoading(true);
      // 默认获取按收入排序的数据，后续排序在本地进行
      const response = await api.music.getTracksByRevenue(1, 100); // 获取更多数据用于本地排序筛选

      if (response.code === 200) {
        setTracks(response.body.pageView.result || []);
      }
    } catch (error) {
      console.error('Failed to fetch tracks:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchGenres();
    fetchTracks();
  }, []);

  // 语言变化时重新获取genres并重置筛选
  useEffect(() => {
    console.log(language);
    console.log('获取数据');

    fetchGenres();
    // 重置genre筛选，因为语言切换后genre名称可能变化
    setSelectedGenre('');
  }, [language]);

  // 排序处理函数 - 参考ArtistModal的实现
  const handleSort = (field: SortField) => {
    // 每次点击都会切换排序状态，会在这3个状态中循环。 asc → desc → null → asc
    let newOrder: SortOrder = 'asc';

    if (sortField === field) {
      if (sortOrder === 'asc') {
        newOrder = 'desc';
      } else if (sortOrder === 'desc') {
        newOrder = null;
      } else {
        newOrder = 'asc';
      }
    }

    setSortField(newOrder ? field : null);
    setSortOrder(newOrder);
    setCurrentPage(1); // 排序后重置到第一页
  };

  // 获取排序图标 - 参考ArtistModal的实现
  const getSortIcon = (field: SortField) => {
    // 非排序列，显示向下灰色箭头
    if (sortField !== field) {
      return (
        <CaretDownOutlined className="ml-1 text-10px text-#666 hover:text-#999 transition-colors cursor-pointer" />
      );
    }
    // 排序状态，根据排序状态显示向上或向下的橙色箭头
    if (sortOrder === 'asc') {
      return <CaretUpOutlined className="ml-1 text-10px text-primary" />;
    } else if (sortOrder === 'desc') {
      return <CaretDownOutlined className="ml-1 text-10px text-primary" />;
    }

    // 非排序状态，显示向下灰色箭头
    return (
      <CaretDownOutlined className="ml-1 text-10px text-#666 hover:text-#999 transition-colors cursor-pointer" />
    );
  };

  // 本地筛选和排序逻辑
  const filteredAndSortedTracks = useMemo(() => {
    let result = [...tracks];

    // 1. 筛选逻辑
    result = result.filter(track => {
      const matchesSearch =
        track.title.toLowerCase().includes(searchValue.toLowerCase()) ||
        track.artistStageName.toLowerCase().includes(searchValue.toLowerCase());
      const matchesGenre = !selectedGenre || track.genre === selectedGenre;
      // 注意：API返回的数据中没有sharesAvailable字段，这里暂时忽略该筛选条件
      // const matchesShares = !sharesAvailableOnly || track.sharesAvailable;

      return matchesSearch && matchesGenre;
    });

    // 2. 排序逻辑
    if (sortField && sortOrder) {
      result.sort((a, b) => {
        let aValue: string | number;
        let bValue: string | number;

        switch (sortField) {
          case 'title':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'genre':
            aValue = a.genre.toLowerCase();
            bValue = b.genre.toLowerCase();
            break;
          case 'totalRevenue':
            aValue = a.totalRevenue;
            bValue = b.totalRevenue;
            break;
          case 'totalStreams':
            aValue = a.totalStreams;
            bValue = b.totalStreams;
            break;
          default:
            return 0;
        }

        if (aValue < bValue) {
          return sortOrder === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortOrder === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return result;
  }, [tracks, searchValue, selectedGenre, sortField, sortOrder]);

  // 本地分页逻辑
  const paginatedTracks = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAndSortedTracks.slice(startIndex, endIndex);
  }, [filteredAndSortedTracks, currentPage, pageSize]);

  // 分页处理
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
      setCurrentPage(1); // 改变页面大小时重置到第一页
    }
  };

  return (
    <div className="p-8">
      {/* 筛选区域 */}
      <div className="mb-8 space-y-4">
        {/* 搜索栏 */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 ">
            <Input
              placeholder={t('musicMarket.searchPlaceholder')}
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              className="s-submit-input !h-12 !rounded-lg"
              prefix={<SearchOutlined className="text-[#666] mr-2" />}
            />
          </div>
        </div>

        {/* 筛选器 */}
        <div className="flex flex-col gap-40px">
          {/* Genre Filter */}
          <div className="flex flex-col gap-20px">
            <span className="text-20px font-bold">
              {t('musicMarket.filters.genreFilter')}:
            </span>
            <div className="min-w-full overflow-x-scroll">
              <div className="flex items-center pb-20px gap-15px flex-wrap  w-2000px">
                {/* 全部类型选项 */}
                <div
                  className={`rounded-100 px-20px py-10px cursor-pointer transition-colors ${
                    !selectedGenre
                      ? 'bg-primary text-black'
                      : 'bg-[#262626] text-[#ccc] hover:bg-[#333]'
                  }`}
                  onClick={() => setSelectedGenre('')}
                >
                  {t('common.all')}
                </div>
                {/* 动态渲染音乐类型 */}
                {genres.map(genre => (
                  <div
                    key={genre.code}
                    className={`rounded-100 px-20px py-10px cursor-pointer transition-colors ${
                      selectedGenre === genre.name
                        ? 'bg-primary text-black'
                        : 'bg-[#262626] text-[#ccc] hover:bg-[#333]'
                    }`}
                    onClick={() => setSelectedGenre(genre.name)}
                  >
                    {genre.name}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Released Between */}
          <div className="flex flex-col gap-20px">
            <span className="text-20px font-bold">
              {t('musicMarket.filters.releasedBetween')}:
            </span>
            <div className="flex items-center gap-15px">
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={[t('common.startDate'), t('common.endDate')]}
                className="s-submit-datepicker !w-50 !rounded-lg"
              />
            </div>
          </div>

          {/* Shares Available Only */}
          <div className="flex gap-20px">
            <span className="text-20px font-bold">
              {t('musicMarket.filters.sharesAvailableOnly')}:
            </span>
            <Switch
              checked={sharesAvailableOnly}
              onChange={setSharesAvailableOnly}
            />
          </div>
        </div>
      </div>

      {/* 表格视图 */}
      <div className="bg-[#1a1a1a] rounded-lg overflow-hidden">
        {/* 表格头部 */}
        <div className="grid grid-cols-4 gap-4 p-4 border-b border-[#333] bg-[#222]">
          <div
            className="flex items-center text-[#999] text-12px font-semibold cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('title')}
          >
            <span>{t('musicMarket.table.title')}</span>
            {getSortIcon('title')}
          </div>
          <div
            className="flex items-center text-[#999] text-12px font-semibold cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('genre')}
          >
            <span>{t('musicMarket.table.genre')}</span>
            {getSortIcon('genre')}
          </div>
          <div
            className="flex items-center text-[#999] text-12px font-semibold cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('totalRevenue')}
          >
            <span>{t('musicMarket.table.revenue')}</span>
            {getSortIcon('totalRevenue')}
          </div>
          <div
            className="flex items-center text-[#999] text-12px font-semibold cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('totalStreams')}
          >
            <span>{t('musicMarket.table.streams')}</span>
            {getSortIcon('totalStreams')}
          </div>
        </div>

        {/* 表格内容 */}
        <div className="divide-y divide-[#333]">
          {loading ? (
            <div className="text-center py-8 text-[#999]">
              {t('messages.loading')}
            </div>
          ) : paginatedTracks.length > 0 ? (
            paginatedTracks.map(track => (
              <div
                key={track.id}
                className="grid grid-cols-4 gap-4 p-4 hover:bg-[#222] transition-colors"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-[#333] rounded mr-3 flex-shrink-0">
                    <img
                      src={track.coverArtUrl || rectangle1}
                      alt={track.title}
                      className="w-full h-full object-cover rounded"
                      onError={e => {
                        (e.target as HTMLImageElement).src =
                          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4=';
                      }}
                    />
                  </div>
                  <div>
                    <div className="text-white text-14px font-medium">
                      {track.title}
                    </div>
                    <div className="text-[#999] text-12px">
                      {track.artistStageName}
                    </div>
                  </div>
                </div>
                <div className="flex items-center text-[#656565] text-14px">
                  {track.genre}
                </div>
                <div className="flex items-center text-[#656565] text-14px">
                  {musicUtils.formatRevenue(track.totalRevenue)}
                </div>
                <div className="flex items-center text-[#656565] text-14px">
                  {musicUtils.formatStreams(track.totalStreams)}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-[#999]">
              {t('messages.noData')}
            </div>
          )}
        </div>

        {/* 分页 */}
        {!loading && filteredAndSortedTracks.length > 0 && (
          <div className="flex justify-center mt-6 pb-5">
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={filteredAndSortedTracks.length}
              onChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} 共 ${total} 条`
              }
              className="s-submit-pagination"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MusicMarket;

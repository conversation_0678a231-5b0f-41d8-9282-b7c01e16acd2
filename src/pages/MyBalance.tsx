import React, { useState } from 'react';
import { Input, Select, DatePicker, Switch } from 'antd';
import rectangle1 from '@/assets/images/rectangle-1.png';
import rectangle2 from '@/assets/images/rectangle-2.png';
import rectangle3 from '@/assets/images/rectangle-3.png';
import withdrawIcon from '@/assets/images/withdraw.png';
import addFundsIcon from '@/assets/images/add-funds.png';
import {
  SearchOutlined,
  CaretDownOutlined,
  FilterOutlined,
  ImportOutlined,
} from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 模拟音乐数据
const mockMusicData = [
  {
    id: 1,
    title: 'Summer Vibes',
    artist: 'DJ <PERSON>',
    genre: 'Pop',
    revenue: '22,345.60',
    streams: '1.2M',
    cover: rectangle1,
    releaseDate: '2024-01-15',
    sharesAvailable: true,
  },
  {
    id: 2,
    title: 'Jazz Night',
    artist: '<PERSON>',
    genre: 'Jazz',
    revenue: '9,345.30',
    streams: '856K',
    cover: rectangle2,
    releaseDate: '2024-02-20',
    sharesAvailable: false,
  },
  {
    id: 3,
    title: 'Peaceful Mind',
    artist: 'Meditation Masters',
    genre: 'Instrumental Music',
    revenue: '8,345.61',
    streams: '743K',
    cover: rectangle3,
    releaseDate: '2024-01-10',
    sharesAvailable: true,
  },
  {
    id: 4,
    title: 'City Lights',
    artist: 'Urban Beats',
    genre: 'Pop',
    revenue: '8,041.70',
    streams: '692K',
    cover: rectangle1,
    releaseDate: '2024-03-05',
    sharesAvailable: true,
  },
  {
    id: 5,
    title: 'Smooth Jazz',
    artist: 'The Jazz Collective',
    genre: 'Jazz',
    revenue: '7,441.23',
    streams: '634K',
    cover: rectangle2,
    releaseDate: '2024-02-14',
    sharesAvailable: false,
  },
  {
    id: 6,
    title: 'Electronic Dreams',
    artist: 'Synth Wave',
    genre: 'Pop',
    revenue: '5,441.58',
    streams: '521K',
    cover: rectangle3,
    releaseDate: '2024-01-28',
    sharesAvailable: true,
  },
  {
    id: 7,
    title: 'Acoustic Soul',
    artist: 'Guitar Hero',
    genre: 'Jazz',
    revenue: '5,449.36',
    streams: '498K',
    cover: rectangle1,
    releaseDate: '2024-03-12',
    sharesAvailable: false,
  },
  {
    id: 8,
    title: 'Morning Coffee',
    artist: 'Cafe Sounds',
    genre: 'Pop',
    revenue: '6,442.14',
    streams: '567K',
    cover: rectangle2,
    releaseDate: '2024-02-08',
    sharesAvailable: true,
  },
];

const MyBalance: React.FC = () => {
  const { t } = useLanguage();
  const [searchValue, setSearchValue] = useState('');
  const [selectedGenre, setSelectedGenre] = useState<string>('');
  const [dateRange, setDateRange] = useState<any>(null);
  const [sharesAvailableOnly, setSharesAvailableOnly] = useState(false);

  // 筛选后的音乐数据
  const filteredMusicData = mockMusicData.filter(music => {
    const matchesSearch =
      music.title.toLowerCase().includes(searchValue.toLowerCase()) ||
      music.artist.toLowerCase().includes(searchValue.toLowerCase());
    const matchesGenre = !selectedGenre || music.genre === selectedGenre;
    const matchesShares = !sharesAvailableOnly || music.sharesAvailable;

    return matchesSearch && matchesGenre && matchesShares;
  });

  return (
    <div className="p-8">
      <div className="flex text-black bg-[#FFECDA] rounded-6px p-50px justify-between mb-50px">
        <div className="flex flex-col text-32px gap-20px">
          <div className=" font-bold ">Account Balance</div>
          <div className=" text-64px font-bold text-primary">2,000.00</div>
        </div>
        <div className="flex flex-col gap-20px text-22px">
          <div>
            <div>Committed to Bid</div>
            <div className="text-primary text-32px font-bold">1,860.00</div>
          </div>
          <div>
            <div>Available Balance</div>
            <div className="text-primary text-32px font-bold">140.00</div>
          </div>
        </div>
        <div className="flex w-386px flex-col gap-20px">
          <div className="h-63px flex items-center justify-center text-center text-20px   text-primary border-2 border-solid border-primary rounded-6px text-primary px-4 py-2">
            <img
              src={withdrawIcon}
              alt="withdraw"
              className="w-26px h-26px mr-10px"
            />
            Withdraw
          </div>
          <div className="h-63px flex items-center justify-center text-center text-20px bg-primary text-black  border-2 border-solid border-primary rounded-6px ">
            <img
              src={addFundsIcon}
              alt="add-funds"
              className="w-26px h-26px mr-10px"
            />
            Add Funds
          </div>
        </div>
      </div>
      {/* 筛选区域 */}
      <div className="mb-8 space-y-4">
        {/* 筛选器 */}
        <div className="flex flex-col gap-40px">
          {/* Genre Filter */}
          <div className="flex flex-col gap-20px">
            <span className=" text-20px font-bold ">
              {t('musicMarket.filters.genreFilter')}:
            </span>
            <div className="flex items-center gap-15px flex-wrap">
              {/* 循环渲染40个span */}
              {Array.from({ length: 20 }).map((_, idx) => (
                <div
                  key={idx}
                  className="rounded-100 bg-[#262626]   text-[#ccc] px-20px py-10px"
                >
                  {t(
                    `musicMarket.genres.${['pop', 'jazz', 'instrumental', 'electronic', 'acoustic'][Math.floor(Math.random() * 5)]}`
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Released Between */}
          <div className="flex flex-col gap-20px">
            <span className=" text-20px font-bold ">
              {t('musicMarket.filters.releasedBetween')}:
            </span>
            <div className="flex items-center gap-15px">
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                className="bg-[#1a1a1a] border-[#333]"
              />
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                className="bg-[#1a1a1a] border-[#333]"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 表格视图 */}
      <div className="bg-[#1a1a1a] rounded-lg overflow-hidden">
        {/* 表格头部 */}
        <div className="grid grid-cols-4 gap-4 p-4 border-b border-[#333] bg-[#222]">
          <div className="flex items-center text-[#999] text-12px font-semibold">
            <span>{t('musicMarket.table.title')}</span>
          </div>
          <div className="flex items-center text-[#999] text-12px font-semibold">
            <span>{t('musicMarket.table.genre')}</span>
            <CaretDownOutlined className="ml-1 text-10px" />
          </div>
          <div className="flex items-center text-[#999] text-12px font-semibold">
            <span>{t('musicMarket.table.revenue')}</span>
            <CaretDownOutlined className="ml-1 text-10px" />
          </div>
          <div className="flex items-center text-[#999] text-12px font-semibold">
            <span>{t('musicMarket.table.streams')}</span>
            <CaretDownOutlined className="ml-1 text-10px" />
          </div>
        </div>

        {/* 表格内容 */}
        <div className="divide-y divide-[#333]">
          {filteredMusicData.map(music => (
            <div
              key={music.id}
              className="grid grid-cols-4 gap-4 p-4 hover:bg-[#222] transition-colors"
            >
              <div className="flex items-center">
                <div className="w-12 h-12 bg-[#333] rounded mr-3 flex-shrink-0">
                  <img
                    src={music.cover}
                    alt={music.title}
                    className="w-full h-full object-cover rounded"
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4=';
                    }}
                  />
                </div>
                <div>
                  <div className="text-white text-14px font-medium">
                    {music.title}
                  </div>
                  <div className="text-[#999] text-12px">{music.artist}</div>
                </div>
              </div>
              <div className="flex items-center text-[#656565] text-14px">
                {music.genre}
              </div>
              <div className="flex items-center text-[#656565] text-14px">
                {music.revenue}
              </div>
              <div className="flex items-center text-[#656565] text-14px">
                {music.streams}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MyBalance;

import React, { useState, useEffect } from 'react';
import { Form, message, ConfigProvider } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import Header from '@/components/Header';

import UserNameInput from './coms/UserNameInput';
import EmailCodeInput from './coms/EmailCodeInput';
import PasswordInput from './coms/PasswordInput';
import type { LoginFormData, ExtendedLoginRequest } from '@/types';
import SelectLanguage from '@/components/SelectLanguage';
import { useAuthStore } from '@/store/authStore';
import { useNavigate, useLocation } from 'react-router-dom';
import { api } from '@/services';

import logoIcon from '@/assets/logo.svg';

type LoginStep = 'userName' | 'auth';
type AuthMethod = 'email' | 'password';

const Login: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const [currentStep, setCurrentStep] = useState<LoginStep>('userName');
  const [authMethod, setAuthMethod] = useState<AuthMethod>('password');
  const [form] = Form.useForm<LoginFormData>();
  useEffect(() => {
    form.setFieldsValue({
      email: '',
      password: '',
    });
  }, []);

  // 用户名提交
  const handleUsernameSubmit = (values: { email: string }) => {
    form.setFieldsValue({ email: values.email });

    setCurrentStep('auth');
  };

  // 密码登录提交
  const handlePasswordSubmit = (values: Pick<LoginFormData, 'password'>) => {
    form.setFieldsValue({ password: values.password });

    handleLogin();
  };

  // 邮箱验证码登录提交
  const handleEmailCodeSubmit = (values: Pick<LoginFormData, 'emailCode'>) => {
    form.setFieldsValue({ emailCode: values.emailCode });

    handleLogin();
  };

  const { login } = useAuthStore();
  // 统一的登录处理函数
  const handleLogin = async () => {
    const loginData = form.getFieldsValue(true);
    console.log('loginData----', loginData);

    // 构建登录请求数据
    const data: ExtendedLoginRequest = {
      username: loginData.email,
    };

    // 根据登录方式设置不同字段
    if (authMethod === 'password') {
      data.password = loginData.password;
    } else {
      data.verificationCode = loginData.emailCode;
    }

    try {
      // 调用实际的登录API
      await login(data);

      message.success(t('auth.login.success'));

      // 登录成功后跳转
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    } catch (error: any) {
      console.error('登录失败:', error);
      // 显示具体的错误信息
      const errorMessage = error.message || t('auth.login.error');
      message.error(errorMessage);
    }
  };

  // 切换到邮箱验证码登录
  const handleSwitchToEmailCode = async () => {
    setAuthMethod('email');
    form.setFieldsValue({ password: '', emailCode: '' });

    console.log('清空密码和邮箱验证码');

    // 自动发送验证码
    try {
      const email = getEmail();
      if (email) {
        const result = await api.auth.sendLoginOtp({ recipient: email });

        if (result.code === 200 && result.body.trueOrFalse) {
          message.success(t('auth.login.otpSent'));
        } else {
          // 根据错误代码提供更具体的错误信息
          let errorMessage = t('auth.login.otpSendFailed');
          switch (result.code) {
            case 400:
              errorMessage = '邮箱格式不正确';
              break;
            case 404:
              errorMessage = '该邮箱未注册';
              break;
            case 429:
              errorMessage = '发送频率过快，请稍后重试';
              break;
            case 500:
              errorMessage = '服务器错误，请稍后重试';
              break;
            default:
              errorMessage = result.message || t('auth.login.otpSendFailed');
          }
          message.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      message.error(t('auth.login.otpSendFailed'));
    }
  };

  // 切换到密码登录
  const handleSwitchToPassword = () => {
    setAuthMethod('password');
    form.setFieldsValue({ password: '', emailCode: '' });
    console.log('清空密码和邮箱验证码');
  };
  const getEmail = () => {
    const { email } = form.getFieldsValue(true);
    return email;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'userName':
        return <UserNameInput form={form} onSubmit={handleUsernameSubmit} />;
      case 'auth':
        return authMethod === 'email' ? (
          <EmailCodeInput
            form={form}
            onSubmit={handleEmailCodeSubmit}
            onSwitchToPassword={handleSwitchToPassword}
            userEmail={getEmail()}
          />
        ) : (
          <PasswordInput
            form={form}
            onSubmit={handlePasswordSubmit}
            onSwitchToEmailCode={handleSwitchToEmailCode}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelFontSize: 12,
          },
        },
      }}
    >
      <div className="relative pt-[120px] min-h-screen flex items-center justify-center bg-black text-[#656565] yue-layout-content">
        <Header />
        <div className="absolute inset-0 bg-page-bg" />

        <div className="relative z-10 h-520px flex flex-col items-center">
          <div className="mb-8">
            <img
              src={logoIcon}
              alt="Yuequ Logo"
              className="h-[60px] w-[69px]"
            />
          </div>

          <h1 className="font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold">
            {t('auth.login.title')}
          </h1>

          <div className="w-[496px]">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={(changedValues, allValues) => {
                // console.log('Form values changed:', changedValues, allValues);
              }}
            >
              {renderStepContent()}
            </Form>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Login;

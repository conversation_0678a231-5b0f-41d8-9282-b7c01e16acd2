import React from 'react';
import { Form } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types';
import FormActions from './FormActions';
import SendCodeInput from '@/components/SendCodeInput';
import { api } from '@/services';

interface EmailCodeInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'emailCode'>) => void;
  onSwitchToPassword: () => void;
  userEmail?: string; // 添加用户邮箱显示
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  form,
  onSubmit,
  onSwitchToPassword,
  userEmail,
}) => {
  const { t } = useTranslation();
  const handleSubmit = () => {
    if (form && onSubmit) {
      form
        .validateFields(['emailCode'])
        .then(values => {
          onSubmit(values);
        })
        .catch(errorInfo => {
          console.log('Email code validation failed:', errorInfo);
        });
    }
  };

  // 重新发送验证码
  const handleResendCode = async () => {
    try {
      const email = form.getFieldValue('email');
      if (!email) {
        throw new Error('邮箱地址不能为空');
      }

      console.log('重新发送验证码到邮箱:', email);

      // 调用发送登录验证码API
      const result = await api.auth.sendLoginOtp({ recipient: email });

      if (result.code === 200 && result.body.trueOrFalse) {
        console.log('验证码发送成功');
      } else {
        // 根据错误代码提供更具体的错误信息
        let errorMessage = '验证码发送失败';
        switch (result.code) {
          case 400:
            errorMessage = '邮箱格式不正确';
            break;
          case 404:
            errorMessage = '该邮箱未注册';
            break;
          case 429:
            errorMessage = '发送频率过快，请稍后重试';
            break;
          case 500:
            errorMessage = '服务器错误，请稍后重试';
            break;
          default:
            errorMessage = result.message || '验证码发送失败，请重试';
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error; // 重新抛出错误，让组件处理
    }
  };

  return (
    <>
      <Form.Item
        label={t('auth.login.form.emailCodeLabel')}
        name="emailCode"
        rules={[{ required: true, message: t('common.form.required') }]}
        validateTrigger={['onChange', 'onBlur']}
        className="mb-6"
      >
        <SendCodeInput onSendCode={handleResendCode} />
      </Form.Item>

      <FormActions
        buttonText={t('auth.login.form.submit')}
        onSubmit={handleSubmit}
      />
      <div className="mt-6 text-center">
        <span
          className="cursor-pointer text-[12px] text-[#ff5e13] hover:underline"
          onClick={onSwitchToPassword}
        >
          {t('auth.login.form.loginWithPassword')}
        </span>
      </div>
    </>
  );
};

export default EmailCodeInput;

import React, { useEffect, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useDate } from '@/hooks/useDate';
import { DateUtils } from '@/utils/dateUtils';
import { Button, Divider, Layout as AntLayout } from 'antd';
import Header from '@/components/Header';
import homeBg from '@/assets/images/homebg.png';
import rectangle1 from '@/assets/images/rectangle-1.png';
import TrackDetailModal from '@/components/TrackDetailModal';
import ArtistModal from '@/components/ArtistModal/ArtistModal';
import { api } from '@/services';
import type { TracksByGenreResponse } from '@/types/api';

const { Header: AntHeader } = AntLayout;

const Home: React.FC = () => {
  const { t, language: currentLanguage } = useLanguage();
  const { formatByLanguage } = useDate();
  const [musicCategories, setMusicCategories] = useState<any[]>([]);
  // 获取音乐分类数据
  const fetchMusicCategories = async () => {
    const response = await api.music.getTracksByGenre(10);
    const list = [];
    if (response.code === 200) {
      for (const key in response.body) {
        if (Object.prototype.hasOwnProperty.call(response.body, key)) {
          const element = response.body[key];
          list.push({
            id: key,
            name: key,
            tracks: element,
          });
        }
      }
      setMusicCategories(list);
    }
  };
  // 获取每周播放量数据
  const [weeklyStreamingData, setWeeklyStreamingData] = useState<any[]>([]);
  const fetchWeeklyStreamingData = async () => {
    const response = await api.music.getTracksByStreams(1, 7);
    if (response.code === 200) {
      setWeeklyStreamingData(response.body?.pageView?.result);
    }
  };
  // 获取每周收入数据
  const [weeklyRevenueData, setWeeklyRevenueData] = useState<any[]>([]);
  const fetchWeeklyRevenueData = async () => {
    const response = await api.music.getTracksByRevenue(1, 7);
    if (response.code === 200) {
      setWeeklyRevenueData(response.body?.pageView?.result);
    }
  };
  useEffect(() => {
    fetchMusicCategories();
    fetchWeeklyStreamingData();
    fetchWeeklyRevenueData();
  }, []);

  const [trackDetailModalOpen, setTrackDetailModalOpen] = useState(false);
  const [artistModalOpen, setArtistModalOpen] = useState(false);

  // 打开音乐详情弹窗
  const handleOpenTrackDetailModal = (track: any) => {
    setTrackDetailModalOpen(true);
    // console.log('track----', track);
  };
  const [artistId, setArtistId] = useState('');

  // 打开艺术家详情弹窗
  const handleOpenArtistModal = (id: string) => {
    setArtistId(id);
    setArtistModalOpen(true);
  };
  return (
    <div
      className="min-h-screen bg-cover bg-#d3d3d3 bg-center bg-no-repeat yue-layout-content"
      style={{
        backgroundImage: `url(${homeBg})`,
      }}
    >
      <div className="max-w-[1920px] mx-auto">
        <AntHeader className="border-b-0 p-0 h-auto !bg-transparent">
          <Header fixed={false} bgColor="bg-transparent" />
        </AntHeader>
        <div className="flex  ">
          {/* 主内容区域 */}
          <div className="flex-1 p-8">
            {/* 音乐分类网格 */}
            <div className="grid grid-cols-2 min-[1600px]:grid-cols-4 min-[1530px]:grid-cols-3  gap-8 gap-y-10">
              {musicCategories.map(category => (
                <div
                  key={category.id}
                  className="space-y-4 h-421px bg-[#0D0D0D] backdrop-blur-md rounded-20px p-5 "
                >
                  {/* 分类标题 */}
                  <div className="flex  justify-between">
                    <h2 className="text-primary text-3xl  font-bold cursor-pointer">
                      {category.name}
                    </h2>
                    <span>{t('common.seeAll')}</span>
                  </div>
                  <div
                    className="flex gap-4 items-center cursor-pointer"
                    onClick={() =>
                      handleOpenTrackDetailModal(category.tracks[0])
                    }
                  >
                    <img
                      src={category.tracks[0]?.coverArtUrl || rectangle1}
                      alt="cover"
                      className="w-105px h-105px rounded-10px"
                      onError={e => {
                        const target = e.target as HTMLImageElement;
                        if (target.src !== rectangle1) {
                          target.src = rectangle1;
                        }
                      }}
                    />
                    <div className="flex flex-col gap-16px ">
                      <div className="text-white text-16px hover:text-primary font-bold">
                        {category.tracks[0].title}
                      </div>
                      <div className="text-label">
                        {category.tracks[0].artistStageName}
                      </div>
                    </div>
                  </div>

                  {/* 音乐列表 */}
                  <div className="space-y-3">
                    {category.tracks.slice(0, 3).map(track => (
                      <div
                        key={track.id}
                        className="flex items-center space-3 group cursor-pointer"
                        onClick={() => handleOpenTrackDetailModal(track)}
                      >
                        {/* 音乐信息 */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-white text-14px font-medium truncate hover:text-primary cursor-pointer transition-colors">
                            {track.title}
                          </h3>
                          {track.artistStageName && (
                            <p className="text-[#999] text-12px truncate mt-1">
                              {track.artistStageName}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 右侧边栏 */}
          <div className="min-w-472px p-8">
            <div className="grid grid-cols-1 grid-rows-2  gap-8 gap-y-10">
              <div className=" bg-[#0d0d0d] px-5 pb-5 rounded-20px h-421px overflow-hidden relative">
                <div className="flex h-58px leading-58px  justify-between ">
                  <span className="text-primary  font-bold">
                    {t('common.weeklyRevenueChart')}
                  </span>
                  <span>{t('common.seeAll')}</span>
                </div>
                <Divider className="bg-[#262626] m-0 " />

                <div className="">
                  <div className=" my-10px">
                    <div className="grid  grid-cols-[15px_1fr_60px_60px] gap-10px text-right  text-label text-12px items-center">
                      <div className="text-12px"></div>
                      <div className="text-left">{t('common.title')}</div>
                      <div className="">{t('common.genre')}</div>
                      <div className="">{t('common.revenue')}</div>
                    </div>
                  </div>
                  <div className="">
                    {weeklyRevenueData.map((item, index) => (
                      <div
                        className="grid  grid-cols-[15px_1fr_60px_60px] gap-10px text-right text-white h-45px items-center text-12px "
                        key={index}
                      >
                        <div className="text-label">{index + 1}</div>
                        <div className="text-left truncate" title={item.title}>
                          {item.title}
                        </div>
                        <div className="truncate" title={item.genre}>
                          {item.genre}
                        </div>
                        <div className="truncate" title={item.periodicRevenue}>
                          {item.periodicRevenue}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="text-label text-10px text-center absolute bottom-8px left-30px">
                  {t('common.lastUpdated')}: {formatByLanguage(Date.now())}
                </div>
              </div>

              <div className=" bg-[#0d0d0d] px-5 pb-5 rounded-20px h-421px overflow-hidden relative">
                <div className="flex h-58px leading-58px  justify-between   ">
                  <span className="text-primary  font-bold">
                    {t('common.weeklyStreamingChart')}
                  </span>
                  <span>{t('common.seeAll')}</span>
                </div>
                <Divider className="bg-[#262626] m-0" />
                <div className="">
                  <div className=" my-10px">
                    <div className="grid  grid-cols-[15px_1fr_60px_60px] gap-10px text-right  text-label text-12px items-center">
                      <div className="text-12px"></div>
                      <div className="text-left">{t('common.title')}</div>
                      <div className="">{t('common.genre')}</div>
                      <div className="">{t('common.streams')}</div>
                    </div>
                  </div>
                  <div className="">
                    {weeklyStreamingData.map((item, index) => (
                      <div
                        className="grid  grid-cols-[15px_1fr_60px_60px] gap-10px text-right text-white h-45px items-center text-12px "
                        key={index}
                      >
                        <div className="text-label">{index + 1}</div>
                        <div className="text-left truncate" title={item.title}>
                          {item.title}
                        </div>
                        <div className="truncate" title={item.genre}>
                          {item.genre}
                        </div>
                        <div className="truncate" title={item.periodicStreams}>
                          {item.periodicStreams}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className=" text-label text-10px absolute bottom-8px left-30px">
                  {t('common.lastUpdated')}: {formatByLanguage(Date.now())}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <TrackDetailModal
        open={trackDetailModalOpen}
        onArtistModalOpen={handleOpenArtistModal}
        onClose={() => setTrackDetailModalOpen(false)}
        id="test-track-id"
      />
      <ArtistModal
        open={artistModalOpen}
        onClose={() => setArtistModalOpen(false)}
        artistId={artistId}
      />
    </div>
  );
};

export default Home;

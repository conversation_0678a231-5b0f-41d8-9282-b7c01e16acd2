import React, { useState, useEffect } from 'react';
import {
  ConfigProvider,
  Card,
  Form,
  Input,
  Button,
  Typography,
  message,
  Row,
  Col,
  Radio,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { api } from '@/services';
import type { DefaultRole } from '@/types/api';
import FormButton from './coms/FormButton';
import CountrySelector from '@/components/CountryStateSelector/CountrySelector';
import StateSelector from '@/components/CountryStateSelector/StateSelector';
import PhoneInputFormItem from '@/components/PhoneInputFormItem/PhoneInputFormItem';

const PersonalInfo: React.FC = () => {
  const navigate = useNavigate();
  const { t, isEnUS } = useLanguage();
  const { formData, updateFormData } = useRegisterStore();
  const [form] = Form.useForm();
  const [defaultRoles, setDefaultRoles] = useState<DefaultRole[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取默认角色列表
  useEffect(() => {
    const fetchDefaultRoles = async () => {
      try {
        const response = await api.meta.getDefaultRoles();

        if (response.code === 200) {
          setDefaultRoles(response.body || []);
        } else {
          message.error(t('common.messages.loadRolesFailed'));
        }
      } catch (error: any) {
        console.error('获取角色列表失败:', error);
        message.error(error?.message || t('common.messages.loadRolesFailed'));
      }
    };

    fetchDefaultRoles();
  }, [t]);

  const onFinish = (values: any) => {
    console.log('form values----', form.getFieldsValue());

    updateFormData(values);
    // message.success(t('auth.register.step3.messages.success'));
    navigate('/register/password');
  };

  // 初始化表单值
  React.useEffect(() => {
    const initialValues = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone,
      address: formData.address,
      // 处理国家和地区的初始值
      country:
        formData.country ||
        (formData.countryRegion && Array.isArray(formData.countryRegion)
          ? formData.countryRegion[0]
          : undefined),
      state:
        formData.state ||
        formData.state ||
        (formData.countryRegion && Array.isArray(formData.countryRegion)
          ? formData.countryRegion[1]
          : undefined),
      postalZipCode: formData.postalZipCode,
    };
    form.setFieldsValue(initialValues);
  }, [formData, form]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    }
  }, [formData.email, formData.alias, navigate]);

  // 生成姓名输入框, 页面语言是中文，姓在前名在后，否则名在前姓在后
  const generateNameFormItem = () => {
    const rules = [
      {
        min: 1,
        message: t('auth.register.step3.validation.nameLength', {
          min: 1,
          max: 12,
        }),
      },
      {
        max: 12,
        message: t('auth.register.step3.validation.nameLength', {
          min: 1,
          max: 12,
        }),
      },
      {
        pattern: /^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,
        message: t('auth.register.step3.validation.namePattern'), // '姓名只能包含中英文、空格和常用符号'
      },
    ];

    const nameFormItem = (
      <Form.Item
        label={t('auth.register.step3.form.firstName')}
        name="firstName"
        rules={[
          {
            required: true,
            message: t('auth.register.step3.form.firstNameRequired'),
          },
          ...rules,
        ]}
        key="firstName"
      >
        <Input
          placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
          className="s-form-input"
          size="large"
        />
      </Form.Item>
    );
    const lastNameFormItem = (
      <Form.Item
        label={t('auth.register.step3.form.lastName')}
        name="lastName"
        rules={[
          {
            required: true,
            message: t('auth.register.step3.form.lastNameRequired'),
          },
          ...rules,
        ]}
        key="lastName"
      >
        <Input
          placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
          className="s-form-input"
          size="large"
        />
      </Form.Item>
    );
    if (isEnUS) {
      return [nameFormItem, lastNameFormItem];
    } else {
      return [lastNameFormItem, nameFormItem];
    }
  };

  return (
    <>
      <h1 className="font-arial mb-2 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.step3.title')}
      </h1>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          defaultRole: 'account.role.investor',
        }}
        autoComplete="off"
      >
        {generateNameFormItem()}
        <PhoneInputFormItem />

        <Form.Item label={t('auth.register.step3.form.address')} name="address">
          <Input
            placeholder={t('auth.register.step3.form.addressPlaceholder')}
            className="s-form-input"
            size="large"
          />
        </Form.Item>
        <Form.Item label={t('auth.register.step3.form.city')} name="city">
          <Input
            placeholder={t('auth.register.step3.form.cityPlaceholder')}
            className="s-form-input"
            size="large"
          />
        </Form.Item>

        <Form.Item label={t('auth.register.step3.form.country')} name="country">
          <CountrySelector
            placeholder={t('common.form.selectCountry')}
            size="large"
          />
        </Form.Item>

        <Form.Item label={t('common.form.selectState')} name="state">
          <StateSelector
            form={form}
            placeholder={t('common.form.selectState')}
            size="large"
          />
        </Form.Item>

        <Form.Item
          label={t('auth.register.step3.form.postalZipCode')}
          name="postalZipCode"
        >
          <Input
            placeholder={t('auth.register.step3.form.postalZipCodePlaceholder')}
            className="s-form-input"
            size="large"
          />
        </Form.Item>
        <Form.Item label={t('common.defaultRole')} name="defaultRole">
          <Radio.Group buttonStyle="solid" size="large">
            {defaultRoles.map(role => (
              <Radio key={role.code} value={role.code}>
                {role.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <FormButton text={t('auth.register.step3.buttons.next')} />
      </Form>
    </>
  );
};

export default PersonalInfo;

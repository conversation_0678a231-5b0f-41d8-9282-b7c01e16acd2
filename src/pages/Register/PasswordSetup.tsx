import React, { useMemo } from 'react';
import { Form, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { useAuthStore } from '@/store';
import { api } from '@/services';
import type { SignupRequest, UserProfile } from '@/types/api';
import PasswordForm, { type PasswordFormData } from '@/components/PasswordForm';
import FormButton from './coms/FormButton';
import { getTimezone } from '@/components/CountryStateSelector/timezoneMap';

const PasswordSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, clearFormData } = useRegisterStore();
  const { setAuthData } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [password, setPassword] = React.useState('');
  const [agreeTerms, setAgreeTerms] = React.useState(false);

  const onPasswordChange = (value: string) => {
    setPassword(value);
  };

  const onFinish = async (values: PasswordFormData) => {
    if (!agreeTerms) {
      message.error(t('auth.register.step4.form.agreeTermsRequired'));
      return;
    }

    // 构建用户资料对象
    const profile: UserProfile = {
      firstName: formData.firstName || '',
      lastName: formData.lastName || '',
      addressLine1: formData.addressLine1 || formData.address || '',
      addressLine2: formData.addressLine2 || formData.city || '',
      stateProvince: formData.stateProvince || formData.state || '',
      countryCode: formData.countryCode || formData.country || '',
      postalZipCode: formData.postalZipCode || '',
      avatarUrl: formData.avatarUrl || '',
      stageName: formData.stageName || '',
      bio: formData.bio || '',
    };

    // 构建注册请求对象
    const signupData: SignupRequest = {
      username: formData.email || '',
      password: values.password,
      alias: formData.alias || '',
      profile,
      defaultRoleId: formData.defaultRole || 'account.role.investor',
    };

    console.log('注册数据:', signupData);

    setLoading(true);
    try {
      const response = await api.auth.signup(signupData);

      if (response.code === 200 && response.body.token) {
        // 注册成功后自动登录
        const { token, accountId, alias, displayName, avatarUrl, roles } =
          response.body;

        // 构建用户对象
        const user = {
          accountId,
          email: formData.email || '',
          alias,
          firstName: formData.firstName || '',
          lastName: formData.lastName || '',
          displayName,
          avatarUrl: formData.avatarUrl || null,
          stageName: formData.stageName,
          roles: roles.map(role => ({
            id: role.id,
            name: role.name,
            realm: role.realm,
          })),
        };

        // 设置认证数据
        setAuthData(token, user);

        message.success(t('auth.register.success'));
        clearFormData(); // 清空注册数据
        navigate('/');
      } else {
        message.error(response.message || t('auth.register.error'));
      }
    } catch (error: any) {
      console.error('注册失败:', error);
      message.error(error?.message || t('auth.register.error'));
    } finally {
      setLoading(false);
    }
  };

  const disabledButton = useMemo(() => {
    return !agreeTerms || !password.trim();
  }, [agreeTerms, password]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    } else if (!formData.firstName || !formData.lastName) {
      message.warning(t('auth.register.step4.messages.personalInfoRequired'));
      navigate('/register/personal-info');
    }
  }, [formData, navigate, t]);

  return (
    <>
      <h1 className="font-arial mb-13 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.step4.title')}
      </h1>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        size="large"
      >
        <PasswordForm
          form={form}
          password={password}
          onPasswordChange={onPasswordChange}
          agreeTerms={agreeTerms}
          onAgreeTermsChange={setAgreeTerms}
        />

        <FormButton loading={loading} disabled={disabledButton}>
          {t('auth.register.step4.form.signUp')}
        </FormButton>
      </Form>
    </>
  );
};

export default PasswordSetup;

import { ApiService } from './request';
import type {
  SignupRequest,
  LoginRequest,
  OtpLoginRequest,
  SendOtpRequest,
  VerifyOtpRequest,
  AuthResponse,
  BooleanResponse,
  AliasCheckResponse,
  ApiResponse,
} from '@/types/api';
import { API_ENDPOINTS } from '@/types/api';

/**
 * 认证相关 API 服务
 */
export const authApi = {
  /**
   * 用户注册
   * @param params 注册参数
   * @returns 认证响应
   */
  async signup(params: SignupRequest): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SIGNUP, params);
  },

  /**
   * 用户登录
   * @param params 登录参数
   * @returns 认证响应
   */
  async login(params: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.LOGIN, params);
  },

  /**
   * 验证码登录
   * @param params 验证码登录参数
   * @returns 认证响应
   */
  async loginWithOtp(
    params: OtpLoginRequest
  ): Promise<ApiResponse<AuthResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.LOGIN_OTP, params);
  },

  /**
   * 发送注册验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendSignupOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_SIGNUP_OTP, params);
  },

  /**
   * 发送登录验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendLoginOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_LOGIN_OTP, params);
  },

  /**
   * 发送更改用户名验证码
   * @param params 发送验证码参数
   * @returns 布尔响应
   */
  async sendChangeUsernameOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.post(API_ENDPOINTS.AUTH.SEND_CHANGE_USERNAME_OTP, params);
  },

  /**
   * 验证OTP验证码
   * @param params 验证参数
   * @returns 布尔响应
   */
  async verifyOtp(
    params: VerifyOtpRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.AUTH.VERIFY_OTP, params);
  },

  /**
   * 检查用户名可用性
   * @param username 用户名
   * @returns 布尔响应
   */
  async checkUsername(username: string): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.AUTH.CHECK_USERNAME}?username=${encodeURIComponent(username)}`
    );
  },

  /**
   * 检查别名可用性
   * @param alias 别名
   * @returns 别名检查响应
   */
  async checkAlias(alias: string): Promise<ApiResponse<AliasCheckResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.AUTH.CHECK_ALIAS}?alias=${encodeURIComponent(alias)}`
    );
  },
};

/**
 * 认证工具函数
 */
export const authUtils = {};

// 导出默认对象以保持向后兼容
export default authApi;

// 导出基础服务
export { ApiService, default as apiClient, loadingManager } from './request';

// 导出认证相关服务
import { authApi, authUtils, default as auth } from './auth';
export { authApi, authUtils, auth };

// 导出用户相关服务
import { userApi, userUtils, default as user } from './user';
export { userApi, userUtils, user };

// 导出元数据相关服务
import { metaApi, metaUtils, default as meta } from './meta';
export { metaApi, metaUtils, meta };

// 导出音乐相关服务
import { musicApi, musicUtils, default as music } from './music';
export { musicApi, musicUtils, music };

// 组装新的API对象，推荐使用这种方式
export const api = {
  auth: authApi,
  user: userApi,
  meta: metaApi,
  music: musicApi,
};

// 导出所有API类型
export * from '@/types/api';

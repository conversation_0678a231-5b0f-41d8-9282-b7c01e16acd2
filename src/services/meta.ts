import { ApiService } from './request';
import type {
  Country,
  Subdivision,
  DefaultRole,
  ApiResponse,
} from '@/types/api';
import { API_ENDPOINTS } from '@/types/api';

/**
 * 元数据相关 API 服务
 */
export const metaApi = {
  /**
   * 获取所有国家列表
   * @returns 国家列表
   */
  async getCountries(): Promise<ApiResponse<Country[]>> {
    return ApiService.get(API_ENDPOINTS.META.COUNTRIES);
  },

  /**
   * 根据国家代码获取国家信息
   * @param countryCode 国家代码（Alpha2）
   * @returns 国家信息
   */
  async getCountryByCode(countryCode: string): Promise<ApiResponse<Country>> {
    return ApiService.get(`${API_ENDPOINTS.META.COUNTRY_BY_CODE}/${countryCode}`);
  },

  /**
   * 根据国家代码获取州/省列表
   * @param countryCode 国家代码（Alpha2）
   * @returns 州/省列表
   */
  async getSubdivisions(countryCode: string): Promise<ApiResponse<Subdivision[]>> {
    return ApiService.get(`${API_ENDPOINTS.META.SUBDIVISIONS}/${countryCode}/subdivisions`);
  },

  /**
   * 根据国家代码和州/省代码获取州/省信息
   * @param countryCode 国家代码（Alpha2）
   * @param subdivisionCode 州/省代码（ISO2）
   * @returns 州/省信息
   */
  async getSubdivisionByCode(
    countryCode: string,
    subdivisionCode: string
  ): Promise<ApiResponse<Subdivision>> {
    return ApiService.get(
      `${API_ENDPOINTS.META.SUBDIVISION_BY_CODE}/${countryCode}/subdivisions/${subdivisionCode}`
    );
  },

  /**
   * 获取默认角色列表
   * @returns 默认角色列表
   */
  async getDefaultRoles(): Promise<ApiResponse<DefaultRole[]>> {
    return ApiService.get(API_ENDPOINTS.META.DEFAULT_ROLES);
  },

  /**
   * 根据角色ID获取默认角色信息
   * @param roleId 角色ID
   * @returns 默认角色信息
   */
  async getDefaultRoleById(roleId: string): Promise<ApiResponse<DefaultRole>> {
    return ApiService.get(`${API_ENDPOINTS.META.DEFAULT_ROLE_BY_ID}/${roleId}`);
  },
};

/**
 * 元数据工具函数
 */
export const metaUtils = {
  /**
   * 检查国家是否支持州/省列表
   * @param countryCode 国家代码
   * @returns 是否支持
   */
  isSupportedCountryForSubdivisions(countryCode: string): boolean {
    const supportedCountries = ['US', 'CA', 'CN'];
    return supportedCountries.includes(countryCode.toUpperCase());
  },

  /**
   * 格式化国家显示名称
   * @param country 国家对象
   * @returns 格式化后的显示名称
   */
  formatCountryDisplayName(country: Country): string {
    return `${country.name} (${country.code})`;
  },

  /**
   * 格式化州/省显示名称
   * @param subdivision 州/省对象
   * @returns 格式化后的显示名称
   */
  formatSubdivisionDisplayName(subdivision: Subdivision): string {
    return `${subdivision.name} (${subdivision.code})`;
  },

  /**
   * 根据角色代码获取角色显示名称
   * @param roleCode 角色代码
   * @returns 角色显示名称
   */
  getRoleDisplayName(roleCode: string): string {
    const roleMap: Record<string, string> = {
      'account.role.investor': 'Investor',
      'account.role.artist': 'Artist',
    };
    return roleMap[roleCode] || roleCode;
  },
};

// 导出默认对象以保持向后兼容
export default metaApi;

import React, { useState, useEffect } from 'react';
import { Select, message } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { api } from '@/services';
import type { Country } from '@/types/api';
// import styles from './CountryStateSelector.module.css';

interface CountrySelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}
const CountrySelector: React.FC<CountrySelectorProps> = ({
  placeholder,
  size = 'large',
  value,
  onChange,
  className,
}) => {
  const { t } = useLanguage();
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取国家列表
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setLoading(true);
        const response = await api.meta.getCountries();

        if (response.code === 200) {
          setCountries(response.body || []);
        } else {
          message.error(t('common.messages.loadCountriesFailed'));
        }
      } catch (error: any) {
        console.error('获取国家列表失败:', error);
        message.error(
          error?.message || t('common.messages.loadCountriesFailed')
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCountries();
  }, [t]);

  const options = countries.map(country => ({
    label: country.name,
    value: country.code,
  }));
  return (
    // <div className="s-form-selector-hover">

    <Select
      placeholder={placeholder || t('common.form.selectCountry')}
      size={size}
      className={
        className ||
        `s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px `
      }
      options={options}
      value={value}
      onChange={onChange}
      loading={loading}
      showSearch
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
    />
    // </div>
  );
};

export default CountrySelector;

import React, { useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store';

interface RequireAuthProps {
  children: React.ReactElement;
}

/**
 * 路由守卫组件
 * 用于保护需要认证的路由
 * 如果用户未登录，会重定向到登录页面
 */
const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuthStore();

  // 如果未登录，重定向到登录页面
  if (!isAuthenticated) {
    // 重定向到登录页面，并保存当前路径以便登录后返回
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  // 用户已登录，渲染子组件
  return <>{children}</>;
};

export default RequireAuth;

import React from 'react';
import PasswordForm from '@/components/PasswordForm';
import { Form, message } from 'antd';
import EditButton from './EditButton';
import { api } from '@/services';

import { useLanguage } from '@/hooks/useLanguage';

interface PasswordProps {
  visible: boolean;
  onClose: () => void;
}

const EditProfilePassword: React.FC<PasswordProps> = ({ visible, onClose }) => {
  const { t } = useLanguage();
  const [password, setPassword] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [passwordForm] = Form.useForm();

  if (!visible) return null;

  const onPasswordChange = (value: string) => {
    setPassword(value);
  };

  // 密码表单提交处理
  const onFinish = async (values: any) => {
    try {
      setLoading(true);

      // 调用修改密码API
      const response = await api.user.changePassword({
        password: values.password,
      });

      if (response.code === 200 && response.body.trueOrFalse) {
        message.success(t('common.saveSuccess'));
        onClose();
        passwordForm.resetFields();
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('密码修改失败:', error);
      message.error(t('common.saveFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="my-20px -ml-148px">
      <Form
        form={passwordForm}
        requiredMark={false}
        onFinish={onFinish}
        autoComplete="off"
      >
        <PasswordForm
          password={password}
          onPasswordChange={onPasswordChange}
          inputClassName="s-profile-input"
          labelClassName="text-label  !text-12px !w-135px"
          passwordRequirementsClassName="pl-148px"
          showLabelColon={false}
          showTermsCheckbox={false}
          className="border"
        />
        <EditButton
          className="pl-148px"
          loading={loading}
          onCancel={() => {
            onClose();
            passwordForm.resetFields();
          }}
        />
      </Form>
    </div>
  );
};

export default EditProfilePassword;

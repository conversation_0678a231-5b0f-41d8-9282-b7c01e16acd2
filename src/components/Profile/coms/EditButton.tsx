import React from 'react';
import { Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

interface EditButtonProps {
  onCancel: () => void;
  onSave?: () => void;
  htmlType?: 'submit' | 'button';
  className?: string;
  loading?: boolean;
}

const EditButton: React.FC<EditButtonProps> = ({
  onCancel,
  onSave,
  htmlType = 'submit',
  className,
  loading = false,
}) => {
  const { t } = useLanguage();
  return (
    <div className={`flex gap-12px ${className || ''}`}>
      <Button
        size="small"
        type="primary"
        ghost
        onClick={() => {
          onCancel();
        }}
        className="leading-24px rounded-2px !text-label !border-label text-12px hover:!text-label"
      >
        {t('common.cancel')}
      </Button>

      <Button
        size="small"
        type="primary"
        htmlType={htmlType}
        loading={loading}
        className="text-white hover:!text-white leading-24px rounded-2px text-12px"
        onClick={() => {
          onSave && onSave();
        }}
      >
        {t('common.save')}
      </Button>
    </div>
  );
};

export default EditButton;

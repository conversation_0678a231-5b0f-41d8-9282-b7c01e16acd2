import React, { useState, useMemo, useEffect, useRef } from 'react';
import emailIcon from '@/assets/images/icon-email.svg';
import verifyIcon from '@/assets/images/icon-verify.svg';
import type { GetProp } from 'antd';
import {
  Checkbox,
  Col,
  Row,
  Form,
  Button,
  Modal,
  Input,
  message,
  type InputRef,
  ConfigProvider,
} from 'antd';
import { api, loadingManager } from '@/services';
import EditButton from './coms/EditButton';
import {
  ArrowLeftOutlined,
  EditFilled,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import ImageUploadWithCrop from '@/components/ImageUploadWithCrop';
import { useLanguage } from '@/hooks/useLanguage';
import { getDisplayAddress } from '@/utils/utils';
import type { UserProfileResponse, UpdateProfileRequest } from '@/types/api';
import { useAuthStore } from '@/store/authStore';
import FormButton from '@/pages/Register/coms/FormButton';
import EditProfilePassword from './coms/EditProfilePassword';
import CountrySelector from '@/components/CountryStateSelector/CountrySelector';
import StateSelector from '@/components/CountryStateSelector/StateSelector';

interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}
const { TextArea } = Input;

const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  const { t, isEnUS } = useLanguage();
  const {
    logout,
    isArtist,
    profile: userInfo,
    profileLoading,
    fetchUserProfile,
    updateProfileData,
  } = useAuthStore();

  // 编辑状态管理
  const [editingFields, setEditingFields] = useState<Record<string, boolean>>(
    {}
  );
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  // 邮箱验证码倒计时状态
  const [emailCountdown, setEmailCountdown] = useState(0);
  const [isEmailSending, setIsEmailSending] = useState(false);
  const emailTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 使用单一 Form 实例，符合 React 最佳实践
  const [form] = Form.useForm();

  // 开始编辑
  const startEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: true }));
  };
  // 取消编辑
  const cancelEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: false }));
    // 重置表单字段到初始值
    form.resetFields();
  };
  // 保存编辑
  const saveEdit = async ({
    fieldName,
    requestData,
  }: {
    fieldName: string;
    requestData: UpdateProfileRequest;
  }) => {
    try {
      // 如果是alias字段，需要先验证
      if (fieldName === 'alias' && requestData.alias) {
        const aliasCheckResponse = await api.auth.checkAlias(requestData.alias);
        // 判断一下，如果别名没有修改，则不直接关闭编辑状态
        if (requestData.alias === userInfo?.alias) {
          setEditingFields(prev => ({ ...prev, [fieldName]: false }));
          return;
        }

        if (
          aliasCheckResponse.code === 200 &&
          aliasCheckResponse.body?.trueOrFalse === false
        ) {
          // 别名不可用，显示建议
          const suggestions = aliasCheckResponse.body.suggestions || [];
          const suggestionText =
            suggestions.length > 0
              ? t('auth.register.step2.messages.aliasSuggestions', {
                  suggestions: suggestions.join(', '),
                })
              : '';
          form.setFields([
            {
              name: 'alias',
              errors: [
                suggestionText ||
                  t('auth.register.step2.messages.aliasUnavailable'),
              ],
            },
          ]);

          return;
        }
      }

      const response = await api.user.updateProfile(requestData);
      if (response.code === 200) {
        setEditingFields(prev => ({ ...prev, [fieldName]: false }));
        message.success(t('common.saveSuccess'));
        // 重新拉取用户数据而不是仅更新本地状态
        await fetchUserProfile();
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 组件挂载时获取用户资料
  useEffect(() => {
    if (visible) {
      fetchUserProfile();
    }
  }, [visible, fetchUserProfile]);

  // 邮箱验证码倒计时函数
  const startEmailCountdown = () => {
    if (isEmailSending) return;
    setEmailCountdown(60);
    emailTimerRef.current = setInterval(() => {
      setEmailCountdown(prev => {
        if (prev <= 1) {
          clearInterval(emailTimerRef.current as NodeJS.Timeout);
          emailTimerRef.current = null;
          setIsEmailSending(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 重置所有状态
  const resetAllStates = () => {
    // 重置编辑状态
    setEditingFields({});
    // 重置表单
    form.resetFields();
    // 重置密码表单显示状态
    setShowPasswordForm(false);
    // 重置邮箱验证码倒计时
    setEmailCountdown(0);
    setIsEmailSending(false);
    // 清理邮箱验证码定时器
    if (emailTimerRef.current) {
      clearInterval(emailTimerRef.current);
      emailTimerRef.current = null;
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current);
      }
    };
  }, []);
  const displayAddress = useMemo(() => {
    return userInfo ? getDisplayAddress(userInfo) : '';
  }, [userInfo]);

  // 头像上传成功处理
  const handleAvatarUploadSuccess = async (url: string) => {
    try {
      const response = await api.user.updateProfile({ avatarUrl: url });
      if (response.code === 200) {
        message.success(t('common.saveSuccess'));
        // 重新拉取用户数据而不是仅更新本地状态
        await fetchUserProfile();
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 自定义上传请求
  const customAvatarUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      const response = await api.user.uploadAvatar(file);
      onSuccess({ url: response.url });
    } catch (error) {
      console.error('头像上传失败:', error);
      onError(error);
    }
  };
  interface ProfileItem {
    label: string;
    value: string;
    fieldName: string;
  }

  // 生成显示模式的组件
  const generateProfileItem = (item: ProfileItem) => {
    return (
      <div className="flex items-start" key={item.fieldName}>
        <span className="text-label mr-6px w-100px text-right -ml-100px">
          {item.label}:
        </span>
        <div className="flex-1">
          <span className="text-white">{item.value}</span>
          <EditFilled
            className="ml-10px cursor-pointer hover:text-primary"
            onClick={() => startEdit(item.fieldName)}
          />
        </div>
      </div>
    );
  };

  // 渲染不同类型的表单项
  const renderFormItems = (
    type: 'input' | 'textarea' | 'name' | 'address' | 'email',
    item: ProfileItem
  ) => {
    if (type === 'input') {
      return (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">{item.label}</span>
          }
          name={item.fieldName}
        >
          <Input className="s-profile-input" size="large" />
        </Form.Item>
      );
    }

    if (type === 'textarea') {
      return (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">{item.label}</span>
          }
          name={item.fieldName}
        >
          <TextArea rows={4} className="s-profile-input" />
        </Form.Item>
      );
    }

    if (type === 'name') {
      const rules = [
        {
          min: 1,
          message: t('auth.register.step3.validation.nameLength', {
            min: 1,
            max: 12,
          }),
        },
        {
          max: 12,
          message: t('auth.register.step3.validation.nameLength', {
            min: 1,
            max: 12,
          }),
        },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,
          message: t('auth.register.step3.validation.namePattern'),
        },
      ];

      const firstNameItem = (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">
              {t('auth.register.step3.form.firstName')}
            </span>
          }
          name="firstName"
          key="firstName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
            className="s-profile-input"
            size="large"
          />
        </Form.Item>
      );

      const lastNameItem = (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">
              {t('auth.register.step3.form.lastName')}
            </span>
          }
          name="lastName"
          key="lastName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
            className="s-profile-input"
            size="large"
          />
        </Form.Item>
      );

      return isEnUS
        ? [firstNameItem, lastNameItem]
        : [lastNameItem, firstNameItem];
    }

    if (type === 'address') {
      return (
        <>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.address')}
              </span>
            }
            name="address"
          >
            <Input
              placeholder={t('auth.register.step3.form.addressPlaceholder')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.city')}
              </span>
            }
            name="city"
          >
            <Input
              placeholder={t('auth.register.step3.form.cityPlaceholder')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.country')}
              </span>
            }
            name="country"
          >
            <CountrySelector
              placeholder={t('common.form.selectCountry')}
              size="small"
              className="s-profile-selector !h-35px"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.state')}
              </span>
            }
            name="state"
          >
            <StateSelector
              form={form}
              placeholder={t('common.form.selectState')}
              size="small"
              selectClassName="s-profile-selector !h-35px"
              inputClassName="s-profile-input !h-35px"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.postalZipCode')}
              </span>
            }
            name="postalZipCode"
          >
            <Input
              placeholder={t(
                'auth.register.step3.form.postalZipCodePlaceholder'
              )}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
        </>
      );
    }

    if (type === 'email') {
      return (
        <>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.email')}
              </span>
            }
            name="email"
            rules={[
              { required: true, message: t('common.form.emailRequired') },
            ]}
          >
            <Input
              placeholder={t('common.form.emailRequired')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.emailCode')}
              </span>
            }
            name="verificationCode"
            rules={[{ required: true, message: t('common.emailCodeRequired') }]}
          >
            <Input
              placeholder={t('auth.login.form.emailCodeLabel')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <div className="pl-114px flex flex-col gap-12px">
            <div>
              <Button
                htmlType="button"
                size="small"
                type="primary"
                loading={isEmailSending}
                disabled={isEmailSending || emailCountdown > 0}
                onClick={async () => {
                  try {
                    const emailValue = form.getFieldValue('email');
                    if (!emailValue) {
                      message.error(t('common.form.emailRequired'));
                      return;
                    }
                    // 新邮箱不能和旧邮箱相同
                    if (emailValue === userInfo?.email) {
                      form.setFields([
                        {
                          name: 'email',
                          errors: [t('common.messages.emailSameAsOld')],
                        },
                      ]);

                      return;
                    }
                    // 首先检查用户名（邮箱）是否可用
                    const usernameCheckResponse =
                      await api.auth.checkUsername(emailValue);

                    if (
                      usernameCheckResponse.code === 200 &&
                      usernameCheckResponse.body.trueOrFalse
                    ) {
                      // 用户名可用，发送验证码
                      setIsEmailSending(true);
                      const response = await api.user.sendChangeEmailOtp({
                        recipient: emailValue,
                      });

                      if (response.code === 200) {
                        message.success(
                          t('common.messages.verificationCodeSent')
                        );
                        startEmailCountdown();
                      } else {
                        message.error(
                          response.message || t('messages.sendFailed')
                        );
                      }
                    } else {
                      if (!usernameCheckResponse.body.trueOrFalse) {
                        form.setFields([
                          {
                            name: 'email',
                            errors: [t('common.messages.emailUnavailable')],
                          },
                        ]);
                      }
                    }
                  } catch (error) {
                    console.error('发送验证码失败:', error);
                    message.error(t('messages.sendFailed'));
                  } finally {
                    setIsEmailSending(false);
                  }
                }}
                className="leading-24px rounded-2px text-12px !text-white mb-15px hover:!text-white"
              >
                <img src={emailIcon} className="w-13px" alt="sendCode" />
                {emailCountdown > 0
                  ? `${emailCountdown}s`
                  : isEmailSending
                    ? t('common.sending')
                    : t('common.verificationCode')}
              </Button>
            </div>
            <div className="flex gap-12px">
              <Button
                htmlType="button"
                size="small"
                type="primary"
                ghost
                onClick={() => {
                  cancelEdit(item.fieldName);
                }}
                className="leading-24px !text-label !border-label rounded-2px text-12px  mb-15px hover:!text-label"
              >
                {t('common.cancel')}
              </Button>
              <Button
                size="small"
                type="primary"
                htmlType="submit"
                className="leading-24px rounded-2px text-12px text-white mb-15px hover:!text-white"
                icon={<CheckOutlined />}
              >
                {t('common.verify')}
              </Button>
            </div>
          </div>
        </>
      );
    }

    return null;
  };

  // 通用编辑组件 - 使用单一 form 实例和 initialValues
  const generateEditItem = (
    type: 'input' | 'textarea' | 'name' | 'address' | 'email',
    item: ProfileItem
  ) => {
    const handleSave = async (values: any) => {
      let requestData: any = {};

      if (type === 'name') {
        requestData = {
          firstName: values.firstName || '',
          lastName: values.lastName || '',
        };
      } else if (type === 'address') {
        requestData = {
          addressLine1: values.address || '',
          addressLine2: values.city || '',
          countryCode: values.country || '',
          stateProvince: values.state || '',
          postalZipCode: values.postalZipCode || '',
        };
      } else if (type === 'email') {
        // 邮箱编辑需要验证码
        try {
          console.log('values---->', values);
          // 调用验证OTP接口
          const verifyResponse = await api.auth.verifyOtp({
            username: values.email,
            verificationCode: values.verificationCode,
          });

          if (verifyResponse.code === 200 && verifyResponse.body.trueOrFalse) {
            const updateResponse = await api.user.updateProfile({
              email: values.email,
            });
            if (updateResponse.code === 200) {
              setEditingFields(prev => ({ ...prev, [item.fieldName]: false }));
              message.success(t('common.saveSuccess'));
              // 重新拉取用户数据而不是仅更新本地状态
              await fetchUserProfile();
            } else {
              message.error(updateResponse.message || t('common.saveFailed'));
            }
          } else {
            message.error(verifyResponse.message || t('common.saveFailed'));
          }
        } catch (error) {
          console.error('邮箱修改失败:', error);
          message.error(t('common.saveFailed'));
        }
        return;
      } else {
        requestData = {
          [item.fieldName]: values[item.fieldName] || '',
        };
      }

      saveEdit({
        fieldName: item.fieldName,
        requestData,
      });
    };

    // 设置初始值
    const getInitialValues = () => {
      if (type === 'name') {
        return {
          firstName: userInfo?.firstName || '',
          lastName: userInfo?.lastName || '',
        };
      } else if (type === 'address') {
        return {
          address: userInfo?.addressLine1 || '',
          city: userInfo?.addressLine2 || '',
          country: userInfo?.countryCode || '',
          state: userInfo?.stateProvince || '',
          postalZipCode: userInfo?.postalZipCode || '',
        };
      } else if (type === 'email') {
        return {
          email: userInfo?.email || '',
          verificationCode: '',
        };
      } else {
        return {
          [item.fieldName]: item.value,
        };
      }
    };

    return (
      <div className="flex items-start -ml-106px" key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleSave}
            autoComplete="off"
            initialValues={getInitialValues()}
          >
            {renderFormItems(type, item)}
            {type !== 'email' && (
              <EditButton
                className="pl-114px"
                htmlType="submit"
                onCancel={() => cancelEdit(item.fieldName)}
              />
            )}
          </Form>
        </div>
      </div>
    );
  };

  /**
   * 使用 TypeScript 的 as const 和 typeof 语法，将数组直接转换为枚举类型
   */
  const profileNameList = [
    'alias',
    'name',
    'email',
    'address',
    'stageName',
    'bio',
  ] as const;
  type ProfileName = (typeof profileNameList)[number];

  interface ProfileListItem {
    name: ProfileName;
    editable: boolean;
    _id?: number;
    showElement: React.ReactNode;
    editElement?: React.ReactNode;
  }
  const renderProfileInfo = () => {
    let profileList: ProfileListItem[] = [
      {
        name: 'alias',
        editable: !!editingFields['alias'],
        showElement: generateProfileItem({
          label: t('common.alias'),
          value: userInfo?.alias || '',
          fieldName: 'alias',
        }),
        editElement: generateEditItem('input', {
          label: t('common.alias'),
          value: userInfo?.alias || '',
          fieldName: 'alias',
        }),
      },
      {
        name: 'name',
        editable: !!editingFields['name'],
        showElement: generateProfileItem({
          label: t('common.name'),
          value: userInfo?.displayName || '',
          fieldName: 'name',
        }),
        editElement: generateEditItem('name', {
          label: t('common.name'),
          value: userInfo?.displayName || '',
          fieldName: 'name',
        }),
      },
      {
        name: 'email',
        editable: !!editingFields['email'],
        showElement: generateProfileItem({
          label: t('common.email'),
          value: userInfo?.email || '',
          fieldName: 'email',
        }),
        editElement: generateEditItem('email', {
          label: t('common.email'),
          value: userInfo?.email || '',
          fieldName: 'email',
        }),
      },
      {
        name: 'address',
        editable: !!editingFields['address'],
        showElement: generateProfileItem({
          label: t('common.address'),
          value: displayAddress,
          fieldName: 'address',
        }),
        editElement: generateEditItem('address', {
          label: t('common.address'),
          value: displayAddress,
          fieldName: 'address',
        }),
      },
    ];
    const artistProfileList: ProfileListItem[] = [
      {
        name: 'stageName',
        editable: !!editingFields['stageName'],
        showElement: generateProfileItem({
          label: t('common.stageName'),
          value: userInfo?.stageName || '',
          fieldName: 'stageName',
        }),
        editElement: generateEditItem('input', {
          label: t('common.stageName'),
          value: userInfo?.stageName || '',
          fieldName: 'stageName',
        }),
      },
      {
        name: 'bio',
        editable: !!editingFields['bio'],
        showElement: generateProfileItem({
          label: t('common.artistBio'),
          value: userInfo?.bio || '',
          fieldName: 'bio',
        }),
        editElement: generateEditItem('textarea', {
          label: t('common.artistBio'),
          value: userInfo?.bio || '',
          fieldName: 'bio',
        }),
      },
    ];

    if (isArtist) {
      profileList = profileList.concat(artistProfileList);
    }
    profileList.forEach((item, index) => {
      item._id = index;
    });

    return (
      <div className="flex flex-col gap-15px">
        {profileList.map(item => {
          return item.editable ? item.editElement : item.showElement;
        })}
      </div>
    );
  };

  if (!visible) return null;

  // 渲染骨架屏内容
  const renderSkeletonContent = () => {
    return (
      <div className="flex flex-col items-center pt-55px w-500px mx-auto">
        {/* 头像区域 */}
        <div className="flex flex-col items-center mb-30px">
          <div className="w-230px h-230px rounded-full bg-gray-700 animate-pulse mb-4"></div>
        </div>

        {/* 用户信息区域 */}
        <div className="w-full text-12px">
          <div className="text-center text-white text-22px font-medium mb-30px">
            <div className="h-6 bg-gray-700 rounded animate-pulse w-32 mx-auto"></div>
          </div>

          {/* 基本信息骨架 */}
          <div className="flex flex-col gap-15px">
            {[
              { label: t('common.alias') },
              { label: t('common.name') },
              { label: t('common.email') },
              { label: t('common.address') },
              ...(isArtist
                ? [
                    { label: t('common.stageName') },
                    { label: t('common.artistBio') },
                  ]
                : []),
            ].map((item, index) => (
              <div className="flex items-start" key={index}>
                <span className="text-label mr-6px w-100px text-right -ml-100px">
                  {item.label}:
                </span>
                <div className="flex-1">
                  <div className="h-4 bg-gray-700 rounded animate-pulse w-full max-w-xs"></div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-80px flex justify-center gap-20px">
            <div className="h-10 bg-gray-700 rounded animate-pulse flex-1"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse flex-1"></div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            optionLineHeight: '35px',
            optionHeight: 35,
          },
          Form: {
            fontSize: 12,
          },
        },
      }}
    >
      <Modal
        open={visible}
        onCancel={() => {
          resetAllStates();
          onClose();
        }}
        width={1000}
        className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-112px) "
        footer={null}
        keyboard={false}
        maskClosable={false}
        centered
        zIndex={800}
        closeIcon={
          <div className="flex items-center gap-2 hover:opacity-45">
            <ArrowLeftOutlined
              style={{ fontSize: '16px', color: 'var(--color-label)' }}
            />
            <span className="text-#B5B5B5 font-400 min-w-max">
              {t('common.back')}
            </span>
          </div>
        }
        title={
          <div className=" text-center text-white text-32px font-700">
            {t('common.profile')}
          </div>
        }
      >
        {profileLoading || !userInfo ? (
          renderSkeletonContent()
        ) : (
          <div className="flex flex-col items-center pt-55px w-500px mx-auto ">
            {/* 头像区域 */}
            <div className="flex flex-col items-center mb-30px">
              <ImageUploadWithCrop
                avatarUrl={userInfo?.avatarUrl || ''}
                avatarSize={230}
                userName={userInfo?.alias || ''}
                onUploadSuccess={handleAvatarUploadSuccess}
                customRequest={customAvatarUpload}
              />
            </div>

            {/* 用户信息区域 */}
            <div className="w-full text-12px">
              <div className="text-center text-white text-22px font-medium mb-30px">
                {userInfo?.displayName || ''}
              </div>
              <div>{renderProfileInfo()}</div>
              {/* 密码表单区域 */}
              <EditProfilePassword
                visible={showPasswordForm}
                onClose={() => setShowPasswordForm(false)}
              />
              <div className="mt-80px flex justify-center gap-20px">
                {!showPasswordForm && (
                  <FormButton
                    ghost
                    variant="outlined"
                    className="flex-1"
                    onClick={() => setShowPasswordForm(true)}
                  >
                    {t('common.changePassword')}
                  </FormButton>
                )}
                <FormButton type="primary" className="flex-1" onClick={logout}>
                  {t('common.navigation.logout')}
                </FormButton>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </ConfigProvider>
  );
};

export default Profile;

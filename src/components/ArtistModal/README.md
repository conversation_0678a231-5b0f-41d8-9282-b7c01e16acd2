# ArtistModal 组件

基于 Figma 设计稿实现的艺术家详情弹窗组件。

## 功能特性

- 🎨 完全基于 Figma 设计稿实现
- 📱 响应式设计，适配不同屏幕尺寸
- 🎵 展示艺术家基本信息和音乐作品列表
- 🔄 支持异步数据加载
- 💰 格式化显示收入和播放量数据
- 🎯 使用 UnoCSS 进行样式管理

## 组件接口

```typescript
interface ArtistModalProps {
  open: boolean;           // 控制弹窗显示/隐藏
  onClose: () => void;     // 关闭弹窗的回调函数
  artistId?: string;       // 艺术家ID，用于获取数据
  artistName?: string;     // 艺术家名称
  artistAvatar?: string;   // 艺术家头像URL
}
```

## 使用示例

```tsx
import React, { useState } from 'react';
import ArtistModal from '@/components/ArtistModal/ArtistModal';

const MyComponent = () => {
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <button onClick={() => setModalOpen(true)}>
        查看艺术家详情
      </button>
      
      <ArtistModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        artistId="artist-123"
        artistName="たかなし やすはる"
        artistAvatar="/path/to/avatar.jpg"
      />
    </>
  );
};
```

## 设计稿对应

该组件基于 Figma 设计稿 `modal_musician_detail_screen` (节点ID: 135:2026) 实现，包含：

- 艺术家头像 (230x230px)
- 艺术家基本信息（名称、发布数量、收入、播放量）
- 艺术家简介
- 发布的音乐作品列表

## 数据格式

组件会调用 `api.music.getTracksByArtist()` 获取艺术家的音乐作品，期望的数据格式：

```typescript
interface Track {
  id: string;
  title: string;
  artistId: string;
  artistStageName: string;
  genre: string;
  coverArtUrl: string;
  periodicRevenue: number;    // 周期收入（分）
  totalRevenue: number;       // 总收入（分）
  periodicStreams: number;    // 周期播放量
  totalStreams: number;       // 总播放量
  lastUpdate: number | null;  // 最后更新时间戳
}
```

## 样式说明

- 使用 UnoCSS 进行样式管理
- 遵循项目的设计系统颜色变量
- 支持深色主题
- 响应式布局

## 注意事项

1. 组件依赖 `YueModal` 作为基础弹窗容器
2. 需要正确配置 API 服务以获取数据
3. 图片资源需要正确的路径配置
4. 组件内置了模拟数据作为 API 失败时的后备方案

## 开发测试

在开发环境中，可以在首页右下角找到"测试艺术家弹窗"按钮来预览组件效果。

import React, { useState, useEffect, useMemo } from 'react';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import YueModal from '@/components/YueModal/YueModal';
import { api } from '@/services';
import type { Track } from '@/types/api';
import { formatNumber } from '@/utils/utils';
import { useLanguage } from '@/hooks/useLanguage';
import defaultAvatar from '@/assets/images/default-avatar.png';
import rectangle1 from '@/assets/images/rectangle-1.png';

interface ArtistModalProps {
  open: boolean;
  onClose: () => void;
  artistId?: string;
}

interface ArtistInfo {
  id: string;
  name: string;
  avatar: string;
  bio: string;
  releasedCount: number;
  revenue: number;
  streams: number;
}

type SortField = 'title' | 'genre' | 'totalRevenue' | 'totalStreams';
type SortOrder = 'asc' | 'desc' | null;

const ArtistModal: React.FC<ArtistModalProps> = ({
  open,
  onClose,
  artistId = 'default-artist-id',
}) => {
  const { t } = useLanguage();
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(false);
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortOrder, setSortOrder] = useState<SortOrder>(null);
  const [artistInfo, setArtistInfo] = useState<ArtistInfo>({
    name: 'たかなし やすはる',
    avatar: defaultAvatar,
    id: artistId,
    bio: 'Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.',
    releasedCount: 120,
    revenue: 1234560, // 以分为单位
    streams: 246906,
  });

  // 获取艺术家信息
  const fetchArtistInfo = async () => {
    const response = await api.music.getArtistInfo(artistId);
    if (response.code === 200) {
      setArtistInfo(response.body);
    }
  };

  // 获取艺术家音乐列表
  useEffect(() => {
    if (open && artistId) {
      fetchArtistTracks();
    }
  }, [open, artistId]);

  const fetchArtistTracks = async () => {
    try {
      setLoading(true);
      // const response = await api.music.getTracksByArtist(artistId, 1, 30);
      // if (response.code === 200) {
      //   setTracks(response.body.result || []);
      // }

      // 使用模拟数据 - 添加更多数据用于测试排序和分页
      setTracks([
        {
          id: '1',
          title: 'Midnight City Lights',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Electronic',
          coverArtUrl: rectangle1,
          periodicRevenue: 50000,
          totalRevenue: 200000,
          periodicStreams: 15000,
          totalStreams: 80000,
          lastUpdate: null,
        },
        {
          id: '2',
          title: 'Slow Poison',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Ambient',
          coverArtUrl: rectangle1,
          periodicRevenue: 30000,
          totalRevenue: 150000,
          periodicStreams: 12000,
          totalStreams: 65000,
          lastUpdate: null,
        },
        {
          id: '3',
          title: 'Aurora Dreams',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Classical',
          coverArtUrl: rectangle1,
          periodicRevenue: 75000,
          totalRevenue: 300000,
          periodicStreams: 20000,
          totalStreams: 120000,
          lastUpdate: null,
        },
        {
          id: '4',
          title: 'Digital Horizon',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Synthwave',
          coverArtUrl: rectangle1,
          periodicRevenue: 40000,
          totalRevenue: 180000,
          periodicStreams: 18000,
          totalStreams: 95000,
          lastUpdate: null,
        },
        {
          id: '5',
          title: 'Forest Whispers',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Nature',
          coverArtUrl: rectangle1,
          periodicRevenue: 25000,
          totalRevenue: 100000,
          periodicStreams: 8000,
          totalStreams: 45000,
          lastUpdate: null,
        },
        {
          id: '6',
          title: 'Neon Pulse',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Electronic',
          coverArtUrl: rectangle1,
          periodicRevenue: 60000,
          totalRevenue: 250000,
          periodicStreams: 22000,
          totalStreams: 110000,
          lastUpdate: null,
        },
        {
          id: '7',
          title: 'Silent Storm',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Ambient',
          coverArtUrl: rectangle1,
          periodicRevenue: 35000,
          totalRevenue: 140000,
          periodicStreams: 14000,
          totalStreams: 70000,
          lastUpdate: null,
        },
        {
          id: '8',
          title: 'Crystal Voyage',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'New Age',
          coverArtUrl: rectangle1,
          periodicRevenue: 45000,
          totalRevenue: 190000,
          periodicStreams: 16000,
          totalStreams: 85000,
          lastUpdate: null,
        },
        {
          id: '9',
          title: 'Cosmic Wind',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Space',
          coverArtUrl: rectangle1,
          periodicRevenue: 55000,
          totalRevenue: 220000,
          periodicStreams: 19000,
          totalStreams: 100000,
          lastUpdate: null,
        },
        {
          id: '10',
          title: 'Ocean Depths',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Ambient',
          coverArtUrl: rectangle1,
          periodicRevenue: 28000,
          totalRevenue: 120000,
          periodicStreams: 11000,
          totalStreams: 55000,
          lastUpdate: null,
        },
        {
          id: '11',
          title: 'Electric Symphony',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Electronic',
          coverArtUrl: rectangle1,
          periodicRevenue: 65000,
          totalRevenue: 280000,
          periodicStreams: 25000,
          totalStreams: 130000,
          lastUpdate: null,
        },
        {
          id: '12',
          title: 'Moonlight Serenade',
          artistId: artistId,
          artistStageName: artistInfo.name,
          genre: 'Classical',
          coverArtUrl: rectangle1,
          periodicRevenue: 48000,
          totalRevenue: 200000,
          periodicStreams: 17000,
          totalStreams: 90000,
          lastUpdate: null,
        },
      ]);
    } catch (error) {
      console.error('Failed to fetch artist tracks:', error);
    } finally {
      setLoading(false);
    }
  };

  // 排序处理函数
  const handleSort = (field: SortField) => {
    // 每次点击都会切换排序状态，会在这3个状态中循环。 asc → desc → null → asc
    let newOrder: SortOrder = 'asc';

    if (sortField === field) {
      if (sortOrder === 'asc') {
        newOrder = 'desc';
      } else if (sortOrder === 'desc') {
        newOrder = null;
      } else {
        newOrder = 'asc';
      }
    }

    setSortField(newOrder ? field : null);
    setSortOrder(newOrder);
  };

  // 获取排序图标
  const getSortIcon = (field: SortField) => {
    // 非排序列，显示向下灰色箭头
    if (sortField !== field) {
      return (
        <CaretDownOutlined className="ml-1 text-10px text-#666 hover:text-#999 transition-colors cursor-pointer" />
      );
    }
    // 排序状态，根据排序状态显示向上或向下的橙色箭头
    if (sortOrder === 'asc') {
      return <CaretUpOutlined className="ml-1 text-10px text-primary" />;
    } else if (sortOrder === 'desc') {
      return <CaretDownOutlined className="ml-1 text-10px text-primary" />;
    }

    // 非排序状态，显示向下灰色箭头
    return (
      <CaretDownOutlined className="ml-1 text-10px text-#666 hover:text-#999 transition-colors cursor-pointer" />
    );
  };

  // 处理排序和分页的音乐列表
  const sortedAndPaginatedTracks = useMemo(() => {
    let sortedTracks = [...tracks];

    // 排序逻辑
    if (sortField && sortOrder) {
      sortedTracks.sort((a, b) => {
        let aValue: string | number;
        let bValue: string | number;

        switch (sortField) {
          case 'title':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'genre':
            aValue = a.genre.toLowerCase();
            bValue = b.genre.toLowerCase();
            break;
          case 'totalRevenue':
            aValue = a.totalRevenue;
            bValue = b.totalRevenue;
            break;
          case 'totalStreams':
            aValue = a.totalStreams;
            bValue = b.totalStreams;
            break;
          default:
            return 0;
        }

        if (aValue < bValue) {
          return sortOrder === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortOrder === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return sortedTracks;
  }, [tracks, sortField, sortOrder]);

  return (
    <YueModal
      zIndex={801}
      open={open}
      onClose={onClose}
      centered={false}
      title={t('artistModal.artistProfile')}
      width={1000}
      className="artist-modal [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-50px) "
    >
      <div className="flex gap-8 text-white pt-47px">
        {/* 左侧：艺术家头像 */}
        <div className="flex-shrink-0">
          <img
            className="w-230px h-230px bg-cover bg-center bg-no-repeat rounded-full"
            src={artistInfo.avatar}
            alt={artistInfo.name}
          />
        </div>

        {/* 右侧：艺术家信息 */}
        <div className="flex-1 space-y-6">
          {/* 艺术家名称 */}
          <h2 className="text-26px font-medium text-white leading-none">
            {artistInfo.name}
          </h2>

          {/* 统计信息 */}
          <div className="flex gap-8 text-12px">
            <div className="flex gap-1.5">
              <span className="text-label">{t('artistModal.released')}:</span>
              <span className="text-white">
                {formatNumber(artistInfo.releasedCount)}
              </span>
            </div>
            <div className="flex gap-1.5">
              <span className="text-label">{t('artistModal.revenue')}:</span>
              <span className="text-white">
                {formatNumber(artistInfo.revenue)}
              </span>
            </div>
            <div className="flex gap-1.5">
              <span className="text-label">{t('artistModal.streams')}:</span>
              <span className="text-white">
                {formatNumber(artistInfo.streams)}
              </span>
            </div>
          </div>

          {/* 艺术家简介 */}
          <div className="space-y-2">
            <div className="text-12px text-label">
              {t('artistModal.artistBio')}:
            </div>
            <div className="text-12px text-white leading-normal max-h-32 overflow-y-auto">
              {artistInfo.bio}
            </div>
          </div>
        </div>
      </div>

      {/* 发布的音乐列表 */}
      <div className="mt-8">
        <h3 className="text-18px font-medium text-primary mb-4">
          {t('artistModal.releasedTracks')}
        </h3>

        {/* 表格头部 */}
        <div className="flex items-center gap-4 text-12px text-label py-3 px-4 bg-#151515 border-b border-#333">
          <div className="w-12"></div>
          <div
            className="flex-1 flex items-center cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('title')}
          >
            <span>{t('common.title')}</span>
            {getSortIcon('title')}
          </div>
          <div
            className="w-24 text-center flex items-center justify-center cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('genre')}
          >
            <span>{t('common.genre')}</span>
            {getSortIcon('genre')}
          </div>
          <div
            className="w-24 text-center flex items-center justify-center cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('totalRevenue')}
          >
            <span>{t('common.revenue')}</span>
            {getSortIcon('totalRevenue')}
          </div>
          <div
            className="w-24 text-center flex items-center justify-center cursor-pointer hover:text-white transition-colors"
            onClick={() => handleSort('totalStreams')}
          >
            <span>{t('common.streams')}</span>
            {getSortIcon('totalStreams')}
          </div>
        </div>

        {/* 音乐列表 */}
        <div className="space-y-0 max-h-[500px] overflow-y-auto">
          {loading ? (
            <div className="text-center py-8 text-label">
              {t('artistModal.loading')}
            </div>
          ) : sortedAndPaginatedTracks.length > 0 ? (
            sortedAndPaginatedTracks.map(track => (
              <div
                key={track.id}
                className="flex items-center gap-4 py-3 text-12px hover:bg-#1a1a1a transition-colors"
              >
                {/* 封面 */}
                <div
                  className="w-12 h-12 bg-cover bg-center bg-no-repeat rounded flex-shrink-0"
                  style={{
                    backgroundImage: `url('${track.coverArtUrl}')`,
                    backgroundColor: '#333',
                  }}
                />

                {/* 标题和艺术家 */}
                <div className="flex-1 min-w-0">
                  <div className="text-white text-14px font-medium truncate">
                    {track.title}
                  </div>
                  <div className="text-label text-11px truncate">
                    {track.artistStageName}
                  </div>
                </div>

                {/* 类型 */}
                <div className="w-24 text-center text-label">{track.genre}</div>
                {/* 收入 */}
                <div className="w-24 text-center text-label">
                  {formatNumber(track.totalRevenue)}
                </div>

                {/* 播放 */}
                <div className="w-24 text-center text-label">
                  {formatNumber(track.totalStreams)}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-label">
              {t('artistModal.noTracksFound')}
            </div>
          )}
        </div>
      </div>
    </YueModal>
  );
};

export default ArtistModal;

import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, type ButtonProps } from 'antd';
import i18n from '@/locales';
import { useLanguage } from '@/hooks/useLanguage';

const size = ['small', 'middle', 'large'] as const;
type Size = (typeof size)[number];
interface EmailCodeInputProps {
  value?: string; // 输入框的值
  onChange?: (value: string) => void; // 输入框的值改变的回调
  placeholder?: string; // 输入框的placeholder
  onSendCode?: () => Promise<void> | void; // 发送验证码的回调
  disabled?: boolean; // 是否禁用发送按钮
  countdownTime?: number; // 倒计时时间，默认60秒
  className?: string; // 输入框的className
  size?: Size; // 输入框的size
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  value,
  onChange,
  placeholder = i18n.t('auth.login.form.emailCodeLabel'),
  onSendCode,
  disabled = false,
  countdownTime = 60,
  className,
  size = 'large',
}) => {
  const { t } = useLanguage();
  const [isSending, setIsSending] = useState(false); // 是否正在发送验证码
  const [countdown, setCountdown] = useState(0); // 倒计时时间
  const isButtonDisabled = countdown > 0 || isSending || disabled; // 是否禁用发送按钮
  // 发送验证码
  const handleSendCode = async () => {
    if (onSendCode) {
      setIsSending(true);
      await onSendCode();
      setIsSending(false);
    }
    countDownStart();
  };
  // 倒计时timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 开始倒计时
  const countDownStart = () => {
    if (isSending) return;
    setCountdown(countdownTime);
    timerRef.current = setInterval(() => {
      setCountdown(prev => {
        console.log('设置countDown');

        if (prev <= 1) {
          clearInterval(timerRef.current as NodeJS.Timeout);
          timerRef.current = null;
          setIsSending(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  return (
    <Input
      type="text"
      size={size}
      placeholder={placeholder || t('auth.login.form.emailCodeLabel')}
      className={className || 's-form-input'}
      disabled={disabled}
      value={value}
      onChange={e => onChange?.(e.target.value)}
      suffix={
        <div className="flex items-center">
          {countdown > 0 && (
            <span className="mr-2 text-[12px] text-#999">{countdown}s</span>
          )}

          <Button
            variant="link"
            color="default"
            loading={isSending}
            onClick={handleSendCode}
            disabled={isButtonDisabled}
            className="text-[12px] !text-[#ff5e13] !disabled:text-#999"
          >
            {countdown > 0
              ? t('auth.login.form.resend')
              : isSending
                ? t('auth.login.form.sending')
                : t('auth.login.form.sendCode')}
          </Button>
        </div>
      }
    />
  );
};
export default EmailCodeInput;

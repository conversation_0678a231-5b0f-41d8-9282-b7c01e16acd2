import React, { useEffect, useMemo, useState } from 'react';
import { Layout as AntLayout } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import Header from '@/components/Header';
import { MAIN_MENU_ITEMS } from '@/config/menuConfig';
import Profile from '@/components/Profile';

const { Content, Sider, Header: AntHeader } = AntLayout;

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const {} = useAuthStore();

  // 初始化认证状态
  useEffect(() => {
    useAuthStore.getState().initializeAuth();
  }, []);

  const siderStyle: React.CSSProperties = {
    textAlign: 'center',
    lineHeight: '120px',
    color: 'white',
  };

  const [profileVisible, setProfileVisible] = useState(false);
  const handleOpenUserProfile = () => {
    setProfileVisible(true);
  };

  return (
    <div>
      <AntLayout className="min-h-screen bg-page-bg">
        <AntHeader>
          <Header fixed={false} onClick={handleOpenUserProfile} />
        </AntHeader>
        <AntLayout>
          <Sider width="25%" style={siderStyle}>
            {MAIN_MENU_ITEMS.map(item => (
              <div
                key={item.key}
                className="cursor-pointer mb-1 rounded p-2 hover:opacity-45"
                onClick={() => navigate(item.url)}
              >
                {item.name}
              </div>
            ))}
          </Sider>
          <Content className="flex-1">
            <Outlet />
          </Content>
        </AntLayout>
      </AntLayout>
      <Profile
        visible={profileVisible}
        onClose={() => setProfileVisible(false)}
      />
    </div>
  );
};

export default Layout;

import React, { useState, useRef, useCallback } from 'react';
import { Upload, Modal, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import ReactCrop, { type Crop, type PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useLanguage } from '@/hooks/useLanguage';

interface AlbumCoverUploadProps {
  /** 专辑封面地址（用于回显） */
  coverArtUrl?: string;
  /** 封面尺寸 */
  size?: number;
  /** 上传成功回调 */
  onUploadSuccess?: (url: string) => void;
  /** 自定义上传请求 */
  customRequest?: (options: any) => void;
}

const AlbumCoverUpload: React.FC<AlbumCoverUploadProps> = ({
  coverArtUrl,
  size = 340,
  onUploadSuccess,
  customRequest,
}) => {
  const { t } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(coverArtUrl || '');
  const [cropModalVisible, setCropModalVisible] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 80,
    height: 80,
    x: 10,
    y: 10,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const fileRef = useRef<File>(null);

  // 文件上传前验证
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error(t('messages.uploadImageFailed'));
      return false;
    }
    const isLt10M = file.size / 1024 / 1024 < 10; // 专辑封面允许更大文件
    if (!isLt10M) {
      message.error(t('submitMusic.imageSizeTooLarge'));
      return false;
    }

    // 保存文件引用并显示裁切模态框
    fileRef.current = file;
    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
      setCropModalVisible(true);
    };
    reader.readAsDataURL(file);

    return false; // 阻止自动上传
  };

  // 创建裁切后的canvas
  const getCroppedImg = useCallback(
    (
      image: HTMLImageElement,
      crop: PixelCrop,
      originalMimeType: string
    ): Promise<Blob> => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width,
        crop.height
      );

      return new Promise(resolve => {
        // 保留原始格式，如果是不支持的格式则降级为JPEG
        const mimeType =
          originalMimeType === 'image/webp' ||
          originalMimeType === 'image/png' ||
          originalMimeType === 'image/jpeg'
            ? originalMimeType
            : 'image/jpeg';
        const quality = mimeType === 'image/jpeg' ? 0.9 : undefined;
        canvas.toBlob(blob => resolve(blob!), mimeType, quality);
      });
    },
    []
  );

  // 确认裁切
  const handleCropConfirm = async () => {
    if (!completedCrop || !imgRef.current || !fileRef.current) return;

    try {
      setLoading(true);
      const originalMimeType = fileRef.current.type;
      const croppedImageBlob = await getCroppedImg(
        imgRef.current,
        completedCrop,
        originalMimeType
      );

      // 根据原始格式生成文件扩展名
      const getFileExtension = (mimeType: string) => {
        switch (mimeType) {
          case 'image/png':
            return '.png';
          case 'image/webp':
            return '.webp';
          case 'image/jpeg':
          case 'image/jpg':
          default:
            return '.jpg';
        }
      };

      const originalName = fileRef.current.name;
      const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
      const newExtension = getFileExtension(originalMimeType);
      const newFileName = nameWithoutExt + newExtension;

      const croppedFile = new File(
        [croppedImageBlob],
        newFileName || 'album-cover.jpg',
        { type: originalMimeType }
      );

      if (customRequest) {
        customRequest({
          file: croppedFile,
          onSuccess: (response: any) => {
            const url = response?.url || URL.createObjectURL(croppedImageBlob);
            setImageUrl(url);
            onUploadSuccess?.(url);
            setLoading(false);
            setCropModalVisible(false);
            message.success(t('messages.uploadSuccess'));
          },
          onError: () => {
            setLoading(false);
            message.error(t('messages.uploadFailed'));
          },
        });
      } else {
        const url = URL.createObjectURL(croppedImageBlob);
        setImageUrl(url);
        onUploadSuccess?.(url);
        setLoading(false);
        setCropModalVisible(false);
        message.success(t('messages.uploadSuccess'));
      }
    } catch {
      message.error(t('messages.uploadFailed'));
      setLoading(false);
    }
  };

  return (
    <>
      <Upload
        name="albumCover"
        listType="picture-card"
        showUploadList={false}
        beforeUpload={beforeUpload}
        accept="image/*"
        disabled={loading}
        style={{
          width: size,
          height: size,
          border: 'none',
          background: 'transparent',
        }}
      >
        <div className="relative inline-block">
          {imageUrl ? (
            <img
              src={imageUrl}
              alt={t('submitMusic.albumCover')}
              style={{
                width: size,
                height: size,
                objectFit: 'cover',
                borderRadius: '8px',
              }}
              className="cursor-pointer hover:opacity-80"
            />
          ) : (
            <div
              style={{
                width: size,
                height: size,
              }}
              className="flex flex-col items-center  justify-center gap-16px cursor-pointer hover:opacity-80 bg-black  border-none rounded-8px"
            >
              <UploadOutlined className=" text-#999 text-6xl" />
              <div>{t('submitMusic.coverArtUpload')}</div>
            </div>
          )}
        </div>
      </Upload>

      <Modal
        title={t('submitMusic.cropAlbumCover')}
        open={cropModalVisible}
        onOk={handleCropConfirm}
        onCancel={() => setCropModalVisible(false)}
        centered
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        confirmLoading={loading}
        keyboard={false}
        maskClosable={false}
      >
        <div className="flex justify-center py-36px">
          {imageSrc && (
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)}
              onComplete={setCompletedCrop}
              aspect={1} // 保持方形比例
              className="mx-auto"
            >
              <img
                ref={imgRef}
                src={imageSrc}
                style={{ maxHeight: '400px', maxWidth: '100%' }}
                onLoad={() => {
                  if (imgRef.current) {
                    const { width, height } = imgRef.current;
                    const size = Math.min(width, height);
                    const newCrop = {
                      unit: 'px' as const,
                      width: size,
                      height: size,
                      x: (width - size) / 2,
                      y: (height - size) / 2,
                    };
                    setCrop(newCrop);
                    setCompletedCrop(newCrop);
                  }
                }}
              />
            </ReactCrop>
          )}
        </div>
      </Modal>
    </>
  );
};

export default AlbumCoverUpload;

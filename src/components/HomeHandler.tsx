import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { getDefaultPageByRole } from '@/utils/roleUtils';
import Home from '@/pages/Home';

/**
 * 首页处理组件
 * 未登录用户显示Home页面
 * 已登录用户重定向到默认页面
 */
const HomeHandler: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();

  // 如果未登录，显示Home页面
  if (!isAuthenticated) {
    return <Home />;
  }

  // 如果已登录，根据角色重定向到默认页面
  const defaultPage = getDefaultPageByRole(user);
  return <Navigate to={defaultPage} replace />;
};

export default HomeHandler;

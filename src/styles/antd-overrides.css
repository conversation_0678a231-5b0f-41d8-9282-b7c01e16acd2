/* 全局样式 覆盖antd默认样式 */
/* 所有样式以s-开头 */

/**
 * 1. 针对 s-form-selector-hover 的样式
 * 当带有 .s-form-selector-hover 的 antd Select 组件在 hover 状态时，修改其选择器样式
 */
.s-form-selector-hover.ant-select-outlined:not(.ant-select-disabled):not(
    .ant-select-customize-input
  ):not(.ant-pagination-size-changer):hover
  .ant-select-selector {
  @apply !bg-white !border-white;
}

/**
 * 2. 针对 s-profile-selector 的样式
 * 复杂组合，分解成多个规则
 */

/* 默认、激活、选中项的样式 */
.s-profile-selector.ant-select-outlined:not(.ant-select-disabled):not(
    .ant-select-customize-input
  ):not(.ant-pagination-size-changer):hover
  .ant-select-selector,
.s-profile-selector.ant-select-outlined:not(.ant-select-disabled)
  .ant-select-selector,
.s-profile-selector .ant-select-item-option-selected {
  @apply !bg-label !border-label !rounded-2px !text-white text-12px;
}

/* 占位符 placeholder 的样式 */
.s-profile-selector .ant-select-selection-placeholder {
  @apply text-white text-12px;
}

/* 禁用状态的样式 */
.s-profile-selector.ant-select-outlined.ant-select-disabled
  .ant-select-selector,
.s-profile-selector.ant-select-outlined.ant-select-disabled
  .ant-select-selection-placeholder {
  @apply border-none text-black bg-label opacity-45;
}

/**
 * 3. 针对 s-form-input 的样式
 */
.s-form-input {
  @apply placeholder:font-inter h-[54px] rounded-6px border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25);
}

/**
 * 4. 针对 s-profile-input 的样式
 */

.s-profile-input {
  @apply rounded-2px  border-label  !bg-label hover:border-label   text-white h-35px  text-12px   placeholder:(text-[12px] text-black/25);
}

/* 密码图标样式 */
.s-profile-input .ant-input-password-icon {
  @apply !text-white;
}

.s-profile-input.ant-input-outlined:focus,
.s-profile-input.ant-input-outlined:focus-within {
  @apply border-label;
}
/* 提交音乐页面 样式 */
.s-submit-input {
  @apply rounded-2px   border-submit-input-bg   !bg-submit-input-bg   text-[#999999] h-42px  text-14px   placeholder:(text-[12px] text-[#474747]);
}
.s-submit-input-label label {
  --ant-form-label-height: 100%;
}
textarea.s-submit-input {
  @apply h-140px max-h-140px;
}
/* hover:border-submit-input-bg */
/* .s-submit-input.ant-input-outlined:focus,
.s--input.ant-input-outlined:focus-within {
  @apply border-submit-input-bg shadow-none;
} */

.s-submit-checkbox .ant-checkbox {
  .ant-checkbox-inner {
    --ant-color-bg-container: black;
    --ant-line-width: 2px;
    --ant-border-radius-sm: 2px;
  }

  &:not(.ant-checkbox-checked) {
    .ant-checkbox-inner {
      @apply border-label;
    }
  }
}

.s-submit-select {
  .ant-select-selector {
    --ant-color-border: var(--color-submit-input-bg);
    --ant-color-text: var(--color-999);
    --ant-border-radius: 0;
    --ant-select-selector-bg: var(--color-submit-input-bg);
  }
}
.s-submit-datepicker {
  --ant-color-text: var(--color-999);
  --ant-border-radius: 0;
  --ant-color-border: var(--color-submit-input-bg);
  --ant-color-bg-container: var(--color-submit-input-bg);
  --ant-date-picker-hover-bg: var(--color-submit-input-bg) !important;
  --ant-date-picker-active-bg: var(--color-submit-input-bg) !important;
  @apply w-full;
  /* &:hover {
  } */
}

.s-submit-pagination {
  .ant-pagination-item {
    --ant-pagination-item-bg: #222222;
    --ant-pagination-item-active-bg: #222222;
  }
  .ant-select-selector {
    --ant-select-selector-bg: #222222;
    --ant-color-border: var(--color-label);
  }
}

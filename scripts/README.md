# 音乐作品批量上传工具

这个工具可以帮助你批量上传50条音乐信息到系统中。

## 文件说明

- `batch-upload-music.js` - 主要的批量上传脚本
- `generate-music-data.js` - 生成50条示例音乐数据的脚本
- `music-data-template.json` - 音乐数据模板文件（5条示例）
- `music-data-50-tracks.json` - 50条音乐数据文件（由生成脚本创建）

## 使用步骤

### 1. 准备数据

#### 方法一：使用生成脚本（推荐）
```bash
# 生成50条示例音乐数据
node scripts/generate-music-data.js
```

#### 方法二：手动编辑
编辑 `music-data-template.json` 文件，添加你的音乐信息。

### 2. 准备封面URL

在数据文件中的 `coverUrls` 数组中添加你的封面图片URL：

```json
{
  "coverUrls": [
    "https://your-domain.com/cover1.jpg",
    "https://your-domain.com/cover2.jpg",
    "https://your-domain.com/cover3.jpg"
  ]
}
```

### 3. 获取认证Token

1. 登录你的账户
2. 从浏览器开发者工具中获取Authorization token
3. 在 `batch-upload-music.js` 中配置 `CONFIG.AUTH_TOKEN`

### 4. 配置API地址

在 `batch-upload-music.js` 中配置正确的API地址：

```javascript
const CONFIG = {
  BASE_URL: 'http://localhost:3000/api', // 开发环境
  // BASE_URL: 'https://your-production-api.com/api', // 生产环境
  AUTH_TOKEN: 'your-actual-token-here',
  REQUEST_INTERVAL: 1000, // 请求间隔（毫秒）
};
```

### 5. 运行批量上传

```bash
node scripts/batch-upload-music.js
```

## 数据格式说明

每条音乐数据包含以下字段：

```json
{
  "title": "歌曲标题",
  "labelName": "唱片公司名称",
  "albumName": "专辑名称",
  "trackInfo": "歌曲描述信息",
  "primaryLanguage": "zh", // 主要语言
  "upc": "123456789001", // UPC码
  "isrc": "CNRC24000001", // ISRC码
  "primaryGenreId": "pop", // 主要音乐类型
  "secondaryGenreId": "folk", // 次要音乐类型
  "copyrightName": "版权所有者名称",
  "copyrightYear": 2024, // 版权年份
  "phonogramCopyright": "录音版权所有者",
  "phonogramCopyrightYear": 2024, // 录音版权年份
  "audioFormats": ["mp3", "wav"], // 音频格式
  "releaseOptions": ["digital"], // 发布选项
  "mediaUrls": ["https://example.com/audio.mp3"] // 音频文件URL
}
```

## 可用的音乐类型

运行脚本时会自动获取并显示可用的音乐类型、音频格式和发布选项。

常见的音乐类型包括：
- `pop` - 流行音乐
- `rock` - 摇滚音乐
- `folk` - 民谣
- `electronic` - 电子音乐
- `jazz` - 爵士音乐
- `classical` - 古典音乐
- `hiphop` - 嘻哈音乐
- `country` - 乡村音乐

## 注意事项

1. **认证Token**: 确保使用有效的认证token
2. **请求频率**: 脚本会在每个请求之间等待1秒，避免请求过于频繁
3. **数据验证**: 上传前请确保所有必填字段都已填写
4. **封面URL**: 确保封面图片URL可以正常访问
5. **音频URL**: 确保音频文件URL可以正常访问

## 错误处理

脚本会自动处理错误并显示详细的错误信息：

- ✅ 成功上传的歌曲会显示绿色标记和ID
- ❌ 失败的歌曲会显示红色标记和错误原因
- 📊 最后会显示成功和失败的统计信息
- 📄 结果会保存到时间戳命名的JSON文件中

## 示例输出

```
🎵 音乐作品批量上传工具
================================
✅ 成功加载 50 条音乐数据
✅ 成功加载 10 个封面URL

📋 获取元数据信息...
🎵 可用的音乐类型:
  - pop: 流行音乐
  - rock: 摇滚音乐
  ...

⚠️  请确认以下信息:
📊 准备上传 50 首歌曲
🖼️  可用封面 10 个
🌐 API地址: http://localhost:3000/api
⏱️  请求间隔: 1000ms

🚀 开始批量上传音乐作品...
📝 [1/50] 处理中...
正在提交: 夜空中最亮的星 1
✅ 成功提交: 夜空中最亮的星 1, ID: track_123456

📊 上传完成统计:
✅ 成功: 48 首
❌ 失败: 2 首
📄 结果已保存到: upload-results-1703123456789.json
```

## 故障排除

1. **Token过期**: 重新登录获取新的token
2. **网络错误**: 检查网络连接和API地址
3. **数据格式错误**: 检查JSON文件格式是否正确
4. **权限不足**: 确保账户有上传音乐的权限

/**
 * 批量上传音乐作品脚本
 * 使用方法：
 * 1. 修改 music-data-template.json 文件，添加你的音乐信息和封面URL
 * 2. 获取登录token并配置到 CONFIG.AUTH_TOKEN
 * 3. 在项目根目录运行：node scripts/batch-upload-music.js
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置信息
const CONFIG = {
  // API 基础URL - 根据你的环境修改
  BASE_URL: 'https://musicapi.renee-arts.com/api/v1', // 开发环境

  // 认证token - 需要先登录获取（请更新为最新的token）
  AUTH_TOKEN:
    'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJiZDVjZGJjNC00MzdkLTQ0ZTItOTU5Mi0wMmI5OTJmNTlhZmQiLCJpYXQiOjE3NTY5MjE1NTIsImV4cCI6MTc1NjkyNTE1MiwianRpIjoiYTc0MDViNjktNmMyNy00NDZjLWI2ZjUtYjIwYzkzOTBmMDk3In0.Yz5qKTEIjEls1TZMd8idXpE5NOmH0bz_9hzY6onHVNQ',
  // 请求间隔（毫秒）- 避免请求过于频繁
  REQUEST_INTERVAL: 1000,

  // 数据文件路径 - 使用生成的50条数据
  DATA_FILE: path.join(__dirname, 'music-data-130-tracks.json'),
};

// 从JSON文件加载数据
function loadMusicData() {
  try {
    if (!fs.existsSync(CONFIG.DATA_FILE)) {
      console.log(`❌ 数据文件不存在: ${CONFIG.DATA_FILE}`);
      console.log('💡 请先创建 music-data-template.json 文件');
      return null;
    }

    const rawData = fs.readFileSync(CONFIG.DATA_FILE, 'utf8');
    const data = JSON.parse(rawData);

    if (!data.musicData || !Array.isArray(data.musicData)) {
      console.log('❌ 数据文件格式错误: 缺少 musicData 数组');
      return null;
    }

    if (!data.coverUrls || !Array.isArray(data.coverUrls)) {
      console.log('❌ 数据文件格式错误: 缺少 coverUrls 数组');
      return null;
    }

    console.log(`✅ 成功加载 ${data.musicData.length} 条音乐数据`);
    console.log(`✅ 成功加载 ${data.coverUrls.length} 个封面URL`);

    return data;
  } catch (error) {
    console.log(`❌ 加载数据文件失败: ${error.message}`);
    return null;
  }
}

// 创建HTTP客户端
const apiClient = axios.create({
  baseURL: CONFIG.BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${CONFIG.AUTH_TOKEN}`,
    'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'X-Realm': 1,
    'Accept-Language': 'zh',
  },
});

// 生成curl命令格式的请求
function generateCurlCommand(url, data, headers) {
  const curlHeaders = Object.entries(headers)
    .map(([key, value]) => `-H "${key}: ${value}"`)
    .join(' ');

  const curlData = JSON.stringify(data, null, 2).replace(/"/g, '\\"');

  return `curl -X POST "${url}" \\
${curlHeaders} \\
-d "${curlData}"`;
}

// 保存失败的请求到文件
function saveFailedRequest(trackData, coverUrl, error, response = null) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `failed-request-${trackData.title.replace(/[^a-zA-Z0-9]/g, '_')}-${timestamp}.txt`;
  const filepath = path.join(__dirname, 'failed-requests', filename);

  // 确保目录存在
  const dir = path.dirname(filepath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const submitData = {
    ...trackData,
    coverArtUrl: coverUrl,
  };

  const fullUrl = `${CONFIG.BASE_URL}/member/track`;
  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${CONFIG.AUTH_TOKEN}`,
    'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'X-Realm': '1',
    'Accept-Language': 'zh',
  };

  const curlCommand = generateCurlCommand(fullUrl, submitData, headers);

  const debugInfo = `
=== 失败请求调试信息 ===
时间: ${new Date().toISOString()}
歌曲: ${trackData.title}
错误: ${error}

=== 响应信息 ===
${response ? JSON.stringify(response.data, null, 2) : '无响应数据'}

=== HTTP状态码 ===
${response ? response.status : '无状态码'}

=== 请求数据 ===
${JSON.stringify(submitData, null, 2)}

=== CURL命令 (可直接复制使用) ===
${curlCommand}

=== 请求头 ===
${JSON.stringify(headers, null, 2)}
`;

  try {
    fs.writeFileSync(filepath, debugInfo);
    console.log(`🐛 调试信息已保存到: ${filepath}`);
  } catch (saveError) {
    console.log(`❌ 保存调试信息失败: ${saveError.message}`);
  }
}

// 修正数据格式 - 将简单格式转换为API要求的完整标识符
function fixDataFormat(trackData) {
  const audioFormatMap = {
    mp3: 'music.audio-format.supports-dolby-atmos',
    wav: 'music.audio-format.apple-digital-masters',
    flac: 'music.audio-format.apple-digital-masters',
  };

  const releaseOptionMap = {
    digital: 'music.release-option.enable-itunes-pre-order',
    streaming: 'music.release-option.live-concert-recording',
    physical: 'music.release-option.remastered-recording',
  };

  return {
    ...trackData,
    // 修正audioFormats格式
    audioFormats: trackData.audioFormats?.map(
      format => audioFormatMap[format] || format
    ) || [
      'music.audio-format.supports-dolby-atmos',
      'music.audio-format.apple-digital-masters',
    ],

    // 修正releaseOptions格式
    releaseOptions: trackData.releaseOptions?.map(
      option => releaseOptionMap[option] || option
    ) || [
      'music.release-option.enable-itunes-pre-order',
      'music.release-option.live-concert-recording',
    ],
  };
}

// 提交单个音乐作品
async function submitTrack(trackData, coverUrl) {
  try {
    const submitData = {
      ...trackData,
      coverArtUrl: coverUrl,
    };

    console.log(`正在提交: ${trackData.title}`);

    const response = await apiClient.post('/member/track', submitData);

    if (response.data.code === 200) {
      console.log(
        `✅ 成功提交: ${trackData.title}, ID: ${response.data.body.id}`
      );
      return {
        success: true,
        id: response.data.body.id,
        title: trackData.title,
      };
    } else {
      console.log(
        `❌ 提交失败: ${trackData.title}, 错误: ${response.data.message}`
      );

      // 保存失败请求的调试信息
      saveFailedRequest(trackData, coverUrl, response.data.message, response);

      return {
        success: false,
        error: response.data.message,
        title: trackData.title,
        debugInfo: `详细信息已保存到 failed-requests/ 目录`,
      };
    }
  } catch (error) {
    console.log(`❌ 提交失败: ${trackData.title}, 错误: ${error.message}`);

    // 保存失败请求的调试信息
    saveFailedRequest(trackData, coverUrl, error.message, error.response);

    return {
      success: false,
      error: error.message,
      title: trackData.title,
      debugInfo: `详细信息已保存到 failed-requests/ 目录`,
    };
  }
}

// 批量上传主函数
async function batchUpload(musicData, coverUrls) {
  console.log('🚀 开始批量上传音乐作品...');
  console.log(`📊 总计: ${musicData.length} 首歌曲`);

  const results = [];

  for (let i = 0; i < musicData.length; i++) {
    const trackData = musicData[i];
    const coverUrl = coverUrls[i];

    console.log(`\n📝 [${i + 1}/${musicData.length}] 处理中...`);

    // 添加时间戳字段
    const trackDataWithTimestamps = {
      ...trackData,
      originalReleaseDate: Date.now(),
      streetDate: Date.now(),
    };

    const result = await submitTrack(trackDataWithTimestamps, coverUrl);
    results.push(result);

    // 请求间隔
    if (i < musicData.length - 1) {
      console.log(`⏳ 等待 ${CONFIG.REQUEST_INTERVAL}ms...`);
      await new Promise(resolve =>
        setTimeout(resolve, CONFIG.REQUEST_INTERVAL)
      );
    }
  }

  // 输出统计结果
  const successCount = results.filter(r => r.success).length;
  const failCount = results.filter(r => !r.success).length;

  console.log('\n📊 上传完成统计:');
  console.log(`✅ 成功: ${successCount} 首`);
  console.log(`❌ 失败: ${failCount} 首`);

  if (failCount > 0) {
    console.log('\n❌ 失败列表:');
    results
      .filter(r => !r.success)
      .forEach(r => {
        console.log(`  - ${r.title}: ${r.error}`);
        if (r.debugInfo) {
          console.log(`    💡 ${r.debugInfo}`);
        }
      });

    console.log('\n🐛 调试提示:');
    console.log('  - 失败请求的详细信息已保存到 scripts/failed-requests/ 目录');
    console.log('  - 每个失败请求都包含完整的curl命令，可直接复制测试');
    console.log('  - 检查token是否过期、请求数据格式是否正确');
  }

  if (successCount > 0) {
    console.log('\n✅ 成功列表:');
    results
      .filter(r => r.success)
      .forEach(r => {
        console.log(`  - ${r.title} (ID: ${r.id})`);
      });
  }

  return results;
}

// 获取元数据信息的辅助函数
async function getMetadata() {
  try {
    console.log('📋 获取元数据信息...');

    const [genresRes, audioFormatsRes, releaseOptionsRes] = await Promise.all([
      apiClient.get('/meta/default/genres'),
      apiClient.get('/meta/default/audio-formats'),
      apiClient.get('/meta/default/release-options'),
    ]);

    console.log('\n🎵 可用的音乐类型:');
    genresRes.data.body.forEach(genre => {
      console.log(`  - ${genre.code}: ${genre.name}`);
    });

    console.log('\n🎧 可用的音频格式:');
    audioFormatsRes.data.body.forEach(format => {
      console.log(`  - ${format.code}: ${format.name}`);
    });

    console.log('\n📀 可用的发布选项:');
    releaseOptionsRes.data.body.forEach(option => {
      console.log(`  - ${option.code}: ${option.name}`);
    });

    // 返回元数据供其他函数使用
    return {
      genres: genresRes.data.body,
      audioFormats: audioFormatsRes.data.body,
      releaseOptions: releaseOptionsRes.data.body,
    };
  } catch (error) {
    console.log('❌ 获取元数据失败:', error.message);
    return null;
  }
}

// 主程序
async function main() {
  console.log('🎵 音乐作品批量上传工具');
  console.log('================================');

  // 检查配置
  if (CONFIG.AUTH_TOKEN === 'your-auth-token-here') {
    console.log('❌ 请先配置 AUTH_TOKEN');
    console.log(
      '💡 提示: 先登录系统获取token，然后修改脚本中的 CONFIG.AUTH_TOKEN'
    );
    return;
  }

  // 加载音乐数据
  const data = loadMusicData();

  if (!data) {
    console.log('❌ 无法加载音乐数据，程序退出');
    return;
  }

  const { musicData, coverUrls } = data;

  if (musicData.length === 0) {
    console.log('❌ 音乐数据为空，请先添加数据到 music-data-template.json');
    return;
  }

  // 获取元数据信息（可选）
  await getMetadata();

  console.log('\n⚠️  请确认以下信息:');
  console.log(`📊 准备上传 ${musicData.length} 首歌曲`);
  console.log(`🖼️  可用封面 ${coverUrls.length} 个`);
  console.log(`🌐 API地址: ${CONFIG.BASE_URL}`);
  console.log(`⏱️  请求间隔: ${CONFIG.REQUEST_INTERVAL}ms`);

  // 开始批量上传
  const results = await batchUpload(musicData.slice(3), coverUrls.slice(3));

  console.log('\n🎉 批量上传完成!');

  // 保存结果到文件
  const resultFile = path.join(__dirname, `upload-results-${Date.now()}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  console.log(`📄 结果已保存到: ${resultFile}`);
}

// 运行主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { submitTrack, batchUpload, getMetadata };

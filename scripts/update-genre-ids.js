#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 音乐类型映射表 - 将简短代码转换为完整的API格式
const genreMapping = {
  pop: 'music.genre.pop',
  electronic: 'music.genre.electronic',
  rock: 'music.genre.rock',
  dance: 'music.genre.dance',
  rnb: 'music.genre.rnb',
  hiphop: 'music.genre.hiphop',
  folk: 'music.genre.folk',
  country: 'music.genre.country',
  afrobeats: 'music.genre.afrobeats',
  indie: 'music.genre.indie',
  acoustic: 'music.genre.acoustic',
  ballad: 'music.genre.ballad',
  alternative: 'music.genre.alternative',
  synthpop: 'music.genre.synthpop',
  dark: 'music.genre.dark',
  funk: 'music.genre.funk',
  rap: 'music.genre.rap',
  latin: 'music.genre.latin',
  disco: 'music.genre.disco',
  trap: 'music.genre.trap',
  chill: 'music.genre.chill',
  melodic: 'music.genre.melodic',
};

function updateGenreIds(fileName = 'music-data-50-tracks.json') {
  const filePath = path.join(__dirname, fileName);

  try {
    console.log('📖 读取音乐数据文件...');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileContent);

    console.log('🔄 更新音乐类型ID格式...');
    let updateCount = 0;

    // 更新每首歌的音乐类型ID
    data.musicData.forEach((track, index) => {
      // 更新主要音乐类型ID
      if (track.primaryGenreId && genreMapping[track.primaryGenreId]) {
        const oldId = track.primaryGenreId;
        track.primaryGenreId = genreMapping[track.primaryGenreId];
        console.log(
          `  ${index + 1}. ${track.title}: primaryGenreId "${oldId}" -> "${track.primaryGenreId}"`
        );
        updateCount++;
      }

      // 更新次要音乐类型ID
      if (track.secondaryGenreId && genreMapping[track.secondaryGenreId]) {
        const oldId = track.secondaryGenreId;
        track.secondaryGenreId = genreMapping[track.secondaryGenreId];
        console.log(
          `     secondaryGenreId "${oldId}" -> "${track.secondaryGenreId}"`
        );
        updateCount++;
      }
    });

    console.log(`\n✅ 共更新了 ${updateCount} 个音乐类型ID`);

    // 写回文件
    console.log('💾 保存更新后的文件...');
    const updatedContent = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, updatedContent, 'utf8');

    console.log('🎉 音乐类型ID更新完成！');

    // 显示更新后的示例
    console.log('\n📋 更新后的示例:');
    data.musicData.slice(0, 3).forEach((track, index) => {
      console.log(`${index + 1}. ${track.title}:`);
      console.log(`   primaryGenreId: ${track.primaryGenreId}`);
      console.log(`   secondaryGenreId: ${track.secondaryGenreId}`);
    });
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    process.exit(1);
  }
}

// 运行更新
console.log('🎵 更新音乐类型ID格式工具');
console.log('================================\n');

// 更新50首歌曲文件
console.log('📁 更新 music-data-50-tracks.json...');
updateGenreIds('music-data-50-tracks.json');

console.log('\n📁 更新 music-data-3-tracks.json...');
updateGenreIds('music-data-3-tracks.json');

console.log('\n📁 更新生成脚本模板...');
updateGenerateScripts();

console.log('\n🎉 所有文件更新完成！');

// 更新生成脚本中的音乐类型ID格式
function updateGenerateScripts() {
  const scriptFiles = ['generate-english-songs.js', 'generate-music-data.js'];

  scriptFiles.forEach(fileName => {
    const filePath = path.join(__dirname, fileName);

    try {
      console.log(`  📝 更新 ${fileName}...`);
      let content = fs.readFileSync(filePath, 'utf8');

      // 替换音乐类型ID格式
      Object.keys(genreMapping).forEach(shortCode => {
        const fullCode = genreMapping[shortCode];
        // 替换 primaryGenreId: "shortCode" 格式
        content = content.replace(
          new RegExp(`primaryGenreId: ['"]${shortCode}['"]`, 'g'),
          `primaryGenreId: '${fullCode}'`
        );
        // 替换 secondaryGenreId: "shortCode" 格式
        content = content.replace(
          new RegExp(`secondaryGenreId: ['"]${shortCode}['"]`, 'g'),
          `secondaryGenreId: '${fullCode}'`
        );
      });

      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ ${fileName} 更新完成`);
    } catch (error) {
      console.log(`  ❌ 更新 ${fileName} 失败:`, error.message);
    }
  });
}

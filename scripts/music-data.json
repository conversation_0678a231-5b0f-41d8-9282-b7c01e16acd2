[{"title": "Midnight Dreams", "artist": "Luna Eclipse", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.synth-pop"}, {"title": "Electric Nights", "artist": "Neon Waves", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.electronic"}, {"title": "Summer Breeze", "artist": "Golden Coast", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.indie-pop"}, {"title": "City Lights", "artist": "Urban Pulse", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance"}, {"title": "Starlight Serenade", "artist": "Cosmic Dreams", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ambient"}, {"title": "Dancing Queen", "artist": "Royal Beats", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.disco"}, {"title": "Heartbeat Symphony", "artist": "Melody Makers", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.soundtrack"}, {"title": "Thunder Road", "artist": "Steel Lightning", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.classic-rock"}, {"title": "Rebel Heart", "artist": "Wild Spirits", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.alternative"}, {"title": "Fire Storm", "artist": "Blazing Guitars", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.hard-rock"}, {"title": "Midnight Rider", "artist": "Highway Kings", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.blues"}, {"title": "Electric Storm", "artist": "Voltage Rush", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.metal"}, {"title": "Revolution Song", "artist": "Freedom Fighters", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.punk"}, {"title": "Broken Dreams", "artist": "Shattered Glass", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.grunge"}, {"title": "Bass Drop", "artist": "Digital Storm", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.dubstep"}, {"title": "Neon Pulse", "artist": "Cyber Wave", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.techno"}, {"title": "Midnight House", "artist": "Deep Vibes", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.house"}, {"title": "Trance State", "artist": "Mind Shift", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.trance"}, {"title": "Club Anthem", "artist": "Party Masters", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.edm"}, {"title": "Underground Beat", "artist": "Basement Crew", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.garage"}, {"title": "Digital Dreams", "artist": "Future Sound", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.ambient"}, {"title": "Street Rhymes", "artist": "Urban Poets", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rap"}, {"title": "City Chronicles", "artist": "Metro Minds", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.rnb"}, {"title": "Boom Bap Classic", "artist": "Old School Kings", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.funk"}, {"title": "Flow State", "artist": "Rhythm Masters", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.soul"}, {"title": "Beat Machine", "artist": "Sample Lords", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.electronic"}, {"title": "<PERSON><PERSON>", "artist": "Verbal Assault", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.alternative"}, {"title": "Underground Anthem", "artist": "Concrete Jungle", "primaryGenreId": "music.genre.hip-hop", "secondaryGenreId": "music.genre.indie"}, {"title": "Smooth Operator", "artist": "Velvet Voice", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.blues"}, {"title": "Blue Note Cafe", "artist": "Midnight Quartet", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.soul"}, {"title": "Swing Time", "artist": "Big Band Revival", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.funk"}, {"title": "Saxophone Dreams", "artist": "Smooth Collective", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.ambient"}, {"title": "Jazz Fusion", "artist": "Electric Ensemble", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.electronic"}, {"title": "Late Night Sessions", "artist": "Lounge Lizards", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.chill"}, {"title": "Bebop Revival", "artist": "Modern Classics", "primaryGenreId": "music.genre.jazz", "secondaryGenreId": "music.genre.classical"}, {"title": "Country Roads", "artist": "Mountain View", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.folk"}, {"title": "Whiskey Nights", "artist": "Bourbon Creek", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.blues"}, {"title": "Hometown Hero", "artist": "Small Town Stories", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.singer-songwriter"}, {"title": "Truck Stop Blues", "artist": "Highway Wanderers", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.rock"}, {"title": "Barn Dance", "artist": "Fiddle Masters", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.bluegrass"}, {"title": "Southern Comfort", "artist": "Delta Dreams", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.gospel"}, {"title": "Sunset Highway", "artist": "Open Road", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.alternative"}, {"title": "Indie Anthem", "artist": "Bedroom Records", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.indie-rock"}, {"title": "Coffee Shop Vibes", "artist": "Acoustic Dreams", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.indie-pop"}, {"title": "Underground Scene", "artist": "Vinyl Collectors", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.alternative"}, {"title": "Lo-Fi Mornings", "artist": "Tape Deck", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.lo-fi"}, {"title": "Garage Band", "artist": "DIY Collective", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.punk"}, {"title": "Art School Dropout", "artist": "Creative Chaos", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.emo"}, {"title": "Hipster Cafe", "artist": "Vintage Sounds", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.folk"}, {"title": "Moonlight Sonata", "artist": "Symphony Orchestra", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.opera"}, {"title": "Epic Journey", "artist": "Film Score Ensemble", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.soundtrack"}, {"title": "Chamber Music", "artist": "String Quartet", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.ambient"}, {"title": "Baroque Dreams", "artist": "Period Instruments", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.world"}, {"title": "Modern Composition", "artist": "Contemporary Ensemble", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.electronic"}, {"title": "Piano Concerto", "artist": "Virtuoso Collective", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.jazz"}, {"title": "Orchestral Suite", "artist": "Grand Symphony", "primaryGenreId": "music.genre.classical", "secondaryGenreId": "music.genre.folk"}, {"title": "Island Vibes", "artist": "Tropical Breeze", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.ska"}, {"title": "One Love", "artist": "Rasta Warriors", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.dancehall"}, {"title": "Jamaican Sunset", "artist": "Caribbean Soul", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.dub"}, {"title": "Roots and Culture", "artist": "Conscious Collective", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.world"}, {"title": "Beach Party", "artist": "Summer Sounds", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.pop"}, {"title": "Positive Vibration", "artist": "Unity Band", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.gospel"}, {"title": "Reggae Fusion", "artist": "Modern Roots", "primaryGenreId": "music.genre.reggae", "secondaryGenreId": "music.genre.hip-hop"}, {"title": "Latin Fire", "artist": "Salsa Kings", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.salsa"}, {"title": "Bachata Romance", "artist": "Romantic Nights", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.bachata"}, {"title": "Reggaeton Beat", "artist": "Urban Latino", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.reggaeton"}, {"title": "Bossa Nova Cafe", "artist": "Rio Nights", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.bossa-nova"}, {"title": "Samba Carnival", "artist": "Brazilian Beats", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.samba"}, {"title": "Flamenco Passion", "artist": "Spanish Guitar", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.flamenco"}, {"title": "Tango Midnight", "artist": "Buenos Aires", "primaryGenreId": "music.genre.latin", "secondaryGenreId": "music.genre.tango"}, {"title": "Chill Waves", "artist": "Relaxation Station", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.chill"}, {"title": "Meditation Flow", "artist": "Inner Peace", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.downtempo"}, {"title": "Space Journey", "artist": "Cosmic Soundscapes", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.electronic"}, {"title": "Nature Sounds", "artist": "Earth Harmony", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.world"}, {"title": "Dream State", "artist": "Sleep Therapy", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.lo-fi"}, {"title": "Healing Frequencies", "artist": "Sound Bath", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.classical"}, {"title": "Atmospheric Layers", "artist": "Texture Collective", "primaryGenreId": "music.genre.ambient", "secondaryGenreId": "music.genre.soundtrack"}, {"title": "Smooth Groove", "artist": "Velvet Soul", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.soul"}, {"title": "Midnight Love", "artist": "Silk Voice", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.jazz"}, {"title": "Urban Romance", "artist": "City Lights", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.hip-hop"}]
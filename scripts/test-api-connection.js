/**
 * 测试API连接和认证的脚本
 * 运行：node scripts/test-api-connection.js
 */

import axios from 'axios';

// 配置信息 - 请修改为你的实际配置
const CONFIG = {
  BASE_URL: 'http://localhost:3000/api', // 修改为你的API地址
  AUTH_TOKEN: 'your-auth-token-here', // 修改为你的实际token
};

// 创建HTTP客户端
const apiClient = axios.create({
  baseURL: CONFIG.BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`,
    'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
    'X-Realm': 1,
    'Accept-Language': 'zh',
  },
});

// 测试API连接
async function testApiConnection() {
  console.log('🔍 测试API连接...');
  console.log(`🌐 API地址: ${CONFIG.BASE_URL}`);
  console.log(`🔑 Token: ${CONFIG.AUTH_TOKEN.substring(0, 20)}...`);
  
  try {
    // 测试获取音乐类型
    console.log('\n📋 测试获取音乐类型...');
    const genresResponse = await apiClient.get('/meta/default/genres');
    
    if (genresResponse.data.code === 200) {
      console.log('✅ 音乐类型API正常');
      console.log(`📊 可用类型数量: ${genresResponse.data.body.length}`);
      
      // 显示前5个类型
      console.log('🎵 前5个音乐类型:');
      genresResponse.data.body.slice(0, 5).forEach(genre => {
        console.log(`  - ${genre.code}: ${genre.name}`);
      });
    } else {
      console.log(`❌ 音乐类型API错误: ${genresResponse.data.message}`);
    }
  } catch (error) {
    console.log(`❌ 音乐类型API请求失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
  
  try {
    // 测试获取音频格式
    console.log('\n📋 测试获取音频格式...');
    const formatsResponse = await apiClient.get('/meta/default/audio-formats');
    
    if (formatsResponse.data.code === 200) {
      console.log('✅ 音频格式API正常');
      console.log(`📊 可用格式数量: ${formatsResponse.data.body.length}`);
    } else {
      console.log(`❌ 音频格式API错误: ${formatsResponse.data.message}`);
    }
  } catch (error) {
    console.log(`❌ 音频格式API请求失败: ${error.message}`);
  }
  
  try {
    // 测试获取发布选项
    console.log('\n📋 测试获取发布选项...');
    const optionsResponse = await apiClient.get('/meta/default/release-options');
    
    if (optionsResponse.data.code === 200) {
      console.log('✅ 发布选项API正常');
      console.log(`📊 可用选项数量: ${optionsResponse.data.body.length}`);
    } else {
      console.log(`❌ 发布选项API错误: ${optionsResponse.data.message}`);
    }
  } catch (error) {
    console.log(`❌ 发布选项API请求失败: ${error.message}`);
  }
}

// 测试提交音乐作品API（不实际提交）
async function testSubmitTrackApi() {
  console.log('\n🧪 测试提交音乐作品API（模拟数据）...');
  
  const testTrackData = {
    title: "测试歌曲",
    labelName: "测试唱片公司",
    albumName: "测试专辑",
    trackInfo: "这是一个测试歌曲",
    primaryLanguage: "zh",
    upc: "123456789999",
    isrc: "TEST24000001",
    primaryGenreId: "pop",
    secondaryGenreId: "folk",
    originalReleaseDate: Date.now(),
    streetDate: Date.now(),
    copyrightName: "测试版权",
    copyrightYear: 2024,
    phonogramCopyright: "测试录音版权",
    phonogramCopyrightYear: 2024,
    coverArtUrl: "https://example.com/test-cover.jpg",
    audioFormats: ["mp3"],
    releaseOptions: ["digital"],
    mediaUrls: ["https://example.com/test-audio.mp3"],
  };
  
  try {
    // 注意：这里只是测试API端点是否可达，不会实际提交
    // 如果你想实际测试提交，请取消下面的注释
    
    console.log('⚠️  这里只测试API端点，不会实际提交数据');
    console.log('💡 如果需要实际测试提交，请修改脚本');
    
    // const response = await apiClient.post('/member/track', testTrackData);
    // if (response.data.code === 200) {
    //   console.log('✅ 提交音乐作品API正常');
    //   console.log(`🆔 返回ID: ${response.data.body.id}`);
    // } else {
    //   console.log(`❌ 提交音乐作品API错误: ${response.data.message}`);
    // }
    
  } catch (error) {
    console.log(`❌ 提交音乐作品API请求失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      if (error.response.status === 401) {
        console.log('💡 提示: 401错误通常表示token无效或已过期');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🧪 API连接测试工具');
  console.log('===================');
  
  // 检查配置
  if (CONFIG.AUTH_TOKEN === 'your-auth-token-here') {
    console.log('❌ 请先配置 AUTH_TOKEN');
    console.log('💡 提示: 修改脚本中的 CONFIG.AUTH_TOKEN 为你的实际token');
    return;
  }
  
  await testApiConnection();
  await testSubmitTrackApi();
  
  console.log('\n🎉 API测试完成!');
  console.log('💡 如果所有测试都通过，你可以运行批量上传脚本了');
}

// 运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testApiConnection, testSubmitTrackApi };

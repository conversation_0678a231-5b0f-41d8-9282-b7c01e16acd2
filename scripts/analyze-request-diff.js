/**
 * 分析失败和成功请求的差异
 * 运行：node scripts/analyze-request-diff.js
 */

console.log('🔍 请求差异分析报告');
console.log('==================');

console.log('\n❌ 失败的请求格式:');
console.log('audioFormats: ["mp3", "wav"]');
console.log('releaseOptions: ["digital", "streaming"]');
console.log('Token: ...bd5cdbc4-437d-44e2-9592-02b992f59afd...');

console.log('\n✅ 成功的请求格式:');
console.log('audioFormats: ["music.audio-format.supports-dolby-atmos", "music.audio-format.apple-digital-masters"]');
console.log('releaseOptions: ["music.release-option.enable-itunes-pre-order", "music.release-option.live-concert-recording", "music.release-option.remastered-recording"]');
console.log('Token: ...d19571d0-6757-4dde-be5f-3a24fd65815d...');

console.log('\n🎯 关键差异:');
console.log('1. audioFormats 需要使用完整的API标识符格式');
console.log('2. releaseOptions 需要使用完整的API标识符格式');
console.log('3. Token 可能需要更新');

console.log('\n🛠️ 修复方案:');
console.log('1. ✅ 已更新脚本中的token');
console.log('2. ✅ 已添加数据格式修正函数');
console.log('3. ✅ 已添加失败请求的curl命令生成');

console.log('\n📋 格式映射:');
console.log('audioFormats:');
console.log('  mp3  -> music.audio-format.supports-dolby-atmos');
console.log('  wav  -> music.audio-format.apple-digital-masters');
console.log('  flac -> music.audio-format.apple-digital-masters');

console.log('\nreleaseOptions:');
console.log('  digital   -> music.release-option.enable-itunes-pre-order');
console.log('  streaming -> music.release-option.live-concert-recording');
console.log('  physical  -> music.release-option.remastered-recording');

console.log('\n🧪 测试建议:');
console.log('1. 运行: node scripts/test-single-track.js');
console.log('2. 如果成功，再运行批量上传脚本');
console.log('3. 检查 scripts/failed-requests/ 目录中的调试信息');

console.log('\n💡 调试提示:');
console.log('- 每个失败的请求都会生成完整的curl命令');
console.log('- 可以直接复制curl命令到终端测试');
console.log('- 检查token是否在有效期内');
console.log('- 确认API端点地址正确');

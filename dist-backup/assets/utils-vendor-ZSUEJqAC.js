import{r as H,R as qe}from"./react-vendor-D85R-n5s.js";const nn=(n,e,t,s)=>{const r=[t,{code:e,...s||{}}];if(n?.services?.logger?.forward)return n.services.logger.forward(r,"warn","react-i18next::",!0);q(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),n?.services?.logger?.warn?n.services.logger.warn(...r):console?.warn&&console.warn(...r)},Je={},Ae=(n,e,t,s)=>{q(t)&&Je[t]||(q(t)&&(Je[t]=new Date),nn(n,e,t,s))},Et=(n,e)=>()=>{if(n.isInitialized)e();else{const t=()=>{setTimeout(()=>{n.off("initialized",t)},0),e()};n.on("initialized",t)}},Fe=(n,e,t)=>{n.loadNamespaces(e,Et(n,t))},We=(n,e,t,s)=>{if(q(t)&&(t=[t]),n.options.preload&&n.options.preload.indexOf(e)>-1)return Fe(n,t,s);t.forEach(r=>{n.options.ns.indexOf(r)<0&&n.options.ns.push(r)}),n.loadLanguages(e,Et(n,s))},sn=(n,e,t={})=>!e.languages||!e.languages.length?(Ae(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(n,{lng:t.lng,precheck:(s,r)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!r(s.isLanguageChangingTo,n))return!1}}),q=n=>typeof n=="string",rn=n=>typeof n=="object"&&n!==null,on=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,an={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},ln=n=>an[n],un=n=>n.replace(on,ln);let ke={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:un};const cn=(n={})=>{ke={...ke,...n}},fn=()=>ke;let Lt;const dn=n=>{Lt=n},hn=()=>Lt,yr={type:"3rdParty",init(n){cn(n.options.react),dn(n)}},pn=H.createContext();class gn{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const mn=(n,e)=>{const t=H.useRef();return H.useEffect(()=>{t.current=n},[n,e]),t.current},Ct=(n,e,t,s)=>n.getFixedT(e,t,s),yn=(n,e,t,s)=>H.useCallback(Ct(n,e,t,s),[n,e,t,s]),br=(n,e={})=>{const{i18n:t}=e,{i18n:s,defaultNS:r}=H.useContext(pn)||{},i=t||s||hn();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new gn),!i){Ae(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const O=(L,C)=>q(C)?C:rn(C)&&q(C.defaultValue)?C.defaultValue:Array.isArray(L)?L[L.length-1]:L,R=[O,{},!1];return R.t=O,R.i18n={},R.ready=!1,R}i.options.react?.wait&&Ae(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...fn(),...i.options.react,...e},{useSuspense:a,keyPrefix:u}=o;let l=r||i.options?.defaultNS;l=q(l)?[l]:l||["translation"],i.reportNamespaces.addUsedNamespaces?.(l);const c=(i.isInitialized||i.initializedStoreOnce)&&l.every(O=>sn(O,i,o)),f=yn(i,e.lng||null,o.nsMode==="fallback"?l:l[0],u),g=()=>f,m=()=>Ct(i,e.lng||null,o.nsMode==="fallback"?l:l[0],u),[h,y]=H.useState(g);let p=l.join();e.lng&&(p=`${e.lng}${p}`);const w=mn(p),S=H.useRef(!0);H.useEffect(()=>{const{bindI18n:O,bindI18nStore:R}=o;S.current=!0,!c&&!a&&(e.lng?We(i,e.lng,l,()=>{S.current&&y(m)}):Fe(i,l,()=>{S.current&&y(m)})),c&&w&&w!==p&&S.current&&y(m);const L=()=>{S.current&&y(m)};return O&&i?.on(O,L),R&&i?.store.on(R,L),()=>{S.current=!1,i&&O?.split(" ").forEach(C=>i.off(C,L)),R&&i&&R.split(" ").forEach(C=>i.store.off(C,L))}},[i,p]),H.useEffect(()=>{S.current&&c&&y(g)},[i,u,c]);const E=[h,i,c];if(E.t=h,E.i18n=i,E.ready=c,c||!c&&!a)return E;throw new Promise(O=>{e.lng?We(i,e.lng,l,()=>O()):Fe(i,l,()=>O())})},Ge=n=>{let e;const t=new Set,s=(l,c)=>{const f=typeof l=="function"?l(e):l;if(!Object.is(f,e)){const g=e;e=c??(typeof f!="object"||f===null)?f:Object.assign({},e,f),t.forEach(m=>m(e,g))}},r=()=>e,a={setState:s,getState:r,getInitialState:()=>u,subscribe:l=>(t.add(l),()=>t.delete(l))},u=e=n(s,r,a);return a},bn=n=>n?Ge(n):Ge,Sn=n=>n;function xn(n,e=Sn){const t=qe.useSyncExternalStore(n.subscribe,()=>e(n.getState()),()=>e(n.getInitialState()));return qe.useDebugValue(t),t}const Xe=n=>{const e=bn(n),t=s=>xn(e,s);return Object.assign(t,e),t},Sr=n=>n?Xe(n):Xe;function Nt(n,e){return function(){return n.apply(e,arguments)}}const{toString:wn}=Object.prototype,{getPrototypeOf:_e}=Object,{iterator:be,toStringTag:Tt}=Symbol,Se=(n=>e=>{const t=wn.call(e);return n[t]||(n[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),v=n=>(n=n.toLowerCase(),e=>Se(e)===n),xe=n=>e=>typeof e===n,{isArray:X}=Array,se=xe("undefined");function On(n){return n!==null&&!se(n)&&n.constructor!==null&&!se(n.constructor)&&A(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Pt=v("ArrayBuffer");function Rn(n){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(n):e=n&&n.buffer&&Pt(n.buffer),e}const En=xe("string"),A=xe("function"),At=xe("number"),we=n=>n!==null&&typeof n=="object",Ln=n=>n===!0||n===!1,ce=n=>{if(Se(n)!=="object")return!1;const e=_e(n);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Tt in n)&&!(be in n)},Cn=v("Date"),Nn=v("File"),Tn=v("Blob"),Pn=v("FileList"),An=n=>we(n)&&A(n.pipe),Fn=n=>{let e;return n&&(typeof FormData=="function"&&n instanceof FormData||A(n.append)&&((e=Se(n))==="formdata"||e==="object"&&A(n.toString)&&n.toString()==="[object FormData]"))},kn=v("URLSearchParams"),[$n,jn,vn,Dn]=["ReadableStream","Request","Response","Headers"].map(v),Un=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function oe(n,e,{allOwnKeys:t=!1}={}){if(n===null||typeof n>"u")return;let s,r;if(typeof n!="object"&&(n=[n]),X(n))for(s=0,r=n.length;s<r;s++)e.call(null,n[s],s,n);else{const i=t?Object.getOwnPropertyNames(n):Object.keys(n),o=i.length;let a;for(s=0;s<o;s++)a=i[s],e.call(null,n[a],a,n)}}function Ft(n,e){e=e.toLowerCase();const t=Object.keys(n);let s=t.length,r;for(;s-- >0;)if(r=t[s],e===r.toLowerCase())return r;return null}const z=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,kt=n=>!se(n)&&n!==z;function $e(){const{caseless:n}=kt(this)&&this||{},e={},t=(s,r)=>{const i=n&&Ft(e,r)||r;ce(e[i])&&ce(s)?e[i]=$e(e[i],s):ce(s)?e[i]=$e({},s):X(s)?e[i]=s.slice():e[i]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&oe(arguments[s],t);return e}const In=(n,e,t,{allOwnKeys:s}={})=>(oe(e,(r,i)=>{t&&A(r)?n[i]=Nt(r,t):n[i]=r},{allOwnKeys:s}),n),Bn=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),_n=(n,e,t,s)=>{n.prototype=Object.create(e.prototype,s),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:e.prototype}),t&&Object.assign(n.prototype,t)},Mn=(n,e,t,s)=>{let r,i,o;const a={};if(e=e||{},n==null)return e;do{for(r=Object.getOwnPropertyNames(n),i=r.length;i-- >0;)o=r[i],(!s||s(o,n,e))&&!a[o]&&(e[o]=n[o],a[o]=!0);n=t!==!1&&_e(n)}while(n&&(!t||t(n,e))&&n!==Object.prototype);return e},Hn=(n,e,t)=>{n=String(n),(t===void 0||t>n.length)&&(t=n.length),t-=e.length;const s=n.indexOf(e,t);return s!==-1&&s===t},Vn=n=>{if(!n)return null;if(X(n))return n;let e=n.length;if(!At(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=n[e];return t},Kn=(n=>e=>n&&e instanceof n)(typeof Uint8Array<"u"&&_e(Uint8Array)),zn=(n,e)=>{const s=(n&&n[be]).call(n);let r;for(;(r=s.next())&&!r.done;){const i=r.value;e.call(n,i[0],i[1])}},qn=(n,e)=>{let t;const s=[];for(;(t=n.exec(e))!==null;)s.push(t);return s},Jn=v("HTMLFormElement"),Wn=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,s,r){return s.toUpperCase()+r}),Qe=(({hasOwnProperty:n})=>(e,t)=>n.call(e,t))(Object.prototype),Gn=v("RegExp"),$t=(n,e)=>{const t=Object.getOwnPropertyDescriptors(n),s={};oe(t,(r,i)=>{let o;(o=e(r,i,n))!==!1&&(s[i]=o||r)}),Object.defineProperties(n,s)},Xn=n=>{$t(n,(e,t)=>{if(A(n)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const s=n[t];if(A(s)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},Qn=(n,e)=>{const t={},s=r=>{r.forEach(i=>{t[i]=!0})};return X(n)?s(n):s(String(n).split(e)),t},Yn=()=>{},Zn=(n,e)=>n!=null&&Number.isFinite(n=+n)?n:e;function es(n){return!!(n&&A(n.append)&&n[Tt]==="FormData"&&n[be])}const ts=n=>{const e=new Array(10),t=(s,r)=>{if(we(s)){if(e.indexOf(s)>=0)return;if(!("toJSON"in s)){e[r]=s;const i=X(s)?[]:{};return oe(s,(o,a)=>{const u=t(o,r+1);!se(u)&&(i[a]=u)}),e[r]=void 0,i}}return s};return t(n,0)},ns=v("AsyncFunction"),ss=n=>n&&(we(n)||A(n))&&A(n.then)&&A(n.catch),jt=((n,e)=>n?setImmediate:e?((t,s)=>(z.addEventListener("message",({source:r,data:i})=>{r===z&&i===t&&s.length&&s.shift()()},!1),r=>{s.push(r),z.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",A(z.postMessage)),rs=typeof queueMicrotask<"u"?queueMicrotask.bind(z):typeof process<"u"&&process.nextTick||jt,is=n=>n!=null&&A(n[be]),d={isArray:X,isArrayBuffer:Pt,isBuffer:On,isFormData:Fn,isArrayBufferView:Rn,isString:En,isNumber:At,isBoolean:Ln,isObject:we,isPlainObject:ce,isReadableStream:$n,isRequest:jn,isResponse:vn,isHeaders:Dn,isUndefined:se,isDate:Cn,isFile:Nn,isBlob:Tn,isRegExp:Gn,isFunction:A,isStream:An,isURLSearchParams:kn,isTypedArray:Kn,isFileList:Pn,forEach:oe,merge:$e,extend:In,trim:Un,stripBOM:Bn,inherits:_n,toFlatObject:Mn,kindOf:Se,kindOfTest:v,endsWith:Hn,toArray:Vn,forEachEntry:zn,matchAll:qn,isHTMLForm:Jn,hasOwnProperty:Qe,hasOwnProp:Qe,reduceDescriptors:$t,freezeMethods:Xn,toObjectSet:Qn,toCamelCase:Wn,noop:Yn,toFiniteNumber:Zn,findKey:Ft,global:z,isContextDefined:kt,isSpecCompliantForm:es,toJSONObject:ts,isAsyncFn:ns,isThenable:ss,setImmediate:jt,asap:rs,isIterable:is};function x(n,e,t,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}d.inherits(x,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:d.toJSONObject(this.config),code:this.code,status:this.status}}});const vt=x.prototype,Dt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{Dt[n]={value:n}});Object.defineProperties(x,Dt);Object.defineProperty(vt,"isAxiosError",{value:!0});x.from=(n,e,t,s,r,i)=>{const o=Object.create(vt);return d.toFlatObject(n,o,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),x.call(o,n.message,e,t,s,r),o.cause=n,o.name=n.name,i&&Object.assign(o,i),o};const os=null;function je(n){return d.isPlainObject(n)||d.isArray(n)}function Ut(n){return d.endsWith(n,"[]")?n.slice(0,-2):n}function Ye(n,e,t){return n?n.concat(e).map(function(r,i){return r=Ut(r),!t&&i?"["+r+"]":r}).join(t?".":""):e}function as(n){return d.isArray(n)&&!n.some(je)}const ls=d.toFlatObject(d,{},null,function(e){return/^is[A-Z]/.test(e)});function Oe(n,e,t){if(!d.isObject(n))throw new TypeError("target must be an object");e=e||new FormData,t=d.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!d.isUndefined(p[y])});const s=t.metaTokens,r=t.visitor||c,i=t.dots,o=t.indexes,u=(t.Blob||typeof Blob<"u"&&Blob)&&d.isSpecCompliantForm(e);if(!d.isFunction(r))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(d.isDate(h))return h.toISOString();if(d.isBoolean(h))return h.toString();if(!u&&d.isBlob(h))throw new x("Blob is not supported. Use a Buffer instead.");return d.isArrayBuffer(h)||d.isTypedArray(h)?u&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function c(h,y,p){let w=h;if(h&&!p&&typeof h=="object"){if(d.endsWith(y,"{}"))y=s?y:y.slice(0,-2),h=JSON.stringify(h);else if(d.isArray(h)&&as(h)||(d.isFileList(h)||d.endsWith(y,"[]"))&&(w=d.toArray(h)))return y=Ut(y),w.forEach(function(E,O){!(d.isUndefined(E)||E===null)&&e.append(o===!0?Ye([y],O,i):o===null?y:y+"[]",l(E))}),!1}return je(h)?!0:(e.append(Ye(p,y,i),l(h)),!1)}const f=[],g=Object.assign(ls,{defaultVisitor:c,convertValue:l,isVisitable:je});function m(h,y){if(!d.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+y.join("."));f.push(h),d.forEach(h,function(w,S){(!(d.isUndefined(w)||w===null)&&r.call(e,w,d.isString(S)?S.trim():S,y,g))===!0&&m(w,y?y.concat(S):[S])}),f.pop()}}if(!d.isObject(n))throw new TypeError("data must be an object");return m(n),e}function Ze(n){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(s){return e[s]})}function Me(n,e){this._pairs=[],n&&Oe(n,this,e)}const It=Me.prototype;It.append=function(e,t){this._pairs.push([e,t])};It.toString=function(e){const t=e?function(s){return e.call(this,s,Ze)}:Ze;return this._pairs.map(function(r){return t(r[0])+"="+t(r[1])},"").join("&")};function us(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bt(n,e,t){if(!e)return n;const s=t&&t.encode||us;d.isFunction(t)&&(t={serialize:t});const r=t&&t.serialize;let i;if(r?i=r(e,t):i=d.isURLSearchParams(e)?e.toString():new Me(e,t).toString(s),i){const o=n.indexOf("#");o!==-1&&(n=n.slice(0,o)),n+=(n.indexOf("?")===-1?"?":"&")+i}return n}class et{constructor(){this.handlers=[]}use(e,t,s){return this.handlers.push({fulfilled:e,rejected:t,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){d.forEach(this.handlers,function(s){s!==null&&e(s)})}}const _t={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},cs=typeof URLSearchParams<"u"?URLSearchParams:Me,fs=typeof FormData<"u"?FormData:null,ds=typeof Blob<"u"?Blob:null,hs={isBrowser:!0,classes:{URLSearchParams:cs,FormData:fs,Blob:ds},protocols:["http","https","file","blob","url","data"]},He=typeof window<"u"&&typeof document<"u",ve=typeof navigator=="object"&&navigator||void 0,ps=He&&(!ve||["ReactNative","NativeScript","NS"].indexOf(ve.product)<0),gs=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ms=He&&window.location.href||"http://localhost",ys=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:He,hasStandardBrowserEnv:ps,hasStandardBrowserWebWorkerEnv:gs,navigator:ve,origin:ms},Symbol.toStringTag,{value:"Module"})),T={...ys,...hs};function bs(n,e){return Oe(n,new T.classes.URLSearchParams,Object.assign({visitor:function(t,s,r,i){return T.isNode&&d.isBuffer(t)?(this.append(s,t.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},e))}function Ss(n){return d.matchAll(/\w+|\[(\w*)]/g,n).map(e=>e[0]==="[]"?"":e[1]||e[0])}function xs(n){const e={},t=Object.keys(n);let s;const r=t.length;let i;for(s=0;s<r;s++)i=t[s],e[i]=n[i];return e}function Mt(n){function e(t,s,r,i){let o=t[i++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),u=i>=t.length;return o=!o&&d.isArray(r)?r.length:o,u?(d.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!a):((!r[o]||!d.isObject(r[o]))&&(r[o]=[]),e(t,s,r[o],i)&&d.isArray(r[o])&&(r[o]=xs(r[o])),!a)}if(d.isFormData(n)&&d.isFunction(n.entries)){const t={};return d.forEachEntry(n,(s,r)=>{e(Ss(s),r,t,0)}),t}return null}function ws(n,e,t){if(d.isString(n))try{return(e||JSON.parse)(n),d.trim(n)}catch(s){if(s.name!=="SyntaxError")throw s}return(t||JSON.stringify)(n)}const ae={transitional:_t,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const s=t.getContentType()||"",r=s.indexOf("application/json")>-1,i=d.isObject(e);if(i&&d.isHTMLForm(e)&&(e=new FormData(e)),d.isFormData(e))return r?JSON.stringify(Mt(e)):e;if(d.isArrayBuffer(e)||d.isBuffer(e)||d.isStream(e)||d.isFile(e)||d.isBlob(e)||d.isReadableStream(e))return e;if(d.isArrayBufferView(e))return e.buffer;if(d.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return bs(e,this.formSerializer).toString();if((a=d.isFileList(e))||s.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Oe(a?{"files[]":e}:e,u&&new u,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),ws(e)):e}],transformResponse:[function(e){const t=this.transitional||ae.transitional,s=t&&t.forcedJSONParsing,r=this.responseType==="json";if(d.isResponse(e)||d.isReadableStream(e))return e;if(e&&d.isString(e)&&(s&&!this.responseType||r)){const o=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(a){if(o)throw a.name==="SyntaxError"?x.from(a,x.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};d.forEach(["delete","get","head","post","put","patch"],n=>{ae.headers[n]={}});const Os=d.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Rs=n=>{const e={};let t,s,r;return n&&n.split(`
`).forEach(function(o){r=o.indexOf(":"),t=o.substring(0,r).trim().toLowerCase(),s=o.substring(r+1).trim(),!(!t||e[t]&&Os[t])&&(t==="set-cookie"?e[t]?e[t].push(s):e[t]=[s]:e[t]=e[t]?e[t]+", "+s:s)}),e},tt=Symbol("internals");function ee(n){return n&&String(n).trim().toLowerCase()}function fe(n){return n===!1||n==null?n:d.isArray(n)?n.map(fe):String(n)}function Es(n){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=t.exec(n);)e[s[1]]=s[2];return e}const Ls=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Ce(n,e,t,s,r){if(d.isFunction(s))return s.call(this,e,t);if(r&&(e=t),!!d.isString(e)){if(d.isString(s))return e.indexOf(s)!==-1;if(d.isRegExp(s))return s.test(e)}}function Cs(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,s)=>t.toUpperCase()+s)}function Ns(n,e){const t=d.toCamelCase(" "+e);["get","set","has"].forEach(s=>{Object.defineProperty(n,s+t,{value:function(r,i,o){return this[s].call(this,e,r,i,o)},configurable:!0})})}let F=class{constructor(e){e&&this.set(e)}set(e,t,s){const r=this;function i(a,u,l){const c=ee(u);if(!c)throw new Error("header name must be a non-empty string");const f=d.findKey(r,c);(!f||r[f]===void 0||l===!0||l===void 0&&r[f]!==!1)&&(r[f||u]=fe(a))}const o=(a,u)=>d.forEach(a,(l,c)=>i(l,c,u));if(d.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(d.isString(e)&&(e=e.trim())&&!Ls(e))o(Rs(e),t);else if(d.isObject(e)&&d.isIterable(e)){let a={},u,l;for(const c of e){if(!d.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[l=c[0]]=(u=a[l])?d.isArray(u)?[...u,c[1]]:[u,c[1]]:c[1]}o(a,t)}else e!=null&&i(t,e,s);return this}get(e,t){if(e=ee(e),e){const s=d.findKey(this,e);if(s){const r=this[s];if(!t)return r;if(t===!0)return Es(r);if(d.isFunction(t))return t.call(this,r,s);if(d.isRegExp(t))return t.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ee(e),e){const s=d.findKey(this,e);return!!(s&&this[s]!==void 0&&(!t||Ce(this,this[s],s,t)))}return!1}delete(e,t){const s=this;let r=!1;function i(o){if(o=ee(o),o){const a=d.findKey(s,o);a&&(!t||Ce(s,s[a],a,t))&&(delete s[a],r=!0)}}return d.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let s=t.length,r=!1;for(;s--;){const i=t[s];(!e||Ce(this,this[i],i,e,!0))&&(delete this[i],r=!0)}return r}normalize(e){const t=this,s={};return d.forEach(this,(r,i)=>{const o=d.findKey(s,i);if(o){t[o]=fe(r),delete t[i];return}const a=e?Cs(i):String(i).trim();a!==i&&delete t[i],t[a]=fe(r),s[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return d.forEach(this,(s,r)=>{s!=null&&s!==!1&&(t[r]=e&&d.isArray(s)?s.join(", "):s)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const s=new this(e);return t.forEach(r=>s.set(r)),s}static accessor(e){const s=(this[tt]=this[tt]={accessors:{}}).accessors,r=this.prototype;function i(o){const a=ee(o);s[a]||(Ns(r,o),s[a]=!0)}return d.isArray(e)?e.forEach(i):i(e),this}};F.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);d.reduceDescriptors(F.prototype,({value:n},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>n,set(s){this[t]=s}}});d.freezeMethods(F);function Ne(n,e){const t=this||ae,s=e||t,r=F.from(s.headers);let i=s.data;return d.forEach(n,function(a){i=a.call(t,i,r.normalize(),e?e.status:void 0)}),r.normalize(),i}function Ht(n){return!!(n&&n.__CANCEL__)}function Q(n,e,t){x.call(this,n??"canceled",x.ERR_CANCELED,e,t),this.name="CanceledError"}d.inherits(Q,x,{__CANCEL__:!0});function Vt(n,e,t){const s=t.config.validateStatus;!t.status||!s||s(t.status)?n(t):e(new x("Request failed with status code "+t.status,[x.ERR_BAD_REQUEST,x.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function Ts(n){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return e&&e[1]||""}function Ps(n,e){n=n||10;const t=new Array(n),s=new Array(n);let r=0,i=0,o;return e=e!==void 0?e:1e3,function(u){const l=Date.now(),c=s[i];o||(o=l),t[r]=u,s[r]=l;let f=i,g=0;for(;f!==r;)g+=t[f++],f=f%n;if(r=(r+1)%n,r===i&&(i=(i+1)%n),l-o<e)return;const m=c&&l-c;return m?Math.round(g*1e3/m):void 0}}function As(n,e){let t=0,s=1e3/e,r,i;const o=(l,c=Date.now())=>{t=c,r=null,i&&(clearTimeout(i),i=null),n.apply(null,l)};return[(...l)=>{const c=Date.now(),f=c-t;f>=s?o(l,c):(r=l,i||(i=setTimeout(()=>{i=null,o(r)},s-f)))},()=>r&&o(r)]}const he=(n,e,t=3)=>{let s=0;const r=Ps(50,250);return As(i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,u=o-s,l=r(u),c=o<=a;s=o;const f={loaded:o,total:a,progress:a?o/a:void 0,bytes:u,rate:l||void 0,estimated:l&&a&&c?(a-o)/l:void 0,event:i,lengthComputable:a!=null,[e?"download":"upload"]:!0};n(f)},t)},nt=(n,e)=>{const t=n!=null;return[s=>e[0]({lengthComputable:t,total:n,loaded:s}),e[1]]},st=n=>(...e)=>d.asap(()=>n(...e)),Fs=T.hasStandardBrowserEnv?((n,e)=>t=>(t=new URL(t,T.origin),n.protocol===t.protocol&&n.host===t.host&&(e||n.port===t.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,ks=T.hasStandardBrowserEnv?{write(n,e,t,s,r,i){const o=[n+"="+encodeURIComponent(e)];d.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),d.isString(s)&&o.push("path="+s),d.isString(r)&&o.push("domain="+r),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(n){const e=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function $s(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function js(n,e){return e?n.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):n}function Kt(n,e,t){let s=!$s(e);return n&&(s||t==!1)?js(n,e):e}const rt=n=>n instanceof F?{...n}:n;function W(n,e){e=e||{};const t={};function s(l,c,f,g){return d.isPlainObject(l)&&d.isPlainObject(c)?d.merge.call({caseless:g},l,c):d.isPlainObject(c)?d.merge({},c):d.isArray(c)?c.slice():c}function r(l,c,f,g){if(d.isUndefined(c)){if(!d.isUndefined(l))return s(void 0,l,f,g)}else return s(l,c,f,g)}function i(l,c){if(!d.isUndefined(c))return s(void 0,c)}function o(l,c){if(d.isUndefined(c)){if(!d.isUndefined(l))return s(void 0,l)}else return s(void 0,c)}function a(l,c,f){if(f in e)return s(l,c);if(f in n)return s(void 0,l)}const u={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(l,c,f)=>r(rt(l),rt(c),f,!0)};return d.forEach(Object.keys(Object.assign({},n,e)),function(c){const f=u[c]||r,g=f(n[c],e[c],c);d.isUndefined(g)&&f!==a||(t[c]=g)}),t}const zt=n=>{const e=W({},n);let{data:t,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:i,headers:o,auth:a}=e;e.headers=o=F.from(o),e.url=Bt(Kt(e.baseURL,e.url,e.allowAbsoluteUrls),n.params,n.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(d.isFormData(t)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((u=o.getContentType())!==!1){const[l,...c]=u?u.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([l||"multipart/form-data",...c].join("; "))}}if(T.hasStandardBrowserEnv&&(s&&d.isFunction(s)&&(s=s(e)),s||s!==!1&&Fs(e.url))){const l=r&&i&&ks.read(i);l&&o.set(r,l)}return e},vs=typeof XMLHttpRequest<"u",Ds=vs&&function(n){return new Promise(function(t,s){const r=zt(n);let i=r.data;const o=F.from(r.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:l}=r,c,f,g,m,h;function y(){m&&m(),h&&h(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let p=new XMLHttpRequest;p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout;function w(){if(!p)return;const E=F.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),R={data:!a||a==="text"||a==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:E,config:n,request:p};Vt(function(C){t(C),y()},function(C){s(C),y()},R),p=null}"onloadend"in p?p.onloadend=w:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(w)},p.onabort=function(){p&&(s(new x("Request aborted",x.ECONNABORTED,n,p)),p=null)},p.onerror=function(){s(new x("Network Error",x.ERR_NETWORK,n,p)),p=null},p.ontimeout=function(){let O=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const R=r.transitional||_t;r.timeoutErrorMessage&&(O=r.timeoutErrorMessage),s(new x(O,R.clarifyTimeoutError?x.ETIMEDOUT:x.ECONNABORTED,n,p)),p=null},i===void 0&&o.setContentType(null),"setRequestHeader"in p&&d.forEach(o.toJSON(),function(O,R){p.setRequestHeader(R,O)}),d.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),a&&a!=="json"&&(p.responseType=r.responseType),l&&([g,h]=he(l,!0),p.addEventListener("progress",g)),u&&p.upload&&([f,m]=he(u),p.upload.addEventListener("progress",f),p.upload.addEventListener("loadend",m)),(r.cancelToken||r.signal)&&(c=E=>{p&&(s(!E||E.type?new Q(null,n,p):E),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const S=Ts(r.url);if(S&&T.protocols.indexOf(S)===-1){s(new x("Unsupported protocol "+S+":",x.ERR_BAD_REQUEST,n));return}p.send(i||null)})},Us=(n,e)=>{const{length:t}=n=n?n.filter(Boolean):[];if(e||t){let s=new AbortController,r;const i=function(l){if(!r){r=!0,a();const c=l instanceof Error?l:this.reason;s.abort(c instanceof x?c:new Q(c instanceof Error?c.message:c))}};let o=e&&setTimeout(()=>{o=null,i(new x(`timeout ${e} of ms exceeded`,x.ETIMEDOUT))},e);const a=()=>{n&&(o&&clearTimeout(o),o=null,n.forEach(l=>{l.unsubscribe?l.unsubscribe(i):l.removeEventListener("abort",i)}),n=null)};n.forEach(l=>l.addEventListener("abort",i));const{signal:u}=s;return u.unsubscribe=()=>d.asap(a),u}},Is=function*(n,e){let t=n.byteLength;if(t<e){yield n;return}let s=0,r;for(;s<t;)r=s+e,yield n.slice(s,r),s=r},Bs=async function*(n,e){for await(const t of _s(n))yield*Is(t,e)},_s=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const e=n.getReader();try{for(;;){const{done:t,value:s}=await e.read();if(t)break;yield s}}finally{await e.cancel()}},it=(n,e,t,s)=>{const r=Bs(n,e);let i=0,o,a=u=>{o||(o=!0,s&&s(u))};return new ReadableStream({async pull(u){try{const{done:l,value:c}=await r.next();if(l){a(),u.close();return}let f=c.byteLength;if(t){let g=i+=f;t(g)}u.enqueue(new Uint8Array(c))}catch(l){throw a(l),l}},cancel(u){return a(u),r.return()}},{highWaterMark:2})},Re=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",qt=Re&&typeof ReadableStream=="function",Ms=Re&&(typeof TextEncoder=="function"?(n=>e=>n.encode(e))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),Jt=(n,...e)=>{try{return!!n(...e)}catch{return!1}},Hs=qt&&Jt(()=>{let n=!1;const e=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!e}),ot=64*1024,De=qt&&Jt(()=>d.isReadableStream(new Response("").body)),pe={stream:De&&(n=>n.body)};Re&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!pe[e]&&(pe[e]=d.isFunction(n[e])?t=>t[e]():(t,s)=>{throw new x(`Response type '${e}' is not supported`,x.ERR_NOT_SUPPORT,s)})})})(new Response);const Vs=async n=>{if(n==null)return 0;if(d.isBlob(n))return n.size;if(d.isSpecCompliantForm(n))return(await new Request(T.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(d.isArrayBufferView(n)||d.isArrayBuffer(n))return n.byteLength;if(d.isURLSearchParams(n)&&(n=n+""),d.isString(n))return(await Ms(n)).byteLength},Ks=async(n,e)=>{const t=d.toFiniteNumber(n.getContentLength());return t??Vs(e)},zs=Re&&(async n=>{let{url:e,method:t,data:s,signal:r,cancelToken:i,timeout:o,onDownloadProgress:a,onUploadProgress:u,responseType:l,headers:c,withCredentials:f="same-origin",fetchOptions:g}=zt(n);l=l?(l+"").toLowerCase():"text";let m=Us([r,i&&i.toAbortSignal()],o),h;const y=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let p;try{if(u&&Hs&&t!=="get"&&t!=="head"&&(p=await Ks(c,s))!==0){let R=new Request(e,{method:"POST",body:s,duplex:"half"}),L;if(d.isFormData(s)&&(L=R.headers.get("content-type"))&&c.setContentType(L),R.body){const[C,V]=nt(p,he(st(u)));s=it(R.body,ot,C,V)}}d.isString(f)||(f=f?"include":"omit");const w="credentials"in Request.prototype;h=new Request(e,{...g,signal:m,method:t.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:w?f:void 0});let S=await fetch(h,g);const E=De&&(l==="stream"||l==="response");if(De&&(a||E&&y)){const R={};["status","statusText","headers"].forEach($=>{R[$]=S[$]});const L=d.toFiniteNumber(S.headers.get("content-length")),[C,V]=a&&nt(L,he(st(a),!0))||[];S=new Response(it(S.body,ot,C,()=>{V&&V(),y&&y()}),R)}l=l||"text";let O=await pe[d.findKey(pe,l)||"text"](S,n);return!E&&y&&y(),await new Promise((R,L)=>{Vt(R,L,{data:O,headers:F.from(S.headers),status:S.status,statusText:S.statusText,config:n,request:h})})}catch(w){throw y&&y(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new x("Network Error",x.ERR_NETWORK,n,h),{cause:w.cause||w}):x.from(w,w&&w.code,n,h)}}),Ue={http:os,xhr:Ds,fetch:zs};d.forEach(Ue,(n,e)=>{if(n){try{Object.defineProperty(n,"name",{value:e})}catch{}Object.defineProperty(n,"adapterName",{value:e})}});const at=n=>`- ${n}`,qs=n=>d.isFunction(n)||n===null||n===!1,Wt={getAdapter:n=>{n=d.isArray(n)?n:[n];const{length:e}=n;let t,s;const r={};for(let i=0;i<e;i++){t=n[i];let o;if(s=t,!qs(t)&&(s=Ue[(o=String(t)).toLowerCase()],s===void 0))throw new x(`Unknown adapter '${o}'`);if(s)break;r[o||"#"+i]=s}if(!s){const i=Object.entries(r).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=e?i.length>1?`since :
`+i.map(at).join(`
`):" "+at(i[0]):"as no adapter specified";throw new x("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Ue};function Te(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new Q(null,n)}function lt(n){return Te(n),n.headers=F.from(n.headers),n.data=Ne.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),Wt.getAdapter(n.adapter||ae.adapter)(n).then(function(s){return Te(n),s.data=Ne.call(n,n.transformResponse,s),s.headers=F.from(s.headers),s},function(s){return Ht(s)||(Te(n),s&&s.response&&(s.response.data=Ne.call(n,n.transformResponse,s.response),s.response.headers=F.from(s.response.headers))),Promise.reject(s)})}const Gt="1.10.0",Ee={};["object","boolean","number","function","string","symbol"].forEach((n,e)=>{Ee[n]=function(s){return typeof s===n||"a"+(e<1?"n ":" ")+n}});const ut={};Ee.transitional=function(e,t,s){function r(i,o){return"[Axios v"+Gt+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,a)=>{if(e===!1)throw new x(r(o," has been removed"+(t?" in "+t:"")),x.ERR_DEPRECATED);return t&&!ut[o]&&(ut[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(i,o,a):!0}};Ee.spelling=function(e){return(t,s)=>(console.warn(`${s} is likely a misspelling of ${e}`),!0)};function Js(n,e,t){if(typeof n!="object")throw new x("options must be an object",x.ERR_BAD_OPTION_VALUE);const s=Object.keys(n);let r=s.length;for(;r-- >0;){const i=s[r],o=e[i];if(o){const a=n[i],u=a===void 0||o(a,i,n);if(u!==!0)throw new x("option "+i+" must be "+u,x.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new x("Unknown option "+i,x.ERR_BAD_OPTION)}}const de={assertOptions:Js,validators:Ee},B=de.validators;let J=class{constructor(e){this.defaults=e||{},this.interceptors={request:new et,response:new et}}async request(e,t){try{return await this._request(e,t)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const i=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=W(this.defaults,t);const{transitional:s,paramsSerializer:r,headers:i}=t;s!==void 0&&de.assertOptions(s,{silentJSONParsing:B.transitional(B.boolean),forcedJSONParsing:B.transitional(B.boolean),clarifyTimeoutError:B.transitional(B.boolean)},!1),r!=null&&(d.isFunction(r)?t.paramsSerializer={serialize:r}:de.assertOptions(r,{encode:B.function,serialize:B.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),de.assertOptions(t,{baseUrl:B.spelling("baseURL"),withXsrfToken:B.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&d.merge(i.common,i[t.method]);i&&d.forEach(["delete","get","head","post","put","patch","common"],h=>{delete i[h]}),t.headers=F.concat(o,i);const a=[];let u=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(t)===!1||(u=u&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let c,f=0,g;if(!u){const h=[lt.bind(this),void 0];for(h.unshift.apply(h,a),h.push.apply(h,l),g=h.length,c=Promise.resolve(t);f<g;)c=c.then(h[f++],h[f++]);return c}g=a.length;let m=t;for(f=0;f<g;){const h=a[f++],y=a[f++];try{m=h(m)}catch(p){y.call(this,p);break}}try{c=lt.call(this,m)}catch(h){return Promise.reject(h)}for(f=0,g=l.length;f<g;)c=c.then(l[f++],l[f++]);return c}getUri(e){e=W(this.defaults,e);const t=Kt(e.baseURL,e.url,e.allowAbsoluteUrls);return Bt(t,e.params,e.paramsSerializer)}};d.forEach(["delete","get","head","options"],function(e){J.prototype[e]=function(t,s){return this.request(W(s||{},{method:e,url:t,data:(s||{}).data}))}});d.forEach(["post","put","patch"],function(e){function t(s){return function(i,o,a){return this.request(W(a||{},{method:e,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}J.prototype[e]=t(),J.prototype[e+"Form"]=t(!0)});let Ws=class Xt{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(i){t=i});const s=this;this.promise.then(r=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](r);s._listeners=null}),this.promise.then=r=>{let i;const o=new Promise(a=>{s.subscribe(a),i=a}).then(r);return o.cancel=function(){s.unsubscribe(i)},o},e(function(i,o,a){s.reason||(s.reason=new Q(i,o,a),t(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=s=>{e.abort(s)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Xt(function(r){e=r}),cancel:e}}};function Gs(n){return function(t){return n.apply(null,t)}}function Xs(n){return d.isObject(n)&&n.isAxiosError===!0}const Ie={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ie).forEach(([n,e])=>{Ie[e]=n});function Qt(n){const e=new J(n),t=Nt(J.prototype.request,e);return d.extend(t,J.prototype,e,{allOwnKeys:!0}),d.extend(t,e,null,{allOwnKeys:!0}),t.create=function(r){return Qt(W(n,r))},t}const N=Qt(ae);N.Axios=J;N.CanceledError=Q;N.CancelToken=Ws;N.isCancel=Ht;N.VERSION=Gt;N.toFormData=Oe;N.AxiosError=x;N.Cancel=N.CanceledError;N.all=function(e){return Promise.all(e)};N.spread=Gs;N.isAxiosError=Xs;N.mergeConfig=W;N.AxiosHeaders=F;N.formToJSON=n=>Mt(d.isHTMLForm(n)?new FormData(n):n);N.getAdapter=Wt.getAdapter;N.HttpStatusCode=Ie;N.default=N;const{Axios:Or,AxiosError:Rr,CanceledError:Er,isCancel:Lr,CancelToken:Cr,VERSION:Nr,all:Tr,Cancel:Pr,isAxiosError:Ar,spread:Fr,toFormData:kr,AxiosHeaders:$r,HttpStatusCode:jr,formToJSON:vr,getAdapter:Dr,mergeConfig:Ur}=N,b=n=>typeof n=="string",te=()=>{let n,e;const t=new Promise((s,r)=>{n=s,e=r});return t.resolve=n,t.reject=e,t},ct=n=>n==null?"":""+n,Qs=(n,e,t)=>{n.forEach(s=>{e[s]&&(t[s]=e[s])})},Ys=/###/g,ft=n=>n&&n.indexOf("###")>-1?n.replace(Ys,"."):n,dt=n=>!n||b(n),ne=(n,e,t)=>{const s=b(e)?e.split("."):e;let r=0;for(;r<s.length-1;){if(dt(n))return{};const i=ft(s[r]);!n[i]&&t&&(n[i]=new t),Object.prototype.hasOwnProperty.call(n,i)?n=n[i]:n={},++r}return dt(n)?{}:{obj:n,k:ft(s[r])}},ht=(n,e,t)=>{const{obj:s,k:r}=ne(n,e,Object);if(s!==void 0||e.length===1){s[r]=t;return}let i=e[e.length-1],o=e.slice(0,e.length-1),a=ne(n,o,Object);for(;a.obj===void 0&&o.length;)i=`${o[o.length-1]}.${i}`,o=o.slice(0,o.length-1),a=ne(n,o,Object),a?.obj&&typeof a.obj[`${a.k}.${i}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${i}`]=t},Zs=(n,e,t,s)=>{const{obj:r,k:i}=ne(n,e,Object);r[i]=r[i]||[],r[i].push(t)},ge=(n,e)=>{const{obj:t,k:s}=ne(n,e);if(t&&Object.prototype.hasOwnProperty.call(t,s))return t[s]},er=(n,e,t)=>{const s=ge(n,t);return s!==void 0?s:ge(e,t)},Yt=(n,e,t)=>{for(const s in e)s!=="__proto__"&&s!=="constructor"&&(s in n?b(n[s])||n[s]instanceof String||b(e[s])||e[s]instanceof String?t&&(n[s]=e[s]):Yt(n[s],e[s],t):n[s]=e[s]);return n},G=n=>n.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var tr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const nr=n=>b(n)?n.replace(/[&<>"'\/]/g,e=>tr[e]):n;class sr{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}const rr=[" ",",","?","!",";"],ir=new sr(20),or=(n,e,t)=>{e=e||"",t=t||"";const s=rr.filter(o=>e.indexOf(o)<0&&t.indexOf(o)<0);if(s.length===0)return!0;const r=ir.getRegExp(`(${s.map(o=>o==="?"?"\\?":o).join("|")})`);let i=!r.test(n);if(!i){const o=n.indexOf(t);o>0&&!r.test(n.substring(0,o))&&(i=!0)}return i},Be=(n,e,t=".")=>{if(!n)return;if(n[e])return Object.prototype.hasOwnProperty.call(n,e)?n[e]:void 0;const s=e.split(t);let r=n;for(let i=0;i<s.length;){if(!r||typeof r!="object")return;let o,a="";for(let u=i;u<s.length;++u)if(u!==i&&(a+=t),a+=s[u],o=r[a],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&u<s.length-1)continue;i+=u-i+1;break}r=o}return r},re=n=>n?.replace("_","-"),ar={type:"logger",log(n){this.output("log",n)},warn(n){this.output("warn",n)},error(n){this.output("error",n)},output(n,e){console?.[n]?.apply?.(console,e)}};class me{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||ar,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,r){return r&&!this.debug?null:(b(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new me(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new me(this.logger,e)}}var _=new me;class Le{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const r=this.observers[s].get(t)||0;this.observers[s].set(t,r+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([r,i])=>{for(let o=0;o<i;o++)r(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([r,i])=>{for(let o=0;o<i;o++)r.apply(r,[e,...t])})}}class pt extends Le{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s,r={}){const i=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator,o=r.ignoreJSONStructure!==void 0?r.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],s&&(Array.isArray(s)?a.push(...s):b(s)&&i?a.push(...s.split(i)):a.push(s)));const u=ge(this.data,a);return!u&&!t&&!s&&e.indexOf(".")>-1&&(e=a[0],t=a[1],s=a.slice(2).join(".")),u||!o||!b(s)?u:Be(this.data?.[e]?.[t],s,i)}addResource(e,t,s,r,i={silent:!1}){const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let a=[e,t];s&&(a=a.concat(o?s.split(o):s)),e.indexOf(".")>-1&&(a=e.split("."),r=t,t=a[1]),this.addNamespaces(t),ht(this.data,a,r),i.silent||this.emit("added",e,t,s,r)}addResources(e,t,s,r={silent:!1}){for(const i in s)(b(s[i])||Array.isArray(s[i]))&&this.addResource(e,t,i,s[i],{silent:!0});r.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,r,i,o={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),r=s,s=t,t=a[1]),this.addNamespaces(t);let u=ge(this.data,a)||{};o.skipCopy||(s=JSON.parse(JSON.stringify(s))),r?Yt(u,s,i):u={...u,...s},ht(this.data,a,u),o.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(r=>t[r]&&Object.keys(t[r]).length>0)}toJSON(){return this.data}}var Zt={processors:{},addPostProcessor(n){this.processors[n.name]=n},handle(n,e,t,s,r){return n.forEach(i=>{e=this.processors[i]?.process(e,t,s,r)??e}),e}};const gt={},mt=n=>!b(n)&&typeof n!="boolean"&&typeof n!="number";class ye extends Le{constructor(e,t={}){super(),Qs(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=_.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const s={...t};return e==null?!1:this.resolve(e,s)?.res!==void 0}extractFromKey(e,t){let s=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const r=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const o=s&&e.indexOf(s)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!or(e,s,r);if(o&&!a){const u=e.match(this.interpolator.nestingRegexp);if(u&&u.length>0)return{key:e,namespaces:b(i)?[i]:i};const l=e.split(s);(s!==r||s===r&&this.options.ns.indexOf(l[0])>-1)&&(i=l.shift()),e=l.join(r)}return{key:e,namespaces:b(i)?[i]:i}}translate(e,t,s){let r=typeof t=="object"?{...t}:t;if(typeof r!="object"&&this.options.overloadTranslationOptionHandler&&(r=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(r={...r}),r||(r={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=r.returnDetails!==void 0?r.returnDetails:this.options.returnDetails,o=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator,{key:a,namespaces:u}=this.extractFromKey(e[e.length-1],r),l=u[u.length-1];let c=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;c===void 0&&(c=":");const f=r.lng||this.language,g=r.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(f?.toLowerCase()==="cimode")return g?i?{res:`${l}${c}${a}`,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:l,usedParams:this.getUsedParamsDetails(r)}:`${l}${c}${a}`:i?{res:a,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:l,usedParams:this.getUsedParamsDetails(r)}:a;const m=this.resolve(e,r);let h=m?.res;const y=m?.usedKey||a,p=m?.exactUsedKey||a,w=["[object Number]","[object Function]","[object RegExp]"],S=r.joinArrays!==void 0?r.joinArrays:this.options.joinArrays,E=!this.i18nFormat||this.i18nFormat.handleAsObject,O=r.count!==void 0&&!b(r.count),R=ye.hasDefaultValue(r),L=O?this.pluralResolver.getSuffix(f,r.count,r):"",C=r.ordinal&&O?this.pluralResolver.getSuffix(f,r.count,{ordinal:!1}):"",V=O&&!r.ordinal&&r.count===0,$=V&&r[`defaultValue${this.options.pluralSeparator}zero`]||r[`defaultValue${L}`]||r[`defaultValue${C}`]||r.defaultValue;let D=h;E&&!h&&R&&(D=$);const en=mt(D),tn=Object.prototype.toString.apply(D);if(E&&D&&en&&w.indexOf(tn)<0&&!(b(S)&&Array.isArray(D))){if(!r.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const U=this.options.returnedObjectHandler?this.options.returnedObjectHandler(y,D,{...r,ns:u}):`key '${a} (${this.language})' returned an object instead of string.`;return i?(m.res=U,m.usedParams=this.getUsedParamsDetails(r),m):U}if(o){const U=Array.isArray(D),j=U?[]:{},Ve=U?p:y;for(const I in D)if(Object.prototype.hasOwnProperty.call(D,I)){const M=`${Ve}${o}${I}`;R&&!h?j[I]=this.translate(M,{...r,defaultValue:mt($)?$[I]:void 0,joinArrays:!1,ns:u}):j[I]=this.translate(M,{...r,joinArrays:!1,ns:u}),j[I]===M&&(j[I]=D[I])}h=j}}else if(E&&b(S)&&Array.isArray(h))h=h.join(S),h&&(h=this.extendTranslation(h,e,r,s));else{let U=!1,j=!1;!this.isValidLookup(h)&&R&&(U=!0,h=$),this.isValidLookup(h)||(j=!0,h=a);const I=(r.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&j?void 0:h,M=R&&$!==h&&this.options.updateMissing;if(j||U||M){if(this.logger.log(M?"updateKey":"missingKey",f,l,a,M?$:h),o){const k=this.resolve(a,{...r,keySeparator:!1});k&&k.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let Y=[];const le=this.languageUtils.getFallbackCodes(this.options.fallbackLng,r.lng||this.language);if(this.options.saveMissingTo==="fallback"&&le&&le[0])for(let k=0;k<le.length;k++)Y.push(le[k]);else this.options.saveMissingTo==="all"?Y=this.languageUtils.toResolveHierarchy(r.lng||this.language):Y.push(r.lng||this.language);const Ke=(k,K,Z)=>{const ze=R&&Z!==h?Z:I;this.options.missingKeyHandler?this.options.missingKeyHandler(k,l,K,ze,M,r):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(k,l,K,ze,M,r),this.emit("missingKey",k,l,K,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&O?Y.forEach(k=>{const K=this.pluralResolver.getSuffixes(k,r);V&&r[`defaultValue${this.options.pluralSeparator}zero`]&&K.indexOf(`${this.options.pluralSeparator}zero`)<0&&K.push(`${this.options.pluralSeparator}zero`),K.forEach(Z=>{Ke([k],a+Z,r[`defaultValue${Z}`]||$)})}):Ke(Y,a,$))}h=this.extendTranslation(h,e,r,m,s),j&&h===a&&this.options.appendNamespaceToMissingKey&&(h=`${l}${c}${a}`),(j||U)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}${c}${a}`:a,U?h:void 0,r))}return i?(m.res=h,m.usedParams=this.getUsedParamsDetails(r),m):h}extendTranslation(e,t,s,r,i){if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const u=b(e)&&(s?.interpolation?.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let l;if(u){const f=e.match(this.interpolator.nestingRegexp);l=f&&f.length}let c=s.replace&&!b(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(c={...this.options.interpolation.defaultVariables,...c}),e=this.interpolator.interpolate(e,c,s.lng||this.language||r.usedLng,s),u){const f=e.match(this.interpolator.nestingRegexp),g=f&&f.length;l<g&&(s.nest=!1)}!s.lng&&r&&r.res&&(s.lng=this.language||r.usedLng),s.nest!==!1&&(e=this.interpolator.nest(e,(...f)=>i?.[0]===f[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${f[0]} in key: ${t[0]}`),null):this.translate(...f,t),s)),s.interpolation&&this.interpolator.reset()}const o=s.postProcess||this.options.postProcess,a=b(o)?[o]:o;return e!=null&&a?.length&&s.applyPostProcessor!==!1&&(e=Zt.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e,t={}){let s,r,i,o,a;return b(e)&&(e=[e]),e.forEach(u=>{if(this.isValidLookup(s))return;const l=this.extractFromKey(u,t),c=l.key;r=c;let f=l.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const g=t.count!==void 0&&!b(t.count),m=g&&!t.ordinal&&t.count===0,h=t.context!==void 0&&(b(t.context)||typeof t.context=="number")&&t.context!=="",y=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);f.forEach(p=>{this.isValidLookup(s)||(a=p,!gt[`${y[0]}-${p}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(a)&&(gt[`${y[0]}-${p}`]=!0,this.logger.warn(`key "${r}" for languages "${y.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),y.forEach(w=>{if(this.isValidLookup(s))return;o=w;const S=[c];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(S,c,w,p,t);else{let O;g&&(O=this.pluralResolver.getSuffix(w,t.count,t));const R=`${this.options.pluralSeparator}zero`,L=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(S.push(c+O),t.ordinal&&O.indexOf(L)===0&&S.push(c+O.replace(L,this.options.pluralSeparator)),m&&S.push(c+R)),h){const C=`${c}${this.options.contextSeparator}${t.context}`;S.push(C),g&&(S.push(C+O),t.ordinal&&O.indexOf(L)===0&&S.push(C+O.replace(L,this.options.pluralSeparator)),m&&S.push(C+R))}}let E;for(;E=S.pop();)this.isValidLookup(s)||(i=E,s=this.getResource(w,p,E,t))}))})}),{res:s,usedKey:r,exactUsedKey:i,usedLng:o,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,s,r={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,s,r):this.resourceStore.getResource(e,t,s,r)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=e.replace&&!b(e.replace);let r=s?e.replace:e;if(s&&typeof e.count<"u"&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!s){r={...r};for(const i of t)delete r[i]}return r}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&e[s]!==void 0)return!0;return!1}}class yt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=_.create("languageUtils")}getScriptPartFromCode(e){if(e=re(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=re(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(b(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(s=>{if(t)return;const r=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(r))&&(t=r)}),!t&&this.options.supportedLngs&&e.forEach(s=>{if(t)return;const r=this.getScriptPartFromCode(s);if(this.isSupportedCode(r))return t=r;const i=this.getLanguagePartFromCode(s);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(o=>{if(o===i)return o;if(!(o.indexOf("-")<0&&i.indexOf("-")<0)&&(o.indexOf("-")>0&&i.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===i||o.indexOf(i)===0&&i.length>1))return o})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),b(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){const s=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),r=[],i=o=>{o&&(this.isSupportedCode(o)?r.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return b(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):b(e)&&i(this.formatLanguageCode(e)),s.forEach(o=>{r.indexOf(o)<0&&i(this.formatLanguageCode(o))}),r}}const bt={zero:0,one:1,two:2,few:3,many:4,other:5},St={select:n=>n===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class lr{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=_.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const s=re(e==="dev"?"en":e),r=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:s,type:r});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let o;try{o=new Intl.PluralRules(s,{type:r})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),St;if(!e.match(/-|_/))return St;const u=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(u,t)}return this.pluralRulesCache[i]=o,o}needsPlural(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t,s={}){return this.getSuffixes(e,s).map(r=>`${t}${r}`)}getSuffixes(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?s.resolvedOptions().pluralCategories.sort((r,i)=>bt[r]-bt[i]).map(r=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${r}`):[]}getSuffix(e,t,s={}){const r=this.getRule(e,s);return r?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}const xt=(n,e,t,s=".",r=!0)=>{let i=er(n,e,t);return!i&&r&&b(t)&&(i=Be(n,t,s),i===void 0&&(i=Be(e,t,s))),i},Pe=n=>n.replace(/\$/g,"$$$$");class ur{constructor(e={}){this.logger=_.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(t=>t),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:r,prefix:i,prefixEscaped:o,suffix:a,suffixEscaped:u,formatSeparator:l,unescapeSuffix:c,unescapePrefix:f,nestingPrefix:g,nestingPrefixEscaped:m,nestingSuffix:h,nestingSuffixEscaped:y,nestingOptionsSeparator:p,maxReplaces:w,alwaysFormat:S}=e.interpolation;this.escape=t!==void 0?t:nr,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=r!==void 0?r:!1,this.prefix=i?G(i):o||"{{",this.suffix=a?G(a):u||"}}",this.formatSeparator=l||",",this.unescapePrefix=c?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=g?G(g):m||G("$t("),this.nestingSuffix=h?G(h):y||G(")"),this.nestingOptionsSeparator=p||",",this.maxReplaces=w||1e3,this.alwaysFormat=S!==void 0?S:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,s)=>t?.source===s?(t.lastIndex=0,t):new RegExp(s,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,r){let i,o,a;const u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=m=>{if(m.indexOf(this.formatSeparator)<0){const w=xt(t,u,m,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(w,void 0,s,{...r,...t,interpolationkey:m}):w}const h=m.split(this.formatSeparator),y=h.shift().trim(),p=h.join(this.formatSeparator).trim();return this.format(xt(t,u,y,this.options.keySeparator,this.options.ignoreJSONStructure),p,s,{...r,...t,interpolationkey:y})};this.resetRegExp();const c=r?.missingInterpolationHandler||this.options.missingInterpolationHandler,f=r?.interpolation?.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:m=>Pe(m)},{regex:this.regexp,safeValue:m=>this.escapeValue?Pe(this.escape(m)):Pe(m)}].forEach(m=>{for(a=0;i=m.regex.exec(e);){const h=i[1].trim();if(o=l(h),o===void 0)if(typeof c=="function"){const p=c(e,i,r);o=b(p)?p:""}else if(r&&Object.prototype.hasOwnProperty.call(r,h))o="";else if(f){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${h} for interpolating ${e}`),o="";else!b(o)&&!this.useRawValueToEscape&&(o=ct(o));const y=m.safeValue(o);if(e=e.replace(i[0],y),f?(m.regex.lastIndex+=o.length,m.regex.lastIndex-=i[0].length):m.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t,s={}){let r,i,o;const a=(u,l)=>{const c=this.nestingOptionsSeparator;if(u.indexOf(c)<0)return u;const f=u.split(new RegExp(`${c}[ ]*{`));let g=`{${f[1]}`;u=f[0],g=this.interpolate(g,o);const m=g.match(/'/g),h=g.match(/"/g);((m?.length??0)%2===0&&!h||h.length%2!==0)&&(g=g.replace(/'/g,'"'));try{o=JSON.parse(g),l&&(o={...l,...o})}catch(y){return this.logger.warn(`failed parsing options string in nesting for key ${u}`,y),`${u}${c}${g}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,u};for(;r=this.nestingRegexp.exec(e);){let u=[];o={...s},o=o.replace&&!b(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;const l=/{.*}/.test(r[1])?r[1].lastIndexOf("}")+1:r[1].indexOf(this.formatSeparator);if(l!==-1&&(u=r[1].slice(l).split(this.formatSeparator).map(c=>c.trim()).filter(Boolean),r[1]=r[1].slice(0,l)),i=t(a.call(this,r[1].trim(),o),o),i&&r[0]===e&&!b(i))return i;b(i)||(i=ct(i)),i||(this.logger.warn(`missed to resolve ${r[1]} for nesting ${e}`),i=""),u.length&&(i=u.reduce((c,f)=>this.format(c,f,s.lng,{...s,interpolationkey:r[1].trim()}),i.trim())),e=e.replace(r[0],i),this.regexp.lastIndex=0}return e}}const cr=n=>{let e=n.toLowerCase().trim();const t={};if(n.indexOf("(")>-1){const s=n.split("(");e=s[0].toLowerCase().trim();const r=s[1].substring(0,s[1].length-1);e==="currency"&&r.indexOf(":")<0?t.currency||(t.currency=r.trim()):e==="relativetime"&&r.indexOf(":")<0?t.range||(t.range=r.trim()):r.split(";").forEach(o=>{if(o){const[a,...u]=o.split(":"),l=u.join(":").trim().replace(/^'+|'+$/g,""),c=a.trim();t[c]||(t[c]=l),l==="false"&&(t[c]=!1),l==="true"&&(t[c]=!0),isNaN(l)||(t[c]=parseInt(l,10))}})}return{formatName:e,formatOptions:t}},wt=n=>{const e={};return(t,s,r)=>{let i=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(i={...i,[r.interpolationkey]:void 0});const o=s+JSON.stringify(i);let a=e[o];return a||(a=n(re(s),r),e[o]=a),a(t)}},fr=n=>(e,t,s)=>n(re(t),s)(e);class dr{constructor(e={}){this.logger=_.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const s=t.cacheInBuiltFormats?wt:fr;this.formats={number:s((r,i)=>{const o=new Intl.NumberFormat(r,{...i});return a=>o.format(a)}),currency:s((r,i)=>{const o=new Intl.NumberFormat(r,{...i,style:"currency"});return a=>o.format(a)}),datetime:s((r,i)=>{const o=new Intl.DateTimeFormat(r,{...i});return a=>o.format(a)}),relativetime:s((r,i)=>{const o=new Intl.RelativeTimeFormat(r,{...i});return a=>o.format(a,i.range||"day")}),list:s((r,i)=>{const o=new Intl.ListFormat(r,{...i});return a=>o.format(a)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=wt(t)}format(e,t,s,r={}){const i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(a=>a.indexOf(")")>-1)){const a=i.findIndex(u=>u.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,a)].join(this.formatSeparator)}return i.reduce((a,u)=>{const{formatName:l,formatOptions:c}=cr(u);if(this.formats[l]){let f=a;try{const g=r?.formatParams?.[r.interpolationkey]||{},m=g.locale||g.lng||r.locale||r.lng||s;f=this.formats[l](a,m,{...c,...r,...g})}catch(g){this.logger.warn(g)}return f}else this.logger.warn(`there was no format function for ${l}`);return a},e)}}const hr=(n,e)=>{n.pending[e]!==void 0&&(delete n.pending[e],n.pendingCount--)};class pr extends Le{constructor(e,t,s,r={}){super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=r,this.logger=_.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(s,r.backend,r)}queueLoad(e,t,s,r){const i={},o={},a={},u={};return e.forEach(l=>{let c=!0;t.forEach(f=>{const g=`${l}|${f}`;!s.reload&&this.store.hasResourceBundle(l,f)?this.state[g]=2:this.state[g]<0||(this.state[g]===1?o[g]===void 0&&(o[g]=!0):(this.state[g]=1,c=!1,o[g]===void 0&&(o[g]=!0),i[g]===void 0&&(i[g]=!0),u[f]===void 0&&(u[f]=!0)))}),c||(a[l]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(u)}}loaded(e,t,s){const r=e.split("|"),i=r[0],o=r[1];t&&this.emit("failedLoading",i,o,t),!t&&s&&this.store.addResourceBundle(i,o,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const a={};this.queue.forEach(u=>{Zs(u.loaded,[i],o),hr(u,e),t&&u.errors.push(t),u.pendingCount===0&&!u.done&&(Object.keys(u.loaded).forEach(l=>{a[l]||(a[l]={});const c=u.loaded[l];c.length&&c.forEach(f=>{a[l][f]===void 0&&(a[l][f]=!0)})}),u.done=!0,u.errors.length?u.callback(u.errors):u.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(u=>!u.done)}read(e,t,s,r=0,i=this.retryTimeout,o){if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:s,tried:r,wait:i,callback:o});return}this.readingCalls++;const a=(l,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(l&&c&&r<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,s,r+1,i*2,o)},i);return}o(l,c)},u=this.backend[s].bind(this.backend);if(u.length===2){try{const l=u(e,t);l&&typeof l.then=="function"?l.then(c=>a(null,c)).catch(a):a(null,l)}catch(l){a(l)}return}return u(e,t,a)}prepareLoading(e,t,s={},r){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();b(e)&&(e=this.languageUtils.toResolveHierarchy(e)),b(t)&&(t=[t]);const i=this.queueLoad(e,t,s,r);if(!i.toLoad.length)return i.pending.length||r(),null;i.toLoad.forEach(o=>{this.loadOne(o)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e,t=""){const s=e.split("|"),r=s[0],i=s[1];this.read(r,i,"read",void 0,void 0,(o,a)=>{o&&this.logger.warn(`${t}loading namespace ${i} for language ${r} failed`,o),!o&&a&&this.logger.log(`${t}loaded namespace ${i} for language ${r}`,a),this.loaded(e,o,a)})}saveMissing(e,t,s,r,i,o={},a=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if(this.backend?.create){const u={...o,isUpdate:i},l=this.backend.create.bind(this.backend);if(l.length<6)try{let c;l.length===5?c=l(e,t,s,r,u):c=l(e,t,s,r),c&&typeof c.then=="function"?c.then(f=>a(null,f)).catch(a):a(null,c)}catch(c){a(c)}else l(e,t,s,r,a,u)}!e||!e[0]||this.store.addResource(e[0],t,s,r)}}}const Ot=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:n=>{let e={};if(typeof n[1]=="object"&&(e=n[1]),b(n[1])&&(e.defaultValue=n[1]),b(n[2])&&(e.tDescription=n[2]),typeof n[2]=="object"||typeof n[3]=="object"){const t=n[3]||n[2];Object.keys(t).forEach(s=>{e[s]=t[s]})}return e},interpolation:{escapeValue:!0,format:n=>n,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Rt=n=>(b(n.ns)&&(n.ns=[n.ns]),b(n.fallbackLng)&&(n.fallbackLng=[n.fallbackLng]),b(n.fallbackNS)&&(n.fallbackNS=[n.fallbackNS]),n.supportedLngs?.indexOf?.("cimode")<0&&(n.supportedLngs=n.supportedLngs.concat(["cimode"])),typeof n.initImmediate=="boolean"&&(n.initAsync=n.initImmediate),n),ue=()=>{},gr=n=>{Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach(t=>{typeof n[t]=="function"&&(n[t]=n[t].bind(n))})};class ie extends Le{constructor(e={},t){if(super(),this.options=Rt(e),this.services={},this.logger=_,this.modules={external:[]},gr(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(b(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const s=Ot();this.options={...s,...this.options,...Rt(e)},this.options.interpolation={...s.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const r=l=>l?typeof l=="function"?new l:l:null;if(!this.options.isClone){this.modules.logger?_.init(r(this.modules.logger),this.options):_.init(null,this.options);let l;this.modules.formatter?l=this.modules.formatter:l=dr;const c=new yt(this.options);this.store=new pt(this.options.resources,this.options);const f=this.services;f.logger=_,f.resourceStore=this.store,f.languageUtils=c,f.pluralResolver=new lr(c,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),l&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(f.formatter=r(l),f.formatter.init&&f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new ur(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new pr(r(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",(m,...h)=>{this.emit(m,...h)}),this.modules.languageDetector&&(f.languageDetector=r(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=r(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new ye(this.services,this.options),this.translator.on("*",(m,...h)=>{this.emit(m,...h)}),this.modules.external.forEach(m=>{m.init&&m.init(this)})}if(this.format=this.options.interpolation.format,t||(t=ue),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const l=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);l.length>0&&l[0]!=="dev"&&(this.options.lng=l[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(l=>{this[l]=(...c)=>this.store[l](...c)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(l=>{this[l]=(...c)=>(this.store[l](...c),this)});const a=te(),u=()=>{const l=(c,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(f),t(c,f)};if(this.languages&&!this.isInitialized)return l(null,this.t.bind(this));this.changeLanguage(this.options.lng,l)};return this.options.resources||!this.options.initAsync?u():setTimeout(u,0),a}loadResources(e,t=ue){let s=t;const r=b(e)?e:this.language;if(typeof e=="function"&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if(r?.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const i=[],o=a=>{if(!a||a==="cimode")return;this.services.languageUtils.toResolveHierarchy(a).forEach(l=>{l!=="cimode"&&i.indexOf(l)<0&&i.push(l)})};r?o(r):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>o(u)),this.options.preload?.forEach?.(a=>o(a)),this.services.backendConnector.load(i,this.options.ns,a=>{!a&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(a)})}else s(null)}reloadResources(e,t,s){const r=te();return typeof e=="function"&&(s=e,e=void 0),typeof t=="function"&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=ue),this.services.backendConnector.reload(e,t,i=>{r.resolve(),s(i)}),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Zt.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const s=this.languages[t];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const s=te();this.emit("languageChanging",e);const r=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},i=(a,u)=>{u?this.isLanguageChangingTo===e&&(r(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,s.resolve((...l)=>this.t(...l)),t&&t(a,(...l)=>this.t(...l))},o=a=>{!e&&!a&&this.services.languageDetector&&(a=[]);const u=b(a)?a:a&&a[0],l=this.store.hasLanguageSomeTranslations(u)?u:this.services.languageUtils.getBestMatchFromCodes(b(a)?[a]:a);l&&(this.language||r(l),this.translator.language||this.translator.changeLanguage(l),this.services.languageDetector?.cacheUserLanguage?.(l)),this.loadResources(l,c=>{i(c,l)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),s}getFixedT(e,t,s){const r=(i,o,...a)=>{let u;typeof o!="object"?u=this.options.overloadTranslationOptionHandler([i,o].concat(a)):u={...o},u.lng=u.lng||r.lng,u.lngs=u.lngs||r.lngs,u.ns=u.ns||r.ns,u.keyPrefix!==""&&(u.keyPrefix=u.keyPrefix||s||r.keyPrefix);const l=this.options.keySeparator||".";let c;return u.keyPrefix&&Array.isArray(i)?c=i.map(f=>`${u.keyPrefix}${l}${f}`):c=u.keyPrefix?`${u.keyPrefix}${l}${i}`:i,this.t(c,u)};return b(e)?r.lng=e:r.lngs=e,r.ns=t,r.keyPrefix=s,r}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],r=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const o=(a,u)=>{const l=this.services.backendConnector.state[`${a}|${u}`];return l===-1||l===0||l===2};if(t.precheck){const a=t.precheck(this,o);if(a!==void 0)return a}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(s,e)&&(!r||o(i,e)))}loadNamespaces(e,t){const s=te();return this.options.ns?(b(e)&&(e=[e]),e.forEach(r=>{this.options.ns.indexOf(r)<0&&this.options.ns.push(r)}),this.loadResources(r=>{s.resolve(),t&&t(r)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){const s=te();b(e)&&(e=[e]);const r=this.options.preload||[],i=e.filter(o=>r.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return i.length?(this.options.preload=r.concat(i),this.loadResources(o=>{s.resolve(),t&&t(o)}),s):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";try{const r=new Intl.Locale(e);if(r&&r.getTextInfo){const i=r.getTextInfo();if(i&&i.direction)return i.direction}}catch{}const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=this.services?.languageUtils||new yt(Ot());return e.toLowerCase().indexOf("-latn")>1?"ltr":t.indexOf(s.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new ie(e,t)}cloneInstance(e={},t=ue){const s=e.forkResourceStore;s&&delete e.forkResourceStore;const r={...this.options,...e,isClone:!0},i=new ie(r);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(a=>{i[a]=this[a]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},s){const a=Object.keys(this.store.data).reduce((u,l)=>(u[l]={...this.store.data[l]},u[l]=Object.keys(u[l]).reduce((c,f)=>(c[f]={...u[l][f]},c),u[l]),u),{});i.store=new pt(a,r),i.services.resourceStore=i.store}return i.translator=new ye(i.services,r),i.translator.on("*",(a,...u)=>{i.emit(a,...u)}),i.init(r,t),i.translator.options=r,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const P=ie.createInstance();P.createInstance=ie.createInstance;P.createInstance;P.dir;P.init;P.loadResources;P.reloadResources;P.use;P.changeLanguage;P.getFixedT;P.t;P.exists;P.setDefaultNamespace;P.hasLoadedNamespace;P.loadNamespaces;P.loadLanguages;export{yr as a,N as b,Sr as c,P as i,br as u};

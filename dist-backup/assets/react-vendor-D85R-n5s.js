function Ar(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const i=Object.getOwnPropertyDescriptor(n,a);i&&Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function $r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tt={exports:{}},H={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sr;function Sn(){if(sr)return H;sr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),o=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),v=Symbol.iterator;function g(d){return d===null||typeof d!="object"?null:(d=v&&d[v]||d["@@iterator"],typeof d=="function"?d:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},S=Object.assign,x={};function P(d,E,N){this.props=d,this.context=E,this.refs=x,this.updater=N||w}P.prototype.isReactComponent={},P.prototype.setState=function(d,E){if(typeof d!="object"&&typeof d!="function"&&d!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,d,E,"setState")},P.prototype.forceUpdate=function(d){this.updater.enqueueForceUpdate(this,d,"forceUpdate")};function R(){}R.prototype=P.prototype;function z(d,E,N){this.props=d,this.context=E,this.refs=x,this.updater=N||w}var L=z.prototype=new R;L.constructor=z,S(L,P.prototype),L.isPureReactComponent=!0;var V=Array.isArray,D={H:null,A:null,T:null,S:null,V:null},y=Object.prototype.hasOwnProperty;function K(d,E,N,A,j,G){return N=G.ref,{$$typeof:e,type:d,key:E,ref:N!==void 0?N:null,props:G}}function Z(d,E){return K(d.type,E,void 0,void 0,void 0,d.props)}function B(d){return typeof d=="object"&&d!==null&&d.$$typeof===e}function ie(d){var E={"=":"=0",":":"=2"};return"$"+d.replace(/[=:]/g,function(N){return E[N]})}var he=/\/+/g;function Ee(d,E){return typeof d=="object"&&d!==null&&d.key!=null?ie(""+d.key):E.toString(36)}function q(){}function X(d){switch(d.status){case"fulfilled":return d.value;case"rejected":throw d.reason;default:switch(typeof d.status=="string"?d.then(q,q):(d.status="pending",d.then(function(E){d.status==="pending"&&(d.status="fulfilled",d.value=E)},function(E){d.status==="pending"&&(d.status="rejected",d.reason=E)})),d.status){case"fulfilled":return d.value;case"rejected":throw d.reason}}throw d}function ae(d,E,N,A,j){var G=typeof d;(G==="undefined"||G==="boolean")&&(d=null);var k=!1;if(d===null)k=!0;else switch(G){case"bigint":case"string":case"number":k=!0;break;case"object":switch(d.$$typeof){case e:case t:k=!0;break;case h:return k=d._init,ae(k(d._payload),E,N,A,j)}}if(k)return j=j(d),k=A===""?"."+Ee(d,0):A,V(j)?(N="",k!=null&&(N=k.replace(he,"$&/")+"/"),ae(j,E,N,"",function(Pt){return Pt})):j!=null&&(B(j)&&(j=Z(j,N+(j.key==null||d&&d.key===j.key?"":(""+j.key).replace(he,"$&/")+"/")+k)),E.push(j)),1;k=0;var Ce=A===""?".":A+":";if(V(d))for(var ne=0;ne<d.length;ne++)A=d[ne],G=Ce+Ee(A,ne),k+=ae(A,E,N,G,j);else if(ne=g(d),typeof ne=="function")for(d=ne.call(d),ne=0;!(A=d.next()).done;)A=A.value,G=Ce+Ee(A,ne++),k+=ae(A,E,N,G,j);else if(G==="object"){if(typeof d.then=="function")return ae(X(d),E,N,A,j);throw E=String(d),Error("Objects are not valid as a React child (found: "+(E==="[object Object]"?"object with keys {"+Object.keys(d).join(", ")+"}":E)+"). If you meant to render a collection of children, use an array instead.")}return k}function W(d,E,N){if(d==null)return d;var A=[],j=0;return ae(d,A,"","",function(G){return E.call(N,G,j++)}),A}function me(d){if(d._status===-1){var E=d._result;E=E(),E.then(function(N){(d._status===0||d._status===-1)&&(d._status=1,d._result=N)},function(N){(d._status===0||d._status===-1)&&(d._status=2,d._result=N)}),d._status===-1&&(d._status=0,d._result=E)}if(d._status===1)return d._result.default;throw d._result}var le=typeof reportError=="function"?reportError:function(d){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var E=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof d=="object"&&d!==null&&typeof d.message=="string"?String(d.message):String(d),error:d});if(!window.dispatchEvent(E))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",d);return}console.error(d)};function ye(){}return H.Children={map:W,forEach:function(d,E,N){W(d,function(){E.apply(this,arguments)},N)},count:function(d){var E=0;return W(d,function(){E++}),E},toArray:function(d){return W(d,function(E){return E})||[]},only:function(d){if(!B(d))throw Error("React.Children.only expected to receive a single React element child.");return d}},H.Component=P,H.Fragment=r,H.Profiler=a,H.PureComponent=z,H.StrictMode=n,H.Suspense=l,H.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=D,H.__COMPILER_RUNTIME={__proto__:null,c:function(d){return D.H.useMemoCache(d)}},H.cache=function(d){return function(){return d.apply(null,arguments)}},H.cloneElement=function(d,E,N){if(d==null)throw Error("The argument must be a React element, but you passed "+d+".");var A=S({},d.props),j=d.key,G=void 0;if(E!=null)for(k in E.ref!==void 0&&(G=void 0),E.key!==void 0&&(j=""+E.key),E)!y.call(E,k)||k==="key"||k==="__self"||k==="__source"||k==="ref"&&E.ref===void 0||(A[k]=E[k]);var k=arguments.length-2;if(k===1)A.children=N;else if(1<k){for(var Ce=Array(k),ne=0;ne<k;ne++)Ce[ne]=arguments[ne+2];A.children=Ce}return K(d.type,j,void 0,void 0,G,A)},H.createContext=function(d){return d={$$typeof:s,_currentValue:d,_currentValue2:d,_threadCount:0,Provider:null,Consumer:null},d.Provider=d,d.Consumer={$$typeof:i,_context:d},d},H.createElement=function(d,E,N){var A,j={},G=null;if(E!=null)for(A in E.key!==void 0&&(G=""+E.key),E)y.call(E,A)&&A!=="key"&&A!=="__self"&&A!=="__source"&&(j[A]=E[A]);var k=arguments.length-2;if(k===1)j.children=N;else if(1<k){for(var Ce=Array(k),ne=0;ne<k;ne++)Ce[ne]=arguments[ne+2];j.children=Ce}if(d&&d.defaultProps)for(A in k=d.defaultProps,k)j[A]===void 0&&(j[A]=k[A]);return K(d,G,void 0,void 0,null,j)},H.createRef=function(){return{current:null}},H.forwardRef=function(d){return{$$typeof:c,render:d}},H.isValidElement=B,H.lazy=function(d){return{$$typeof:h,_payload:{_status:-1,_result:d},_init:me}},H.memo=function(d,E){return{$$typeof:o,type:d,compare:E===void 0?null:E}},H.startTransition=function(d){var E=D.T,N={};D.T=N;try{var A=d(),j=D.S;j!==null&&j(N,A),typeof A=="object"&&A!==null&&typeof A.then=="function"&&A.then(ye,le)}catch(G){le(G)}finally{D.T=E}},H.unstable_useCacheRefresh=function(){return D.H.useCacheRefresh()},H.use=function(d){return D.H.use(d)},H.useActionState=function(d,E,N){return D.H.useActionState(d,E,N)},H.useCallback=function(d,E){return D.H.useCallback(d,E)},H.useContext=function(d){return D.H.useContext(d)},H.useDebugValue=function(){},H.useDeferredValue=function(d,E){return D.H.useDeferredValue(d,E)},H.useEffect=function(d,E,N){var A=D.H;if(typeof N=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return A.useEffect(d,E)},H.useId=function(){return D.H.useId()},H.useImperativeHandle=function(d,E,N){return D.H.useImperativeHandle(d,E,N)},H.useInsertionEffect=function(d,E){return D.H.useInsertionEffect(d,E)},H.useLayoutEffect=function(d,E){return D.H.useLayoutEffect(d,E)},H.useMemo=function(d,E){return D.H.useMemo(d,E)},H.useOptimistic=function(d,E){return D.H.useOptimistic(d,E)},H.useReducer=function(d,E,N){return D.H.useReducer(d,E,N)},H.useRef=function(d){return D.H.useRef(d)},H.useState=function(d){return D.H.useState(d)},H.useSyncExternalStore=function(d,E,N){return D.H.useSyncExternalStore(d,E,N)},H.useTransition=function(){return D.H.useTransition()},H.version="19.1.0",H}var ur;function Fr(){return ur||(ur=1,Tt.exports=Sn()),Tt.exports}var m=Fr();const Pn=$r(m),$o=Ar({__proto__:null,default:Pn},[m]);var Mt={exports:{}},de={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cr;function xn(){if(cr)return de;cr=1;var e=Fr();function t(l){var o="https://react.dev/errors/"+l;if(1<arguments.length){o+="?args[]="+encodeURIComponent(arguments[1]);for(var h=2;h<arguments.length;h++)o+="&args[]="+encodeURIComponent(arguments[h])}return"Minified React error #"+l+"; visit "+o+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var n={d:{f:r,r:function(){throw Error(t(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},a=Symbol.for("react.portal");function i(l,o,h){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:a,key:v==null?null:""+v,children:l,containerInfo:o,implementation:h}}var s=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(l,o){if(l==="font")return"";if(typeof o=="string")return o==="use-credentials"?o:""}return de.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=n,de.createPortal=function(l,o){var h=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!o||o.nodeType!==1&&o.nodeType!==9&&o.nodeType!==11)throw Error(t(299));return i(l,o,null,h)},de.flushSync=function(l){var o=s.T,h=n.p;try{if(s.T=null,n.p=2,l)return l()}finally{s.T=o,n.p=h,n.d.f()}},de.preconnect=function(l,o){typeof l=="string"&&(o?(o=o.crossOrigin,o=typeof o=="string"?o==="use-credentials"?o:"":void 0):o=null,n.d.C(l,o))},de.prefetchDNS=function(l){typeof l=="string"&&n.d.D(l)},de.preinit=function(l,o){if(typeof l=="string"&&o&&typeof o.as=="string"){var h=o.as,v=c(h,o.crossOrigin),g=typeof o.integrity=="string"?o.integrity:void 0,w=typeof o.fetchPriority=="string"?o.fetchPriority:void 0;h==="style"?n.d.S(l,typeof o.precedence=="string"?o.precedence:void 0,{crossOrigin:v,integrity:g,fetchPriority:w}):h==="script"&&n.d.X(l,{crossOrigin:v,integrity:g,fetchPriority:w,nonce:typeof o.nonce=="string"?o.nonce:void 0})}},de.preinitModule=function(l,o){if(typeof l=="string")if(typeof o=="object"&&o!==null){if(o.as==null||o.as==="script"){var h=c(o.as,o.crossOrigin);n.d.M(l,{crossOrigin:h,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0})}}else o==null&&n.d.M(l)},de.preload=function(l,o){if(typeof l=="string"&&typeof o=="object"&&o!==null&&typeof o.as=="string"){var h=o.as,v=c(h,o.crossOrigin);n.d.L(l,h,{crossOrigin:v,integrity:typeof o.integrity=="string"?o.integrity:void 0,nonce:typeof o.nonce=="string"?o.nonce:void 0,type:typeof o.type=="string"?o.type:void 0,fetchPriority:typeof o.fetchPriority=="string"?o.fetchPriority:void 0,referrerPolicy:typeof o.referrerPolicy=="string"?o.referrerPolicy:void 0,imageSrcSet:typeof o.imageSrcSet=="string"?o.imageSrcSet:void 0,imageSizes:typeof o.imageSizes=="string"?o.imageSizes:void 0,media:typeof o.media=="string"?o.media:void 0})}},de.preloadModule=function(l,o){if(typeof l=="string")if(o){var h=c(o.as,o.crossOrigin);n.d.m(l,{as:typeof o.as=="string"&&o.as!=="script"?o.as:void 0,crossOrigin:h,integrity:typeof o.integrity=="string"?o.integrity:void 0})}else n.d.m(l)},de.requestFormReset=function(l){n.d.r(l)},de.unstable_batchedUpdates=function(l,o){return l(o)},de.useFormState=function(l,o,h){return s.H.useFormState(l,o,h)},de.useFormStatus=function(){return s.H.useHostTransitionStatus()},de.version="19.1.0",de}var dr;function _n(){if(dr)return Mt.exports;dr=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}return e(),Mt.exports=xn(),Mt.exports}var zt=_n();const Ln=$r(zt),Fo=Ar({__proto__:null,default:Ln},[zt]);/**
 * react-router v7.7.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Ur=e=>{throw TypeError(e)},Dn=(e,t,r)=>t.has(e)||Ur("Cannot "+r),Ot=(e,t,r)=>(Dn(e,t,"read from private field"),r?r.call(e):t.get(e)),Tn=(e,t,r)=>t.has(e)?Ur("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),fr="popstate";function Mn(e={}){function t(a,i){let{pathname:s="/",search:c="",hash:l=""}=_e(a.location.hash.substring(1));return!s.startsWith("/")&&!s.startsWith(".")&&(s="/"+s),et("",{pathname:s,search:c,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function r(a,i){let s=a.document.querySelector("base"),c="";if(s&&s.getAttribute("href")){let l=a.location.href,o=l.indexOf("#");c=o===-1?l:l.slice(0,o)}return c+"#"+(typeof i=="string"?i:Ne(i))}function n(a,i){re(a.pathname.charAt(0)==="/",`relative pathnames are not supported in hash history.push(${JSON.stringify(i)})`)}return Nn(t,r,n,e)}function I(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function re(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function On(){return Math.random().toString(36).substring(2,10)}function hr(e,t){return{usr:e.state,key:e.key,idx:t}}function et(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?_e(t):t,state:r,key:t&&t.key||n||On()}}function Ne({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function _e(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function Nn(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:i=!1}=n,s=a.history,c="POP",l=null,o=h();o==null&&(o=0,s.replaceState({...s.state,idx:o},""));function h(){return(s.state||{idx:null}).idx}function v(){c="POP";let P=h(),R=P==null?null:P-o;o=P,l&&l({action:c,location:x.location,delta:R})}function g(P,R){c="PUSH";let z=et(x.location,P,R);r&&r(z,P),o=h()+1;let L=hr(z,o),V=x.createHref(z);try{s.pushState(L,"",V)}catch(D){if(D instanceof DOMException&&D.name==="DataCloneError")throw D;a.location.assign(V)}i&&l&&l({action:c,location:x.location,delta:1})}function w(P,R){c="REPLACE";let z=et(x.location,P,R);r&&r(z,P),o=h();let L=hr(z,o),V=x.createHref(z);s.replaceState(L,"",V),i&&l&&l({action:c,location:x.location,delta:0})}function S(P){return kr(P)}let x={get action(){return c},get location(){return e(a,s)},listen(P){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(fr,v),l=P,()=>{a.removeEventListener(fr,v),l=null}},createHref(P){return t(a,P)},createURL:S,encodeLocation(P){let R=S(P);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:g,replace:w,go(P){return s.go(P)}};return x}function kr(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),I(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:Ne(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Ze,mr=class{constructor(e){if(Tn(this,Ze,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Ot(this,Ze).has(e))return Ot(this,Ze).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Ot(this,Ze).set(e,t)}};Ze=new WeakMap;var An=new Set(["lazy","caseSensitive","path","id","index","children"]);function $n(e){return An.has(e)}var Fn=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Un(e){return Fn.has(e)}function kn(e){return e.index===!0}function tt(e,t,r=[],n={},a=!1){return e.map((i,s)=>{let c=[...r,String(s)],l=typeof i.id=="string"?i.id:c.join("-");if(I(i.index!==!0||!i.children,"Cannot specify children on an index route"),I(a||!n[l],`Found a route id collision on id "${l}".  Route id's must be globally unique within Data Router usages`),kn(i)){let o={...i,...t(i),id:l};return n[l]=o,o}else{let o={...i,...t(i),id:l,children:void 0};return n[l]=o,i.children&&(o.children=tt(i.children,t,c,n,a)),o}})}function Oe(e,t,r="/"){return mt(e,t,r,!1)}function mt(e,t,r,n){let a=typeof t=="string"?_e(t):t,i=we(a.pathname||"/",r);if(i==null)return null;let s=Hr(e);jn(s);let c=null;for(let l=0;c==null&&l<s.length;++l){let o=Xn(i);c=Jn(s[l],o,n)}return c}function Hn(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function Hr(e,t=[],r=[],n=""){let a=(i,s,c)=>{let l={relativePath:c===void 0?i.path||"":c,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(I(l.relativePath.startsWith(n),`Absolute route path "${l.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(n.length));let o=Pe([n,l.relativePath]),h=r.concat(l);i.children&&i.children.length>0&&(I(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${o}".`),Hr(i.children,t,h,o)),!(i.path==null&&!i.index)&&t.push({path:o,score:Kn(o,i.index),routesMeta:h})};return e.forEach((i,s)=>{if(i.path===""||!i.path?.includes("?"))a(i,s);else for(let c of jr(i.path))a(i,s,c)}),t}function jr(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return a?[i,""]:[i];let s=jr(n.join("/")),c=[];return c.push(...s.map(l=>l===""?i:[i,l].join("/"))),a&&c.push(...s),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function jn(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:qn(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var zn=/^:[\w-]+$/,In=3,Bn=2,Wn=1,Yn=10,Vn=-2,pr=e=>e==="*";function Kn(e,t){let r=e.split("/"),n=r.length;return r.some(pr)&&(n+=Vn),t&&(n+=Bn),r.filter(a=>!pr(a)).reduce((a,i)=>a+(zn.test(i)?In:i===""?Wn:Yn),n)}function qn(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Jn(e,t,r=!1){let{routesMeta:n}=e,a={},i="/",s=[];for(let c=0;c<n.length;++c){let l=n[c],o=c===n.length-1,h=i==="/"?t:t.slice(i.length)||"/",v=gt({path:l.relativePath,caseSensitive:l.caseSensitive,end:o},h),g=l.route;if(!v&&o&&r&&!n[n.length-1].route.index&&(v=gt({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!v)return null;Object.assign(a,v.params),s.push({params:a,pathname:Pe([i,v.pathname]),pathnameBase:ta(Pe([i,v.pathnameBase])),route:g}),v.pathnameBase!=="/"&&(i=Pe([i,v.pathnameBase]))}return s}function gt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Gn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let i=a[0],s=i.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:n.reduce((o,{paramName:h,isOptional:v},g)=>{if(h==="*"){let S=c[g]||"";s=i.slice(0,i.length-S.length).replace(/(.)\/+$/,"$1")}const w=c[g];return v&&!w?o[h]=void 0:o[h]=(w||"").replace(/%2F/g,"/"),o},{}),pathname:i,pathnameBase:s,pattern:e}}function Gn(e,t=!1,r=!0){re(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,c,l)=>(n.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Xn(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return re(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function we(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Qn({basename:e,pathname:t}){return t==="/"?e:Pe([e,t])}function Zn(e,t="/"){let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?_e(e):e;return{pathname:r?r.startsWith("/")?r:ea(r,t):t,search:ra(n),hash:na(a)}}function ea(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Nt(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function zr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Rt(e){let t=zr(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function bt(e,t,r,n=!1){let a;typeof e=="string"?a=_e(e):(a={...e},I(!a.pathname||!a.pathname.includes("?"),Nt("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),Nt("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),Nt("#","search","hash",a)));let i=e===""||a.pathname==="",s=i?"/":a.pathname,c;if(s==null)c=r;else{let v=t.length-1;if(!n&&s.startsWith("..")){let g=s.split("/");for(;g[0]==="..";)g.shift(),v-=1;a.pathname=g.join("/")}c=v>=0?t[v]:"/"}let l=Zn(a,c),o=s&&s!=="/"&&s.endsWith("/"),h=(i||s===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(o||h)&&(l.pathname+="/"),l}var Pe=e=>e.join("/").replace(/\/\/+/g,"/"),ta=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ra=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,na=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,wt=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function rt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Ir=["POST","PUT","PATCH","DELETE"],aa=new Set(Ir),oa=["GET",...Ir],ia=new Set(oa),la=new Set([301,302,303,307,308]),sa=new Set([307,308]),At={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ua={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ge={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ca=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,It=e=>ca.test(e),da=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Br="remix-router-transitions",Wr=Symbol("ResetLoaderData");function fa(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";I(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],a=e.mapRouteProperties||da,i={},s=tt(e.routes,a,void 0,i),c,l=e.basename||"/",o=e.dataStrategy||va,h={unstable_middleware:!1,...e.future},v=null,g=new Set,w=null,S=null,x=null,P=e.hydrationData!=null,R=Oe(s,e.history.location,l),z=!1,L=null,V;if(R==null&&!e.patchRoutesOnNavigation){let u=ge(404,{pathname:e.history.location.pathname}),{matches:f,route:p}=_r(s);V=!0,R=f,L={[p.id]:u}}else if(R&&!e.hydrationData&&it(R,s,e.history.location.pathname).active&&(R=null),R)if(R.some(u=>u.route.lazy))V=!1;else if(!R.some(u=>u.route.loader))V=!0;else{let u=e.hydrationData?e.hydrationData.loaderData:null,f=e.hydrationData?e.hydrationData.errors:null;if(f){let p=R.findIndex(b=>f[b.route.id]!==void 0);V=R.slice(0,p+1).every(b=>!kt(b.route,u,f))}else V=R.every(p=>!kt(p.route,u,f))}else{V=!1,R=[];let u=it(null,s,e.history.location.pathname);u.active&&u.matches&&(z=!0,R=u.matches)}let D,y={historyAction:e.history.action,location:e.history.location,matches:R,initialized:V,navigation:At,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||L,fetchers:new Map,blockers:new Map},K="POP",Z=!1,B,ie=!1,he=new Map,Ee=null,q=!1,X=!1,ae=new Set,W=new Map,me=0,le=-1,ye=new Map,d=new Set,E=new Map,N=new Map,A=new Set,j=new Map,G,k=null;function Ce(){if(v=e.history.listen(({action:u,location:f,delta:p})=>{if(G){G(),G=void 0;return}re(j.size===0||p!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let b=ar({currentLocation:y.location,nextLocation:f,historyAction:u});if(b&&p!=null){let C=new Promise(T=>{G=T});e.history.go(p*-1),ot(b,{state:"blocked",location:f,proceed(){ot(b,{state:"proceeding",proceed:void 0,reset:void 0,location:f}),C.then(()=>e.history.go(p))},reset(){let T=new Map(y.blockers);T.set(b,Ge),se({blockers:T})}});return}return $e(u,f)}),r){La(t,he);let u=()=>Da(t,he);t.addEventListener("pagehide",u),Ee=()=>t.removeEventListener("pagehide",u)}return y.initialized||$e("POP",y.location,{initialHydration:!0}),D}function ne(){v&&v(),Ee&&Ee(),g.clear(),B&&B.abort(),y.fetchers.forEach((u,f)=>_t(f)),y.blockers.forEach((u,f)=>nr(f))}function Pt(u){return g.add(u),()=>g.delete(u)}function se(u,f={}){u.matches&&(u.matches=u.matches.map(C=>{let T=i[C.route.id],M=C.route;return M.element!==T.element||M.errorElement!==T.errorElement||M.hydrateFallbackElement!==T.hydrateFallbackElement?{...C,route:T}:C})),y={...y,...u};let p=[],b=[];y.fetchers.forEach((C,T)=>{C.state==="idle"&&(A.has(T)?p.push(T):b.push(T))}),A.forEach(C=>{!y.fetchers.has(C)&&!W.has(C)&&p.push(C)}),[...g].forEach(C=>C(y,{deletedFetchers:p,viewTransitionOpts:f.viewTransitionOpts,flushSync:f.flushSync===!0})),p.forEach(C=>_t(C)),b.forEach(C=>y.fetchers.delete(C))}function Ie(u,f,{flushSync:p}={}){let b=y.actionData!=null&&y.navigation.formMethod!=null&&pe(y.navigation.formMethod)&&y.navigation.state==="loading"&&u.state?._isRedirect!==!0,C;f.actionData?Object.keys(f.actionData).length>0?C=f.actionData:C=null:b?C=y.actionData:C=null;let T=f.loaderData?Pr(y.loaderData,f.loaderData,f.matches||[],f.errors):y.loaderData,M=y.blockers;M.size>0&&(M=new Map(M),M.forEach((O,F)=>M.set(F,Ge)));let _=q?!1:ir(u,f.matches||y.matches),$=Z===!0||y.navigation.formMethod!=null&&pe(y.navigation.formMethod)&&u.state?._isRedirect!==!0;c&&(s=c,c=void 0),q||K==="POP"||(K==="PUSH"?e.history.push(u,u.state):K==="REPLACE"&&e.history.replace(u,u.state));let U;if(K==="POP"){let O=he.get(y.location.pathname);O&&O.has(u.pathname)?U={currentLocation:y.location,nextLocation:u}:he.has(u.pathname)&&(U={currentLocation:u,nextLocation:y.location})}else if(ie){let O=he.get(y.location.pathname);O?O.add(u.pathname):(O=new Set([u.pathname]),he.set(y.location.pathname,O)),U={currentLocation:y.location,nextLocation:u}}se({...f,actionData:C,loaderData:T,historyAction:K,location:u,initialized:!0,navigation:At,revalidation:"idle",restoreScrollPosition:_,preventScrollReset:$,blockers:M},{viewTransitionOpts:U,flushSync:p===!0}),K="POP",Z=!1,ie=!1,q=!1,X=!1,k?.resolve(),k=null}async function Gt(u,f){if(typeof u=="number"){e.history.go(u);return}let p=Ut(y.location,y.matches,l,u,f?.fromRouteId,f?.relative),{path:b,submission:C,error:T}=yr(!1,p,f),M=y.location,_=et(y.location,b,f&&f.state);_={..._,...e.history.encodeLocation(_)};let $=f&&f.replace!=null?f.replace:void 0,U="PUSH";$===!0?U="REPLACE":$===!1||C!=null&&pe(C.formMethod)&&C.formAction===y.location.pathname+y.location.search&&(U="REPLACE");let O=f&&"preventScrollReset"in f?f.preventScrollReset===!0:void 0,F=(f&&f.flushSync)===!0,Y=ar({currentLocation:M,nextLocation:_,historyAction:U});if(Y){ot(Y,{state:"blocked",location:_,proceed(){ot(Y,{state:"proceeding",proceed:void 0,reset:void 0,location:_}),Gt(u,f)},reset(){let ee=new Map(y.blockers);ee.set(Y,Ge),se({blockers:ee})}});return}await $e(U,_,{submission:C,pendingError:T,preventScrollReset:O,replace:f&&f.replace,enableViewTransition:f&&f.viewTransition,flushSync:F})}function cn(){k||(k=Ta()),xt(),se({revalidation:"loading"});let u=k.promise;return y.navigation.state==="submitting"?u:y.navigation.state==="idle"?($e(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),u):($e(K||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:ie===!0}),u)}async function $e(u,f,p){B&&B.abort(),B=null,K=u,q=(p&&p.startUninterruptedRevalidation)===!0,En(y.location,y.matches),Z=(p&&p.preventScrollReset)===!0,ie=(p&&p.enableViewTransition)===!0;let b=c||s,C=p&&p.overrideNavigation,T=p?.initialHydration&&y.matches&&y.matches.length>0&&!z?y.matches:Oe(b,f,l),M=(p&&p.flushSync)===!0;if(T&&y.initialized&&!X&&Ca(y.location,f)&&!(p&&p.submission&&pe(p.submission.formMethod))){Ie(f,{matches:T},{flushSync:M});return}let _=it(T,b,f.pathname);if(_.active&&_.matches&&(T=_.matches),!T){let{error:fe,notFoundMatches:te,route:Q}=Lt(f.pathname);Ie(f,{matches:te,loaderData:{},errors:{[Q.id]:fe}},{flushSync:M});return}B=new AbortController;let $=Be(e.history,f,B.signal,p&&p.submission),U=new mr(e.unstable_getContext?await e.unstable_getContext():void 0),O;if(p&&p.pendingError)O=[He(T).route.id,{type:"error",error:p.pendingError}];else if(p&&p.submission&&pe(p.submission.formMethod)){let fe=await dn($,f,p.submission,T,U,_.active,p&&p.initialHydration===!0,{replace:p.replace,flushSync:M});if(fe.shortCircuited)return;if(fe.pendingActionResult){let[te,Q]=fe.pendingActionResult;if(ve(Q)&&rt(Q.error)&&Q.error.status===404){B=null,Ie(f,{matches:fe.matches,loaderData:{},errors:{[te]:Q.error}});return}}T=fe.matches||T,O=fe.pendingActionResult,C=$t(f,p.submission),M=!1,_.active=!1,$=Be(e.history,$.url,$.signal)}let{shortCircuited:F,matches:Y,loaderData:ee,errors:ue}=await fn($,f,T,U,_.active,C,p&&p.submission,p&&p.fetcherSubmission,p&&p.replace,p&&p.initialHydration===!0,M,O);F||(B=null,Ie(f,{matches:Y||T,...xr(O),loaderData:ee,errors:ue}))}async function dn(u,f,p,b,C,T,M,_={}){xt();let $=xa(f,p);if(se({navigation:$},{flushSync:_.flushSync===!0}),T){let F=await lt(b,f.pathname,u.signal);if(F.type==="aborted")return{shortCircuited:!0};if(F.type==="error"){let Y=He(F.partialMatches).route.id;return{matches:F.partialMatches,pendingActionResult:[Y,{type:"error",error:F.error}]}}else if(F.matches)b=F.matches;else{let{notFoundMatches:Y,error:ee,route:ue}=Lt(f.pathname);return{matches:Y,pendingActionResult:[ue.id,{type:"error",error:ee}]}}}let U,O=pt(b,f);if(!O.route.action&&!O.route.lazy)U={type:"error",error:ge(405,{method:u.method,pathname:f.pathname,routeId:O.route.id})};else{let F=We(a,i,u,b,O,M?[]:n,C),Y=await Ve(u,F,C,null);if(U=Y[O.route.id],!U){for(let ee of b)if(Y[ee.route.id]){U=Y[ee.route.id];break}}if(u.signal.aborted)return{shortCircuited:!0}}if(je(U)){let F;return _&&_.replace!=null?F=_.replace:F=br(U.response.headers.get("Location"),new URL(u.url),l)===y.location.pathname+y.location.search,await Fe(u,U,!0,{submission:p,replace:F}),{shortCircuited:!0}}if(ve(U)){let F=He(b,O.route.id);return(_&&_.replace)!==!0&&(K="PUSH"),{matches:b,pendingActionResult:[F.route.id,U,O.route.id]}}return{matches:b,pendingActionResult:[O.route.id,U]}}async function fn(u,f,p,b,C,T,M,_,$,U,O,F){let Y=T||$t(f,M),ee=M||_||Dr(Y),ue=!q&&!U;if(C){if(ue){let ce=Xt(F);se({navigation:Y,...ce!==void 0?{actionData:ce}:{}},{flushSync:O})}let J=await lt(p,f.pathname,u.signal);if(J.type==="aborted")return{shortCircuited:!0};if(J.type==="error"){let ce=He(J.partialMatches).route.id;return{matches:J.partialMatches,loaderData:{},errors:{[ce]:J.error}}}else if(J.matches)p=J.matches;else{let{error:ce,notFoundMatches:ct,route:Je}=Lt(f.pathname);return{matches:ct,loaderData:{},errors:{[Je.id]:ce}}}}let fe=c||s,{dsMatches:te,revalidatingFetchers:Q}=vr(u,b,a,i,e.history,y,p,ee,f,U?[]:n,U===!0,X,ae,A,E,d,fe,l,e.patchRoutesOnNavigation!=null,F);if(le=++me,!e.dataStrategy&&!te.some(J=>J.shouldLoad)&&Q.length===0){let J=tr();return Ie(f,{matches:p,loaderData:{},errors:F&&ve(F[1])?{[F[0]]:F[1].error}:null,...xr(F),...J?{fetchers:new Map(y.fetchers)}:{}},{flushSync:O}),{shortCircuited:!0}}if(ue){let J={};if(!C){J.navigation=Y;let ce=Xt(F);ce!==void 0&&(J.actionData=ce)}Q.length>0&&(J.fetchers=hn(Q)),se(J,{flushSync:O})}Q.forEach(J=>{De(J.key),J.controller&&W.set(J.key,J.controller)});let Ue=()=>Q.forEach(J=>De(J.key));B&&B.signal.addEventListener("abort",Ue);let{loaderResults:Ke,fetcherResults:Te}=await Qt(te,Q,u,b);if(u.signal.aborted)return{shortCircuited:!0};B&&B.signal.removeEventListener("abort",Ue),Q.forEach(J=>W.delete(J.key));let Se=ft(Ke);if(Se)return await Fe(u,Se.result,!0,{replace:$}),{shortCircuited:!0};if(Se=ft(Te),Se)return d.add(Se.key),await Fe(u,Se.result,!0,{replace:$}),{shortCircuited:!0};let{loaderData:Dt,errors:qe}=Sr(y,p,Ke,F,Q,Te);U&&y.errors&&(qe={...y.errors,...qe});let ke=tr(),st=rr(le),ut=ke||st||Q.length>0;return{matches:p,loaderData:Dt,errors:qe,...ut?{fetchers:new Map(y.fetchers)}:{}}}function Xt(u){if(u&&!ve(u[1]))return{[u[0]]:u[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function hn(u){return u.forEach(f=>{let p=y.fetchers.get(f.key),b=Xe(void 0,p?p.data:void 0);y.fetchers.set(f.key,b)}),new Map(y.fetchers)}async function mn(u,f,p,b){De(u);let C=(b&&b.flushSync)===!0,T=c||s,M=Ut(y.location,y.matches,l,p,f,b?.relative),_=Oe(T,M,l),$=it(_,T,M);if($.active&&$.matches&&(_=$.matches),!_){xe(u,f,ge(404,{pathname:M}),{flushSync:C});return}let{path:U,submission:O,error:F}=yr(!0,M,b);if(F){xe(u,f,F,{flushSync:C});return}let Y=new mr(e.unstable_getContext?await e.unstable_getContext():void 0),ee=(b&&b.preventScrollReset)===!0;if(O&&pe(O.formMethod)){await pn(u,f,U,_,Y,$.active,C,ee,O);return}E.set(u,{routeId:f,path:U}),await yn(u,f,U,_,Y,$.active,C,ee,O)}async function pn(u,f,p,b,C,T,M,_,$){xt(),E.delete(u);let U=y.fetchers.get(u);Le(u,_a($,U),{flushSync:M});let O=new AbortController,F=Be(e.history,p,O.signal,$);if(T){let oe=await lt(b,new URL(F.url).pathname,F.signal,u);if(oe.type==="aborted")return;if(oe.type==="error"){xe(u,f,oe.error,{flushSync:M});return}else if(oe.matches)b=oe.matches;else{xe(u,f,ge(404,{pathname:p}),{flushSync:M});return}}let Y=pt(b,p);if(!Y.route.action&&!Y.route.lazy){let oe=ge(405,{method:$.formMethod,pathname:p,routeId:f});xe(u,f,oe,{flushSync:M});return}W.set(u,O);let ee=me,ue=We(a,i,F,b,Y,n,C),te=(await Ve(F,ue,C,u))[Y.route.id];if(F.signal.aborted){W.get(u)===O&&W.delete(u);return}if(A.has(u)){if(je(te)||ve(te)){Le(u,Me(void 0));return}}else{if(je(te))if(W.delete(u),le>ee){Le(u,Me(void 0));return}else return d.add(u),Le(u,Xe($)),Fe(F,te,!1,{fetcherSubmission:$,preventScrollReset:_});if(ve(te)){xe(u,f,te.error);return}}let Q=y.navigation.location||y.location,Ue=Be(e.history,Q,O.signal),Ke=c||s,Te=y.navigation.state!=="idle"?Oe(Ke,y.navigation.location,l):y.matches;I(Te,"Didn't find any matches after fetcher action");let Se=++me;ye.set(u,Se);let Dt=Xe($,te.data);y.fetchers.set(u,Dt);let{dsMatches:qe,revalidatingFetchers:ke}=vr(Ue,C,a,i,e.history,y,Te,$,Q,n,!1,X,ae,A,E,d,Ke,l,e.patchRoutesOnNavigation!=null,[Y.route.id,te]);ke.filter(oe=>oe.key!==u).forEach(oe=>{let dt=oe.key,lr=y.fetchers.get(dt),Cn=Xe(void 0,lr?lr.data:void 0);y.fetchers.set(dt,Cn),De(dt),oe.controller&&W.set(dt,oe.controller)}),se({fetchers:new Map(y.fetchers)});let st=()=>ke.forEach(oe=>De(oe.key));O.signal.addEventListener("abort",st);let{loaderResults:ut,fetcherResults:J}=await Qt(qe,ke,Ue,C);if(O.signal.aborted)return;if(O.signal.removeEventListener("abort",st),ye.delete(u),W.delete(u),ke.forEach(oe=>W.delete(oe.key)),y.fetchers.has(u)){let oe=Me(te.data);y.fetchers.set(u,oe)}let ce=ft(ut);if(ce)return Fe(Ue,ce.result,!1,{preventScrollReset:_});if(ce=ft(J),ce)return d.add(ce.key),Fe(Ue,ce.result,!1,{preventScrollReset:_});let{loaderData:ct,errors:Je}=Sr(y,Te,ut,void 0,ke,J);rr(Se),y.navigation.state==="loading"&&Se>le?(I(K,"Expected pending action"),B&&B.abort(),Ie(y.navigation.location,{matches:Te,loaderData:ct,errors:Je,fetchers:new Map(y.fetchers)})):(se({errors:Je,loaderData:Pr(y.loaderData,ct,Te,Je),fetchers:new Map(y.fetchers)}),X=!1)}async function yn(u,f,p,b,C,T,M,_,$){let U=y.fetchers.get(u);Le(u,Xe($,U?U.data:void 0),{flushSync:M});let O=new AbortController,F=Be(e.history,p,O.signal);if(T){let Q=await lt(b,new URL(F.url).pathname,F.signal,u);if(Q.type==="aborted")return;if(Q.type==="error"){xe(u,f,Q.error,{flushSync:M});return}else if(Q.matches)b=Q.matches;else{xe(u,f,ge(404,{pathname:p}),{flushSync:M});return}}let Y=pt(b,p);W.set(u,O);let ee=me,ue=We(a,i,F,b,Y,n,C),te=(await Ve(F,ue,C,u))[Y.route.id];if(W.get(u)===O&&W.delete(u),!F.signal.aborted){if(A.has(u)){Le(u,Me(void 0));return}if(je(te))if(le>ee){Le(u,Me(void 0));return}else{d.add(u),await Fe(F,te,!1,{preventScrollReset:_});return}if(ve(te)){xe(u,f,te.error);return}Le(u,Me(te.data))}}async function Fe(u,f,p,{submission:b,fetcherSubmission:C,preventScrollReset:T,replace:M}={}){f.response.headers.has("X-Remix-Revalidate")&&(X=!0);let _=f.response.headers.get("Location");I(_,"Expected a Location header on the redirect Response"),_=br(_,new URL(u.url),l);let $=et(y.location,_,{_isRedirect:!0});if(r){let ue=!1;if(f.response.headers.has("X-Remix-Reload-Document"))ue=!0;else if(It(_)){const fe=kr(_,!0);ue=fe.origin!==t.location.origin||we(fe.pathname,l)==null}if(ue){M?t.location.replace(_):t.location.assign(_);return}}B=null;let U=M===!0||f.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:O,formAction:F,formEncType:Y}=y.navigation;!b&&!C&&O&&F&&Y&&(b=Dr(y.navigation));let ee=b||C;if(sa.has(f.response.status)&&ee&&pe(ee.formMethod))await $e(U,$,{submission:{...ee,formAction:_},preventScrollReset:T||Z,enableViewTransition:p?ie:void 0});else{let ue=$t($,b);await $e(U,$,{overrideNavigation:ue,fetcherSubmission:C,preventScrollReset:T||Z,enableViewTransition:p?ie:void 0})}}async function Ve(u,f,p,b){let C,T={};try{C=await ga(o,u,f,b,p,!1)}catch(M){return f.filter(_=>_.shouldLoad).forEach(_=>{T[_.route.id]={type:"error",error:M}}),T}if(u.signal.aborted)return T;for(let[M,_]of Object.entries(C))if(Sa(_)){let $=_.result;T[M]={type:"redirect",response:Ra($,u,M,f,l)}}else T[M]=await Ea(_);return T}async function Qt(u,f,p,b){let C=Ve(p,u,b,null),T=Promise.all(f.map(async $=>{if($.matches&&$.match&&$.request&&$.controller){let O=(await Ve($.request,$.matches,b,$.key))[$.match.route.id];return{[$.key]:O}}else return Promise.resolve({[$.key]:{type:"error",error:ge(404,{pathname:$.path})}})})),M=await C,_=(await T).reduce(($,U)=>Object.assign($,U),{});return{loaderResults:M,fetcherResults:_}}function xt(){X=!0,E.forEach((u,f)=>{W.has(f)&&ae.add(f),De(f)})}function Le(u,f,p={}){y.fetchers.set(u,f),se({fetchers:new Map(y.fetchers)},{flushSync:(p&&p.flushSync)===!0})}function xe(u,f,p,b={}){let C=He(y.matches,f);_t(u),se({errors:{[C.route.id]:p},fetchers:new Map(y.fetchers)},{flushSync:(b&&b.flushSync)===!0})}function Zt(u){return N.set(u,(N.get(u)||0)+1),A.has(u)&&A.delete(u),y.fetchers.get(u)||ua}function _t(u){let f=y.fetchers.get(u);W.has(u)&&!(f&&f.state==="loading"&&ye.has(u))&&De(u),E.delete(u),ye.delete(u),d.delete(u),A.delete(u),ae.delete(u),y.fetchers.delete(u)}function vn(u){let f=(N.get(u)||0)-1;f<=0?(N.delete(u),A.add(u)):N.set(u,f),se({fetchers:new Map(y.fetchers)})}function De(u){let f=W.get(u);f&&(f.abort(),W.delete(u))}function er(u){for(let f of u){let p=Zt(f),b=Me(p.data);y.fetchers.set(f,b)}}function tr(){let u=[],f=!1;for(let p of d){let b=y.fetchers.get(p);I(b,`Expected fetcher: ${p}`),b.state==="loading"&&(d.delete(p),u.push(p),f=!0)}return er(u),f}function rr(u){let f=[];for(let[p,b]of ye)if(b<u){let C=y.fetchers.get(p);I(C,`Expected fetcher: ${p}`),C.state==="loading"&&(De(p),ye.delete(p),f.push(p))}return er(f),f.length>0}function gn(u,f){let p=y.blockers.get(u)||Ge;return j.get(u)!==f&&j.set(u,f),p}function nr(u){y.blockers.delete(u),j.delete(u)}function ot(u,f){let p=y.blockers.get(u)||Ge;I(p.state==="unblocked"&&f.state==="blocked"||p.state==="blocked"&&f.state==="blocked"||p.state==="blocked"&&f.state==="proceeding"||p.state==="blocked"&&f.state==="unblocked"||p.state==="proceeding"&&f.state==="unblocked",`Invalid blocker state transition: ${p.state} -> ${f.state}`);let b=new Map(y.blockers);b.set(u,f),se({blockers:b})}function ar({currentLocation:u,nextLocation:f,historyAction:p}){if(j.size===0)return;j.size>1&&re(!1,"A router only supports one blocker at a time");let b=Array.from(j.entries()),[C,T]=b[b.length-1],M=y.blockers.get(C);if(!(M&&M.state==="proceeding")&&T({currentLocation:u,nextLocation:f,historyAction:p}))return C}function Lt(u){let f=ge(404,{pathname:u}),p=c||s,{matches:b,route:C}=_r(p);return{notFoundMatches:b,route:C,error:f}}function wn(u,f,p){if(w=u,x=f,S=p||null,!P&&y.navigation===At){P=!0;let b=ir(y.location,y.matches);b!=null&&se({restoreScrollPosition:b})}return()=>{w=null,x=null,S=null}}function or(u,f){return S&&S(u,f.map(b=>Hn(b,y.loaderData)))||u.key}function En(u,f){if(w&&x){let p=or(u,f);w[p]=x()}}function ir(u,f){if(w){let p=or(u,f),b=w[p];if(typeof b=="number")return b}return null}function it(u,f,p){if(e.patchRoutesOnNavigation)if(u){if(Object.keys(u[0].params).length>0)return{active:!0,matches:mt(f,p,l,!0)}}else return{active:!0,matches:mt(f,p,l,!0)||[]};return{active:!1,matches:null}}async function lt(u,f,p,b){if(!e.patchRoutesOnNavigation)return{type:"success",matches:u};let C=u;for(;;){let T=c==null,M=c||s,_=i;try{await e.patchRoutesOnNavigation({signal:p,path:f,matches:C,fetcherKey:b,patch:(O,F)=>{p.aborted||gr(O,F,M,_,a,!1)}})}catch(O){return{type:"error",error:O,partialMatches:C}}finally{T&&!p.aborted&&(s=[...s])}if(p.aborted)return{type:"aborted"};let $=Oe(M,f,l);if($)return{type:"success",matches:$};let U=mt(M,f,l,!0);if(!U||C.length===U.length&&C.every((O,F)=>O.route.id===U[F].route.id))return{type:"success",matches:null};C=U}}function Rn(u){i={},c=tt(u,a,void 0,i)}function bn(u,f,p=!1){let b=c==null;gr(u,f,c||s,i,a,p),b&&(s=[...s],se({}))}return D={get basename(){return l},get future(){return h},get state(){return y},get routes(){return s},get window(){return t},initialize:Ce,subscribe:Pt,enableScrollRestoration:wn,navigate:Gt,fetch:mn,revalidate:cn,createHref:u=>e.history.createHref(u),encodeLocation:u=>e.history.encodeLocation(u),getFetcher:Zt,deleteFetcher:vn,dispose:ne,getBlocker:gn,deleteBlocker:nr,patchRoutes:bn,_internalFetchControllers:W,_internalSetRoutes:Rn,_internalSetStateDoNotUseOrYouWillBreakYourApp(u){se(u)}},D}function ha(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Ut(e,t,r,n,a,i){let s,c;if(a){s=[];for(let o of t)if(s.push(o),o.route.id===a){c=o;break}}else s=t,c=t[t.length-1];let l=bt(n||".",Rt(s),we(e.pathname,r)||e.pathname,i==="path");if(n==null&&(l.search=e.search,l.hash=e.hash),(n==null||n===""||n===".")&&c){let o=Bt(l.search);if(c.route.index&&!o)l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index";else if(!c.route.index&&o){let h=new URLSearchParams(l.search),v=h.getAll("index");h.delete("index"),v.filter(w=>w).forEach(w=>h.append("index",w));let g=h.toString();l.search=g?`?${g}`:""}}return r!=="/"&&(l.pathname=Qn({basename:r,pathname:l.pathname})),Ne(l)}function yr(e,t,r){if(!r||!ha(r))return{path:t};if(r.formMethod&&!Pa(r.formMethod))return{path:t,error:ge(405,{method:r.formMethod})};let n=()=>({path:t,error:ge(400,{type:"invalid-body"})}),i=(r.formMethod||"get").toUpperCase(),s=Gr(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!pe(i))return n();let v=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((g,[w,S])=>`${g}${w}=${S}
`,""):String(r.body);return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:void 0,text:v}}}else if(r.formEncType==="application/json"){if(!pe(i))return n();try{let v=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:i,formAction:s,formEncType:r.formEncType,formData:void 0,json:v,text:void 0}}}catch{return n()}}}I(typeof FormData=="function","FormData is not available in this environment");let c,l;if(r.formData)c=jt(r.formData),l=r.formData;else if(r.body instanceof FormData)c=jt(r.body),l=r.body;else if(r.body instanceof URLSearchParams)c=r.body,l=Cr(c);else if(r.body==null)c=new URLSearchParams,l=new FormData;else try{c=new URLSearchParams(r.body),l=Cr(c)}catch{return n()}let o={formMethod:i,formAction:s,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:l,json:void 0,text:void 0};if(pe(o.formMethod))return{path:t,submission:o};let h=_e(t);return e&&h.search&&Bt(h.search)&&c.append("index",""),h.search=`?${c}`,{path:Ne(h),submission:o}}function vr(e,t,r,n,a,i,s,c,l,o,h,v,g,w,S,x,P,R,z,L){let V=L?ve(L[1])?L[1].error:L[1].data:void 0,D=a.createURL(i.location),y=a.createURL(l),K;if(h&&i.errors){let q=Object.keys(i.errors)[0];K=s.findIndex(X=>X.route.id===q)}else if(L&&ve(L[1])){let q=L[0];K=s.findIndex(X=>X.route.id===q)-1}let Z=L?L[1].statusCode:void 0,B=Z&&Z>=400,ie={currentUrl:D,currentParams:i.matches[0]?.params||{},nextUrl:y,nextParams:s[0].params,...c,actionResult:V,actionStatus:Z},he=s.map((q,X)=>{let{route:ae}=q,W=null;if(K!=null&&X>K?W=!1:ae.lazy?W=!0:ae.loader==null?W=!1:h?W=kt(ae,i.loaderData,i.errors):ma(i.loaderData,i.matches[X],q)&&(W=!0),W!==null)return Ht(r,n,e,q,o,t,W);let me=B?!1:v||D.pathname+D.search===y.pathname+y.search||D.search!==y.search||pa(i.matches[X],q),le={...ie,defaultShouldRevalidate:me},ye=Et(q,le);return Ht(r,n,e,q,o,t,ye,le)}),Ee=[];return S.forEach((q,X)=>{if(h||!s.some(N=>N.route.id===q.routeId)||w.has(X))return;let ae=i.fetchers.get(X),W=ae&&ae.state!=="idle"&&ae.data===void 0,me=Oe(P,q.path,R);if(!me){if(z&&W)return;Ee.push({key:X,routeId:q.routeId,path:q.path,matches:null,match:null,request:null,controller:null});return}if(x.has(X))return;let le=pt(me,q.path),ye=new AbortController,d=Be(a,q.path,ye.signal),E=null;if(g.has(X))g.delete(X),E=We(r,n,d,me,le,o,t);else if(W)v&&(E=We(r,n,d,me,le,o,t));else{let N={...ie,defaultShouldRevalidate:B?!1:v};Et(le,N)&&(E=We(r,n,d,me,le,o,t,N))}E&&Ee.push({key:X,routeId:q.routeId,path:q.path,matches:E,match:le,request:d,controller:ye})}),{dsMatches:he,revalidatingFetchers:Ee}}function kt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function ma(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}function pa(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Et(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function gr(e,t,r,n,a,i){let s;if(e){let o=n[e];I(o,`No route found to patch children into: routeId = ${e}`),o.children||(o.children=[]),s=o.children}else s=r;let c=[],l=[];if(t.forEach(o=>{let h=s.find(v=>Yr(o,v));h?l.push({existingRoute:h,newRoute:o}):c.push(o)}),c.length>0){let o=tt(c,a,[e||"_","patch",String(s?.length||"0")],n);s.push(...o)}if(i&&l.length>0)for(let o=0;o<l.length;o++){let{existingRoute:h,newRoute:v}=l[o],g=h,[w]=tt([v],a,[],{},!0);Object.assign(g,{element:w.element?w.element:g.element,errorElement:w.errorElement?w.errorElement:g.errorElement,hydrateFallbackElement:w.hydrateFallbackElement?w.hydrateFallbackElement:g.hydrateFallbackElement})}}function Yr(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>t.children?.some(a=>Yr(r,a))):!1}var wr=new WeakMap,Vr=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(I(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let i=a.lazy[e];if(!i)return;let s=wr.get(a);s||(s={},wr.set(a,s));let c=s[e];if(c)return c;let l=(async()=>{let o=$n(e),v=a[e]!==void 0&&e!=="hasErrorBoundary";if(o)re(!o,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),s[e]=Promise.resolve();else if(v)re(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let g=await i();g!=null&&(Object.assign(a,{[e]:g}),Object.assign(a,n(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(g=>g===void 0)&&(a.lazy=void 0))})();return s[e]=l,l},Er=new WeakMap;function ya(e,t,r,n,a){let i=r[e.id];if(I(i,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let h=Er.get(i);if(h)return{lazyRoutePromise:h,lazyHandlerPromise:h};let v=(async()=>{I(typeof e.lazy=="function","No lazy route function found");let g=await e.lazy(),w={};for(let S in g){let x=g[S];if(x===void 0)continue;let P=Un(S),z=i[S]!==void 0&&S!=="hasErrorBoundary";P?re(!P,"Route property "+S+" is not a supported property to be returned from a lazy route function. This property will be ignored."):z?re(!z,`Route "${i.id}" has a static property "${S}" defined but its lazy function is also returning a value for this property. The lazy route property "${S}" will be ignored.`):w[S]=x}Object.assign(i,w),Object.assign(i,{...n(i),lazy:void 0})})();return Er.set(i,v),v.catch(()=>{}),{lazyRoutePromise:v,lazyHandlerPromise:v}}let s=Object.keys(e.lazy),c=[],l;for(let h of s){if(a&&a.includes(h))continue;let v=Vr({key:h,route:e,manifest:r,mapRouteProperties:n});v&&(c.push(v),h===t&&(l=v))}let o=c.length>0?Promise.all(c).then(()=>{}):void 0;return o?.catch(()=>{}),l?.catch(()=>{}),{lazyRoutePromise:o,lazyHandlerPromise:l}}async function Rr(e){let t=e.matches.filter(a=>a.shouldLoad),r={};return(await Promise.all(t.map(a=>a.resolve()))).forEach((a,i)=>{r[t[i].route.id]=a}),r}async function va(e){return e.matches.some(t=>t.route.unstable_middleware)?Kr(e,!1,()=>Rr(e),(t,r)=>({[r]:{type:"error",result:t}})):Rr(e)}async function Kr(e,t,r,n){let{matches:a,request:i,params:s,context:c}=e,l={handlerResult:void 0};try{let o=a.flatMap(v=>v.route.unstable_middleware?v.route.unstable_middleware.map(g=>[v.route.id,g]):[]),h=await qr({request:i,params:s,context:c},o,t,l,r);return t?h:l.handlerResult}catch(o){if(!l.middlewareError)throw o;let h=await n(l.middlewareError.error,l.middlewareError.routeId);return l.handlerResult?Object.assign(l.handlerResult,h):h}}async function qr(e,t,r,n,a,i=0){let{request:s}=e;if(s.signal.aborted)throw s.signal.reason?s.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${s.method} ${s.url}`);let c=t[i];if(!c)return n.handlerResult=await a(),n.handlerResult;let[l,o]=c,h=!1,v,g=async()=>{if(h)throw new Error("You may only call `next()` once per middleware");h=!0,await qr(e,t,r,n,a,i+1)};try{let w=await o({request:e.request,params:e.params,context:e.context},g);return h?w===void 0?v:w:g()}catch(w){throw n.middlewareError?n.middlewareError.error!==w&&(n.middlewareError={routeId:l,error:w}):n.middlewareError={routeId:l,error:w},w}}function Jr(e,t,r,n,a){let i=Vr({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),s=ya(n.route,pe(r.method)?"action":"loader",t,e,a);return{middleware:i,route:s.lazyRoutePromise,handler:s.lazyHandlerPromise}}function Ht(e,t,r,n,a,i,s,c=null){let l=!1,o=Jr(e,t,r,n,a);return{...n,_lazyPromises:o,shouldLoad:s,unstable_shouldRevalidateArgs:c,unstable_shouldCallHandler(h){return l=!0,c?typeof h=="boolean"?Et(n,{...c,defaultShouldRevalidate:h}):Et(n,c):s},resolve(h){return l||s||h&&!pe(r.method)&&(n.route.lazy||n.route.loader)?wa({request:r,match:n,lazyHandlerPromise:o?.handler,lazyRoutePromise:o?.route,handlerOverride:h,scopedContext:i}):Promise.resolve({type:"data",result:void 0})}}}function We(e,t,r,n,a,i,s,c=null){return n.map(l=>l.route.id!==a.route.id?{...l,shouldLoad:!1,unstable_shouldRevalidateArgs:c,unstable_shouldCallHandler:()=>!1,_lazyPromises:Jr(e,t,r,l,i),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Ht(e,t,r,l,i,s,!0,c))}async function ga(e,t,r,n,a,i){r.some(o=>o._lazyPromises?.middleware)&&await Promise.all(r.map(o=>o._lazyPromises?.middleware));let s={request:t,params:r[0].params,context:a,matches:r},l=await e({...s,fetcherKey:n,unstable_runClientMiddleware:o=>{let h=s;return Kr(h,!1,()=>o({...h,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(v,g)=>({[g]:{type:"error",result:v}}))}});try{await Promise.all(r.flatMap(o=>[o._lazyPromises?.handler,o._lazyPromises?.route]))}catch{}return l}async function wa({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:i}){let s,c,l=pe(e.method),o=l?"action":"loader",h=v=>{let g,w=new Promise((P,R)=>g=R);c=()=>g(),e.signal.addEventListener("abort",c);let S=P=>typeof v!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${o}" [routeId: ${t.route.id}]`)):v({request:e,params:t.params,context:i},...P!==void 0?[P]:[]),x=(async()=>{try{return{type:"data",result:await(a?a(R=>S(R)):S())}}catch(P){return{type:"error",result:P}}})();return Promise.race([x,w])};try{let v=l?t.route.action:t.route.loader;if(r||n)if(v){let g,[w]=await Promise.all([h(v).catch(S=>{g=S}),r,n]);if(g!==void 0)throw g;s=w}else{await r;let g=l?t.route.action:t.route.loader;if(g)[s]=await Promise.all([h(g),n]);else if(o==="action"){let w=new URL(e.url),S=w.pathname+w.search;throw ge(405,{method:e.method,pathname:S,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(v)s=await h(v);else{let g=new URL(e.url),w=g.pathname+g.search;throw ge(404,{pathname:w})}}catch(v){return{type:"error",result:v}}finally{c&&e.signal.removeEventListener("abort",c)}return s}async function Ea(e){let{result:t,type:r}=e;if(Xr(t)){let n;try{let a=t.headers.get("Content-Type");a&&/\bapplication\/json\b/.test(a)?t.body==null?n=null:n=await t.json():n=await t.text()}catch(a){return{type:"error",error:a}}return r==="error"?{type:"error",error:new wt(t.status,t.statusText,n),statusCode:t.status,headers:t.headers}:{type:"data",data:n,statusCode:t.status,headers:t.headers}}return r==="error"?Lr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new wt(t.init?.status||500,void 0,t.data),statusCode:rt(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:rt(t)?t.status:void 0}:Lr(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Ra(e,t,r,n,a){let i=e.headers.get("Location");if(I(i,"Redirects returned/thrown from loaders/actions must have a Location header"),!It(i)){let s=n.slice(0,n.findIndex(c=>c.route.id===r)+1);i=Ut(new URL(t.url),s,a,i),e.headers.set("Location",i)}return e}function br(e,t,r){if(It(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),i=we(a.pathname,r)!=null;if(a.origin===t.origin&&i)return a.pathname+a.search+a.hash}return e}function Be(e,t,r,n){let a=e.createURL(Gr(t)).toString(),i={signal:r};if(n&&pe(n.formMethod)){let{formMethod:s,formEncType:c}=n;i.method=s.toUpperCase(),c==="application/json"?(i.headers=new Headers({"Content-Type":c}),i.body=JSON.stringify(n.json)):c==="text/plain"?i.body=n.text:c==="application/x-www-form-urlencoded"&&n.formData?i.body=jt(n.formData):i.body=n.formData}return new Request(a,i)}function jt(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Cr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function ba(e,t,r,n=!1,a=!1){let i={},s=null,c,l=!1,o={},h=r&&ve(r[1])?r[1].error:void 0;return e.forEach(v=>{if(!(v.route.id in t))return;let g=v.route.id,w=t[g];if(I(!je(w),"Cannot handle redirect results in processLoaderData"),ve(w)){let S=w.error;if(h!==void 0&&(S=h,h=void 0),s=s||{},a)s[g]=S;else{let x=He(e,g);s[x.route.id]==null&&(s[x.route.id]=S)}n||(i[g]=Wr),l||(l=!0,c=rt(w.error)?w.error.status:500),w.headers&&(o[g]=w.headers)}else i[g]=w.data,w.statusCode&&w.statusCode!==200&&!l&&(c=w.statusCode),w.headers&&(o[g]=w.headers)}),h!==void 0&&r&&(s={[r[0]]:h},r[2]&&(i[r[2]]=void 0)),{loaderData:i,errors:s,statusCode:c||200,loaderHeaders:o}}function Sr(e,t,r,n,a,i){let{loaderData:s,errors:c}=ba(t,r,n);return a.filter(l=>!l.matches||l.matches.some(o=>o.shouldLoad)).forEach(l=>{let{key:o,match:h,controller:v}=l,g=i[o];if(I(g,"Did not find corresponding fetcher result"),!(v&&v.signal.aborted))if(ve(g)){let w=He(e.matches,h?.route.id);c&&c[w.route.id]||(c={...c,[w.route.id]:g.error}),e.fetchers.delete(o)}else if(je(g))I(!1,"Unhandled fetcher revalidation redirect");else{let w=Me(g.data);e.fetchers.set(o,w)}}),{loaderData:s,errors:c}}function Pr(e,t,r,n){let a=Object.entries(t).filter(([,i])=>i!==Wr).reduce((i,[s,c])=>(i[s]=c,i),{});for(let i of r){let s=i.route.id;if(!t.hasOwnProperty(s)&&e.hasOwnProperty(s)&&i.route.loader&&(a[s]=e[s]),n&&n.hasOwnProperty(s))break}return a}function xr(e){return e?ve(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function He(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function _r(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ge(e,{pathname:t,routeId:r,method:n,type:a,message:i}={}){let s="Unknown Server Error",c="Unknown @remix-run/router error";return e===400?(s="Bad Request",n&&t&&r?c=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:a==="invalid-body"&&(c="Unable to encode submission body")):e===403?(s="Forbidden",c=`Route "${r}" does not match URL "${t}"`):e===404?(s="Not Found",c=`No route matches URL "${t}"`):e===405&&(s="Method Not Allowed",n&&t&&r?c=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(c=`Invalid request method "${n.toUpperCase()}"`)),new wt(e||500,s,new Error(c),!0)}function ft(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(je(a))return{key:n,result:a}}}function Gr(e){let t=typeof e=="string"?_e(e):e;return Ne({...t,hash:""})}function Ca(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Sa(e){return Xr(e.result)&&la.has(e.result.status)}function ve(e){return e.type==="error"}function je(e){return(e&&e.type)==="redirect"}function Lr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Xr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Pa(e){return ia.has(e.toUpperCase())}function pe(e){return aa.has(e.toUpperCase())}function Bt(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function pt(e,t){let r=typeof t=="string"?_e(t).search:t.search;if(e[e.length-1].route.index&&Bt(r||""))return e[e.length-1];let n=zr(e);return n[n.length-1]}function Dr(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:i,json:s}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(i!=null)return{formMethod:t,formAction:r,formEncType:n,formData:i,json:void 0,text:void 0};if(s!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:s,text:void 0}}}function $t(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function xa(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Xe(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function _a(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Me(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function La(e,t){try{let r=e.sessionStorage.getItem(Br);if(r){let n=JSON.parse(r);for(let[a,i]of Object.entries(n||{}))i&&Array.isArray(i)&&t.set(a,new Set(i||[]))}}catch{}}function Da(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Br,JSON.stringify(r))}catch(n){re(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function Ta(){let e,t,r=new Promise((n,a)=>{e=async i=>{n(i);try{await r}catch{}},t=async i=>{a(i);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var ze=m.createContext(null);ze.displayName="DataRouter";var nt=m.createContext(null);nt.displayName="DataRouterState";m.createContext(!1);var Wt=m.createContext({isTransitioning:!1});Wt.displayName="ViewTransition";var Qr=m.createContext(new Map);Qr.displayName="Fetchers";var Ma=m.createContext(null);Ma.displayName="Await";var Re=m.createContext(null);Re.displayName="Navigation";var Ct=m.createContext(null);Ct.displayName="Location";var be=m.createContext({outlet:null,matches:[],isDataRoute:!1});be.displayName="Route";var Yt=m.createContext(null);Yt.displayName="RouteError";function Oa(e,{relative:t}={}){I(Ye(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=m.useContext(Re),{hash:a,pathname:i,search:s}=at(e,{relative:t}),c=i;return r!=="/"&&(c=i==="/"?r:Pe([r,i])),n.createHref({pathname:c,search:s,hash:a})}function Ye(){return m.useContext(Ct)!=null}function Ae(){return I(Ye(),"useLocation() may be used only in the context of a <Router> component."),m.useContext(Ct).location}var Zr="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function en(e){m.useContext(Re).static||m.useLayoutEffect(e)}function tn(){let{isDataRoute:e}=m.useContext(be);return e?Ka():Na()}function Na(){I(Ye(),"useNavigate() may be used only in the context of a <Router> component.");let e=m.useContext(ze),{basename:t,navigator:r}=m.useContext(Re),{matches:n}=m.useContext(be),{pathname:a}=Ae(),i=JSON.stringify(Rt(n)),s=m.useRef(!1);return en(()=>{s.current=!0}),m.useCallback((l,o={})=>{if(re(s.current,Zr),!s.current)return;if(typeof l=="number"){r.go(l);return}let h=bt(l,JSON.parse(i),a,o.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:Pe([t,h.pathname])),(o.replace?r.replace:r.push)(h,o.state,o)},[t,r,i,a,e])}var Aa=m.createContext(null);function $a(e){let t=m.useContext(be).outlet;return t&&m.createElement(Aa.Provider,{value:e},t)}function at(e,{relative:t}={}){let{matches:r}=m.useContext(be),{pathname:n}=Ae(),a=JSON.stringify(Rt(r));return m.useMemo(()=>bt(e,JSON.parse(a),n,t==="path"),[e,a,n,t])}function Fa(e,t,r,n){I(Ye(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=m.useContext(Re),{matches:i}=m.useContext(be),s=i[i.length-1],c=s?s.params:{},l=s?s.pathname:"/",o=s?s.pathnameBase:"/",h=s&&s.route;{let R=h&&h.path||"";rn(l,!h||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let v=Ae(),g;g=v;let w=g.pathname||"/",S=w;if(o!=="/"){let R=o.replace(/^\//,"").split("/");S="/"+w.replace(/^\//,"").split("/").slice(R.length).join("/")}let x=Oe(e,{pathname:S});return re(h||x!=null,`No routes matched location "${g.pathname}${g.search}${g.hash}" `),re(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${g.pathname}${g.search}${g.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),za(x&&x.map(R=>Object.assign({},R,{params:Object.assign({},c,R.params),pathname:Pe([o,a.encodeLocation?a.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?o:Pe([o,a.encodeLocation?a.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),i,r,n)}function Ua(){let e=Va(),t=rt(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},i={padding:"2px 4px",backgroundColor:n},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=m.createElement(m.Fragment,null,m.createElement("p",null,"💿 Hey developer 👋"),m.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",m.createElement("code",{style:i},"ErrorBoundary")," or"," ",m.createElement("code",{style:i},"errorElement")," prop on your route.")),m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),r?m.createElement("pre",{style:a},r):null,s)}var ka=m.createElement(Ua,null),Ha=class extends m.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?m.createElement(be.Provider,{value:this.props.routeContext},m.createElement(Yt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ja({routeContext:e,match:t,children:r}){let n=m.useContext(ze);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),m.createElement(be.Provider,{value:e},r)}function za(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,i=r?.errors;if(i!=null){let l=a.findIndex(o=>o.route.id&&i?.[o.route.id]!==void 0);I(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),a=a.slice(0,Math.min(a.length,l+1))}let s=!1,c=-1;if(r)for(let l=0;l<a.length;l++){let o=a[l];if((o.route.HydrateFallback||o.route.hydrateFallbackElement)&&(c=l),o.route.id){let{loaderData:h,errors:v}=r,g=o.route.loader&&!h.hasOwnProperty(o.route.id)&&(!v||v[o.route.id]===void 0);if(o.route.lazy||g){s=!0,c>=0?a=a.slice(0,c+1):a=[a[0]];break}}}return a.reduceRight((l,o,h)=>{let v,g=!1,w=null,S=null;r&&(v=i&&o.route.id?i[o.route.id]:void 0,w=o.route.errorElement||ka,s&&(c<0&&h===0?(rn("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),g=!0,S=null):c===h&&(g=!0,S=o.route.hydrateFallbackElement||null)));let x=t.concat(a.slice(0,h+1)),P=()=>{let R;return v?R=w:g?R=S:o.route.Component?R=m.createElement(o.route.Component,null):o.route.element?R=o.route.element:R=l,m.createElement(ja,{match:o,routeContext:{outlet:l,matches:x,isDataRoute:r!=null},children:R})};return r&&(o.route.ErrorBoundary||o.route.errorElement||h===0)?m.createElement(Ha,{location:r.location,revalidation:r.revalidation,component:w,error:v,children:P(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):P()},null)}function Vt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ia(e){let t=m.useContext(ze);return I(t,Vt(e)),t}function Ba(e){let t=m.useContext(nt);return I(t,Vt(e)),t}function Wa(e){let t=m.useContext(be);return I(t,Vt(e)),t}function Kt(e){let t=Wa(e),r=t.matches[t.matches.length-1];return I(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Ya(){return Kt("useRouteId")}function Va(){let e=m.useContext(Yt),t=Ba("useRouteError"),r=Kt("useRouteError");return e!==void 0?e:t.errors?.[r]}function Ka(){let{router:e}=Ia("useNavigate"),t=Kt("useNavigate"),r=m.useRef(!1);return en(()=>{r.current=!0}),m.useCallback(async(a,i={})=>{re(r.current,Zr),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...i}))},[e,t])}var Tr={};function rn(e,t,r){!t&&!Tr[e]&&(Tr[e]=!0,re(!1,r))}var Mr={};function Or(e,t){!e&&!Mr[t]&&(Mr[t]=!0,console.warn(t))}function qa(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&re(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:m.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&re(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:m.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&re(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:m.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Ja=["HydrateFallback","hydrateFallbackElement"],Ga=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Xa({router:e,flushSync:t}){let[r,n]=m.useState(e.state),[a,i]=m.useState(),[s,c]=m.useState({isTransitioning:!1}),[l,o]=m.useState(),[h,v]=m.useState(),[g,w]=m.useState(),S=m.useRef(new Map),x=m.useCallback((L,{deletedFetchers:V,flushSync:D,viewTransitionOpts:y})=>{L.fetchers.forEach((Z,B)=>{Z.data!==void 0&&S.current.set(B,Z.data)}),V.forEach(Z=>S.current.delete(Z)),Or(D===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let K=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(Or(y==null||K,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!K){t&&D?t(()=>n(L)):m.startTransition(()=>n(L));return}if(t&&D){t(()=>{h&&(l&&l.resolve(),h.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let Z=e.window.document.startViewTransition(()=>{t(()=>n(L))});Z.finished.finally(()=>{t(()=>{o(void 0),v(void 0),i(void 0),c({isTransitioning:!1})})}),t(()=>v(Z));return}h?(l&&l.resolve(),h.skipTransition(),w({state:L,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(i(L),c({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,h,l]);m.useLayoutEffect(()=>e.subscribe(x),[e,x]),m.useEffect(()=>{s.isTransitioning&&!s.flushSync&&o(new Ga)},[s]),m.useEffect(()=>{if(l&&a&&e.window){let L=a,V=l.promise,D=e.window.document.startViewTransition(async()=>{m.startTransition(()=>n(L)),await V});D.finished.finally(()=>{o(void 0),v(void 0),i(void 0),c({isTransitioning:!1})}),v(D)}},[a,l,e.window]),m.useEffect(()=>{l&&a&&r.location.key===a.location.key&&l.resolve()},[l,h,r.location,a]),m.useEffect(()=>{!s.isTransitioning&&g&&(i(g.state),c({isTransitioning:!0,flushSync:!1,currentLocation:g.currentLocation,nextLocation:g.nextLocation}),w(void 0))},[s.isTransitioning,g]);let P=m.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:L=>e.navigate(L),push:(L,V,D)=>e.navigate(L,{state:V,preventScrollReset:D?.preventScrollReset}),replace:(L,V,D)=>e.navigate(L,{replace:!0,state:V,preventScrollReset:D?.preventScrollReset})}),[e]),R=e.basename||"/",z=m.useMemo(()=>({router:e,navigator:P,static:!1,basename:R}),[e,P,R]);return m.createElement(m.Fragment,null,m.createElement(ze.Provider,{value:z},m.createElement(nt.Provider,{value:r},m.createElement(Qr.Provider,{value:S.current},m.createElement(Wt.Provider,{value:s},m.createElement(eo,{basename:R,location:r.location,navigationType:r.historyAction,navigator:P},m.createElement(Qa,{routes:e.routes,future:e.future,state:r})))))),null)}var Qa=m.memo(Za);function Za({routes:e,future:t,state:r}){return Fa(e,void 0,r,t)}function Uo({to:e,replace:t,state:r,relative:n}){I(Ye(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=m.useContext(Re);re(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=m.useContext(be),{pathname:s}=Ae(),c=tn(),l=bt(e,Rt(i),s,n==="path"),o=JSON.stringify(l);return m.useEffect(()=>{c(JSON.parse(o),{replace:t,state:r,relative:n})},[c,o,n,t,r]),null}function ko(e){return $a(e.context)}function eo({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:i=!1}){I(!Ye(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),c=m.useMemo(()=>({basename:s,navigator:a,static:i,future:{}}),[s,a,i]);typeof r=="string"&&(r=_e(r));let{pathname:l="/",search:o="",hash:h="",state:v=null,key:g="default"}=r,w=m.useMemo(()=>{let S=we(l,s);return S==null?null:{location:{pathname:S,search:o,hash:h,state:v,key:g},navigationType:n}},[s,l,o,h,v,g,n]);return re(w!=null,`<Router basename="${s}"> is not able to match the URL "${l}${o}${h}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:m.createElement(Re.Provider,{value:c},m.createElement(Ct.Provider,{children:t,value:w}))}var yt="get",vt="application/x-www-form-urlencoded";function St(e){return e!=null&&typeof e.tagName=="string"}function to(e){return St(e)&&e.tagName.toLowerCase()==="button"}function ro(e){return St(e)&&e.tagName.toLowerCase()==="form"}function no(e){return St(e)&&e.tagName.toLowerCase()==="input"}function ao(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function oo(e,t){return e.button===0&&(!t||t==="_self")&&!ao(e)}var ht=null;function io(){if(ht===null)try{new FormData(document.createElement("form"),0),ht=!1}catch{ht=!0}return ht}var lo=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ft(e){return e!=null&&!lo.has(e)?(re(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${vt}"`),null):e}function so(e,t){let r,n,a,i,s;if(ro(e)){let c=e.getAttribute("action");n=c?we(c,t):null,r=e.getAttribute("method")||yt,a=Ft(e.getAttribute("enctype"))||vt,i=new FormData(e)}else if(to(e)||no(e)&&(e.type==="submit"||e.type==="image")){let c=e.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||c.getAttribute("action");if(n=l?we(l,t):null,r=e.getAttribute("formmethod")||c.getAttribute("method")||yt,a=Ft(e.getAttribute("formenctype"))||Ft(c.getAttribute("enctype"))||vt,i=new FormData(c,e),!io()){let{name:o,type:h,value:v}=e;if(h==="image"){let g=o?`${o}.`:"";i.append(`${g}x`,"0"),i.append(`${g}y`,"0")}else o&&i.append(o,v)}}else{if(St(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=yt,n=null,a=vt,s=e}return i&&a==="text/plain"&&(s=i,i=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:i,body:s}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function qt(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function uo(e,t,r){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname=`_root.${r}`:t&&we(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.${r}`:n.pathname=`${n.pathname.replace(/\/$/,"")}.${r}`,n}async function co(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function fo(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ho(e,t,r){let n=await Promise.all(e.map(async a=>{let i=t.routes[a.route.id];if(i){let s=await co(i,r);return s.links?s.links():[]}return[]}));return vo(n.flat(1).filter(fo).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Nr(e,t,r,n,a,i){let s=(l,o)=>r[o]?l.route.id!==r[o].route.id:!0,c=(l,o)=>r[o].pathname!==l.pathname||r[o].route.path?.endsWith("*")&&r[o].params["*"]!==l.params["*"];return i==="assets"?t.filter((l,o)=>s(l,o)||c(l,o)):i==="data"?t.filter((l,o)=>{let h=n.routes[l.route.id];if(!h||!h.hasLoader)return!1;if(s(l,o)||c(l,o))return!0;if(l.route.shouldRevalidate){let v=l.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof v=="boolean")return v}return!0}):[]}function mo(e,t,{includeHydrateFallback:r}={}){return po(e.map(n=>{let a=t.routes[n.route.id];if(!a)return[];let i=[a.module];return a.clientActionModule&&(i=i.concat(a.clientActionModule)),a.clientLoaderModule&&(i=i.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(i=i.concat(a.hydrateFallbackModule)),a.imports&&(i=i.concat(a.imports)),i}).flat(1))}function po(e){return[...new Set(e)]}function yo(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function vo(e,t){let r=new Set;return new Set(t),e.reduce((n,a)=>{let i=JSON.stringify(yo(a));return r.has(i)||(r.add(i),n.push({key:i,link:a})),n},[])}function nn(){let e=m.useContext(ze);return qt(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function go(){let e=m.useContext(nt);return qt(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Jt=m.createContext(void 0);Jt.displayName="FrameworkContext";function an(){let e=m.useContext(Jt);return qt(e,"You must render this element inside a <HydratedRouter> element"),e}function wo(e,t){let r=m.useContext(Jt),[n,a]=m.useState(!1),[i,s]=m.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:o,onMouseLeave:h,onTouchStart:v}=t,g=m.useRef(null);m.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let x=R=>{R.forEach(z=>{s(z.isIntersecting)})},P=new IntersectionObserver(x,{threshold:.5});return g.current&&P.observe(g.current),()=>{P.disconnect()}}},[e]),m.useEffect(()=>{if(n){let x=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(x)}}},[n]);let w=()=>{a(!0)},S=()=>{a(!1),s(!1)};return r?e!=="intent"?[i,g,{}]:[i,g,{onFocus:Qe(c,w),onBlur:Qe(l,S),onMouseEnter:Qe(o,w),onMouseLeave:Qe(h,S),onTouchStart:Qe(v,w)}]:[!1,g,{}]}function Qe(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Eo({page:e,...t}){let{router:r}=nn(),n=m.useMemo(()=>Oe(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?m.createElement(bo,{page:e,matches:n,...t}):null}function Ro(e){let{manifest:t,routeModules:r}=an(),[n,a]=m.useState([]);return m.useEffect(()=>{let i=!1;return ho(e,t,r).then(s=>{i||a(s)}),()=>{i=!0}},[e,t,r]),n}function bo({page:e,matches:t,...r}){let n=Ae(),{manifest:a,routeModules:i}=an(),{basename:s}=nn(),{loaderData:c,matches:l}=go(),o=m.useMemo(()=>Nr(e,t,l,a,n,"data"),[e,t,l,a,n]),h=m.useMemo(()=>Nr(e,t,l,a,n,"assets"),[e,t,l,a,n]),v=m.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let S=new Set,x=!1;if(t.forEach(R=>{let z=a.routes[R.route.id];!z||!z.hasLoader||(!o.some(L=>L.route.id===R.route.id)&&R.route.id in c&&i[R.route.id]?.shouldRevalidate||z.hasClientLoader?x=!0:S.add(R.route.id))}),S.size===0)return[];let P=uo(e,s,"data");return x&&S.size>0&&P.searchParams.set("_routes",t.filter(R=>S.has(R.route.id)).map(R=>R.route.id).join(",")),[P.pathname+P.search]},[s,c,n,a,o,t,e,i]),g=m.useMemo(()=>mo(h,a),[h,a]),w=Ro(h);return m.createElement(m.Fragment,null,v.map(S=>m.createElement("link",{key:S,rel:"prefetch",as:"fetch",href:S,...r})),g.map(S=>m.createElement("link",{key:S,rel:"modulepreload",href:S,...r})),w.map(({key:S,link:x})=>m.createElement("link",{key:S,...x})))}function Co(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var on=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{on&&(window.__reactRouterVersion="7.7.0")}catch{}function Ho(e,t){return fa({basename:t?.basename,unstable_getContext:t?.unstable_getContext,future:t?.future,history:Mn({window:t?.window}),hydrationData:So(),routes:e,mapRouteProperties:qa,hydrationRouteProperties:Ja,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation,window:t?.window}).initialize()}function So(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:Po(e.errors)}),e}function Po(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,a]of t)if(a&&a.__type==="RouteErrorResponse")r[n]=new wt(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let i=window[a.__subType];if(typeof i=="function")try{let s=new i(a.message);s.stack="",r[n]=s}catch{}}if(r[n]==null){let i=new Error(a.message);i.stack="",r[n]=i}}else r[n]=a;return r}var ln=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,sn=m.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:a,reloadDocument:i,replace:s,state:c,target:l,to:o,preventScrollReset:h,viewTransition:v,...g},w){let{basename:S}=m.useContext(Re),x=typeof o=="string"&&ln.test(o),P,R=!1;if(typeof o=="string"&&x&&(P=o,on))try{let B=new URL(window.location.href),ie=o.startsWith("//")?new URL(B.protocol+o):new URL(o),he=we(ie.pathname,S);ie.origin===B.origin&&he!=null?o=he+ie.search+ie.hash:R=!0}catch{re(!1,`<Link to="${o}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let z=Oa(o,{relative:a}),[L,V,D]=wo(n,g),y=Do(o,{replace:s,state:c,target:l,preventScrollReset:h,relative:a,viewTransition:v});function K(B){t&&t(B),B.defaultPrevented||y(B)}let Z=m.createElement("a",{...g,...D,href:P||z,onClick:R||i?t:K,ref:Co(w,V),target:l,"data-discover":!x&&r==="render"?"true":void 0});return L&&!x?m.createElement(m.Fragment,null,Z,m.createElement(Eo,{page:z})):Z});sn.displayName="Link";var xo=m.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:a=!1,style:i,to:s,viewTransition:c,children:l,...o},h){let v=at(s,{relative:o.relative}),g=Ae(),w=m.useContext(nt),{navigator:S,basename:x}=m.useContext(Re),P=w!=null&&Ao(v)&&c===!0,R=S.encodeLocation?S.encodeLocation(v).pathname:v.pathname,z=g.pathname,L=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;r||(z=z.toLowerCase(),L=L?L.toLowerCase():null,R=R.toLowerCase()),L&&x&&(L=we(L,x)||L);const V=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let D=z===R||!a&&z.startsWith(R)&&z.charAt(V)==="/",y=L!=null&&(L===R||!a&&L.startsWith(R)&&L.charAt(R.length)==="/"),K={isActive:D,isPending:y,isTransitioning:P},Z=D?t:void 0,B;typeof n=="function"?B=n(K):B=[n,D?"active":null,y?"pending":null,P?"transitioning":null].filter(Boolean).join(" ");let ie=typeof i=="function"?i(K):i;return m.createElement(sn,{...o,"aria-current":Z,className:B,ref:h,style:ie,to:s,viewTransition:c},typeof l=="function"?l(K):l)});xo.displayName="NavLink";var _o=m.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:i,method:s=yt,action:c,onSubmit:l,relative:o,preventScrollReset:h,viewTransition:v,...g},w)=>{let S=Oo(),x=No(c,{relative:o}),P=s.toLowerCase()==="get"?"get":"post",R=typeof c=="string"&&ln.test(c),z=L=>{if(l&&l(L),L.defaultPrevented)return;L.preventDefault();let V=L.nativeEvent.submitter,D=V?.getAttribute("formmethod")||s;S(V||L.currentTarget,{fetcherKey:t,method:D,navigate:r,replace:a,state:i,relative:o,preventScrollReset:h,viewTransition:v})};return m.createElement("form",{ref:w,method:P,action:x,onSubmit:n?l:z,...g,"data-discover":!R&&e==="render"?"true":void 0})});_o.displayName="Form";function Lo(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function un(e){let t=m.useContext(ze);return I(t,Lo(e)),t}function Do(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:i,viewTransition:s}={}){let c=tn(),l=Ae(),o=at(e,{relative:i});return m.useCallback(h=>{if(oo(h,t)){h.preventDefault();let v=r!==void 0?r:Ne(l)===Ne(o);c(e,{replace:v,state:n,preventScrollReset:a,relative:i,viewTransition:s})}},[l,c,o,r,n,t,e,a,i,s])}var To=0,Mo=()=>`__${String(++To)}__`;function Oo(){let{router:e}=un("useSubmit"),{basename:t}=m.useContext(Re),r=Ya();return m.useCallback(async(n,a={})=>{let{action:i,method:s,encType:c,formData:l,body:o}=so(n,t);if(a.navigate===!1){let h=a.fetcherKey||Mo();await e.fetch(h,r,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:o,formMethod:a.method||s,formEncType:a.encType||c,flushSync:a.flushSync})}else await e.navigate(a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:o,formMethod:a.method||s,formEncType:a.encType||c,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function No(e,{relative:t}={}){let{basename:r}=m.useContext(Re),n=m.useContext(be);I(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),i={...at(e||".",{relative:t})},s=Ae();if(e==null){i.search=s.search;let c=new URLSearchParams(i.search),l=c.getAll("index");if(l.some(h=>h==="")){c.delete("index"),l.filter(v=>v).forEach(v=>c.append("index",v));let h=c.toString();i.search=h?`?${h}`:""}}return(!e||e===".")&&a.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(i.pathname=i.pathname==="/"?r:Pe([r,i.pathname])),Ne(i)}function Ao(e,t={}){let r=m.useContext(Wt);I(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=un("useViewTransitionState"),a=at(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=we(r.currentLocation.pathname,n)||r.currentLocation.pathname,s=we(r.nextLocation.pathname,n)||r.nextLocation.pathname;return gt(a.pathname,s)!=null||gt(a.pathname,i)!=null}function jo(e){return m.createElement(Xa,{flushSync:zt.flushSync,...e})}export{sn as L,Uo as N,ko as O,Pn as R,Ln as a,$o as b,zt as c,Fo as d,Fr as e,_n as f,$r as g,Ae as h,Ho as i,jo as j,m as r,tn as u};

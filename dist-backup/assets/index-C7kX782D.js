import{e as rh,f as sh,g as n2,r as G,u as ia,R as ee,h as fi,O as i2,L as r2,N as zu,i as dh,j as uh}from"./react-vendor-D85R-n5s.js";import{i as Ce,a as oh,b as ch,c as Lu,u as s2}from"./utils-vendor-ZSUEJqAC.js";import{s as Z,S as la,D as Il,I as se,R as d2,a as u2,b as Xl,F as X,U as wu,c as Eu,d as ru,e as Rr,B as ht,f as fh,g as mh,h as hh,L as zr,A as ju,i as P1,j as o2,M as c2,T as gh,k as Y1,l as Z1,C as ph,m as mi,n as f2,o as $h,p as vh,q as yh,r as xh,t as bh,u as Sh,v as Nh,w as Ch,x as k1,y as m2,z as wh}from"./antd-vendor-Bi4-MCIN.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))u(c);new MutationObserver(c=>{for(const m of c)if(m.type==="childList")for(const g of m.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&u(g)}).observe(document,{childList:!0,subtree:!0});function s(c){const m={};return c.integrity&&(m.integrity=c.integrity),c.referrerPolicy&&(m.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?m.credentials="include":c.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function u(c){if(c.ep)return;c.ep=!0;const m=s(c);fetch(c.href,m)}})();var su={exports:{}},In={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var F1;function Eh(){if(F1)return In;F1=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(u,c,m){var g=null;if(m!==void 0&&(g=""+m),c.key!==void 0&&(g=""+c.key),"key"in c){m={};for(var v in c)v!=="key"&&(m[v]=c[v])}else m=c;return c=m.ref,{$$typeof:i,type:u,key:g,ref:c!==void 0?c:null,props:m}}return In.Fragment=r,In.jsx=s,In.jsxs=s,In}var V1;function jh(){return V1||(V1=1,su.exports=Eh()),su.exports}var o=jh(),du={exports:{}},Xn={},uu={exports:{}},ou={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var I1;function Ah(){return I1||(I1=1,function(i){function r(R,D){var H=R.length;R.push(D);e:for(;0<H;){var V=H-1>>>1,J=R[V];if(0<c(J,D))R[V]=D,R[H]=J,H=V;else break e}}function s(R){return R.length===0?null:R[0]}function u(R){if(R.length===0)return null;var D=R[0],H=R.pop();if(H!==D){R[0]=H;e:for(var V=0,J=R.length,W=J>>>1;V<W;){var he=2*(V+1)-1,de=R[he],Me=he+1,gt=R[Me];if(0>c(de,H))Me<J&&0>c(gt,de)?(R[V]=gt,R[Me]=H,V=Me):(R[V]=de,R[he]=H,V=he);else if(Me<J&&0>c(gt,H))R[V]=gt,R[Me]=H,V=Me;else break e}}return D}function c(R,D){var H=R.sortIndex-D.sortIndex;return H!==0?H:R.id-D.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;i.unstable_now=function(){return m.now()}}else{var g=Date,v=g.now();i.unstable_now=function(){return g.now()-v}}var x=[],y=[],$=1,j=null,A=3,O=!1,B=!1,z=!1,P=!1,L=typeof setTimeout=="function"?setTimeout:null,U=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function fe(R){for(var D=s(y);D!==null;){if(D.callback===null)u(y);else if(D.startTime<=R)u(y),D.sortIndex=D.expirationTime,r(x,D);else break;D=s(y)}}function ie(R){if(z=!1,fe(R),!B)if(s(x)!==null)B=!0,$e||($e=!0,Oe());else{var D=s(y);D!==null&&jt(ie,D.startTime-R)}}var $e=!1,re=-1,K=5,oe=-1;function Te(){return P?!0:!(i.unstable_now()-oe<K)}function Et(){if(P=!1,$e){var R=i.unstable_now();oe=R;var D=!0;try{e:{B=!1,z&&(z=!1,U(re),re=-1),O=!0;var H=A;try{t:{for(fe(R),j=s(x);j!==null&&!(j.expirationTime>R&&Te());){var V=j.callback;if(typeof V=="function"){j.callback=null,A=j.priorityLevel;var J=V(j.expirationTime<=R);if(R=i.unstable_now(),typeof J=="function"){j.callback=J,fe(R),D=!0;break t}j===s(x)&&u(x),fe(R)}else u(x);j=s(x)}if(j!==null)D=!0;else{var W=s(y);W!==null&&jt(ie,W.startTime-R),D=!1}}break e}finally{j=null,A=H,O=!1}D=void 0}}finally{D?Oe():$e=!1}}}var Oe;if(typeof q=="function")Oe=function(){q(Et)};else if(typeof MessageChannel<"u"){var ra=new MessageChannel,We=ra.port2;ra.port1.onmessage=Et,Oe=function(){We.postMessage(null)}}else Oe=function(){L(Et,0)};function jt(R,D){re=L(function(){R(i.unstable_now())},D)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(R){R.callback=null},i.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<R?Math.floor(1e3/R):5},i.unstable_getCurrentPriorityLevel=function(){return A},i.unstable_next=function(R){switch(A){case 1:case 2:case 3:var D=3;break;default:D=A}var H=A;A=D;try{return R()}finally{A=H}},i.unstable_requestPaint=function(){P=!0},i.unstable_runWithPriority=function(R,D){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var H=A;A=R;try{return D()}finally{A=H}},i.unstable_scheduleCallback=function(R,D,H){var V=i.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?V+H:V):H=V,R){case 1:var J=-1;break;case 2:J=250;break;case 5:J=1073741823;break;case 4:J=1e4;break;default:J=5e3}return J=H+J,R={id:$++,callback:D,priorityLevel:R,startTime:H,expirationTime:J,sortIndex:-1},H>V?(R.sortIndex=H,r(y,R),s(x)===null&&R===s(y)&&(z?(U(re),re=-1):z=!0,jt(ie,H-V))):(R.sortIndex=J,r(x,R),B||O||(B=!0,$e||($e=!0,Oe()))),R},i.unstable_shouldYield=Te,i.unstable_wrapCallback=function(R){var D=A;return function(){var H=A;A=D;try{return R.apply(this,arguments)}finally{A=H}}}}(ou)),ou}var X1;function Th(){return X1||(X1=1,uu.exports=Ah()),uu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q1;function Oh(){if(Q1)return Xn;Q1=1;var i=Th(),r=rh(),s=sh();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(m(e)!==e)throw Error(u(188))}function x(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var d=n.alternate;if(d===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===d.child){for(d=n.child;d;){if(d===a)return v(n),e;if(d===l)return v(n),t;d=d.sibling}throw Error(u(188))}if(a.return!==l.return)a=n,l=d;else{for(var f=!1,h=n.child;h;){if(h===a){f=!0,a=n,l=d;break}if(h===l){f=!0,l=n,a=d;break}h=h.sibling}if(!f){for(h=d.child;h;){if(h===a){f=!0,a=d,l=n;break}if(h===l){f=!0,l=d,a=n;break}h=h.sibling}if(!f)throw Error(u(189))}}if(a.alternate!==l)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var $=Object.assign,j=Symbol.for("react.element"),A=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),P=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),U=Symbol.for("react.consumer"),q=Symbol.for("react.context"),fe=Symbol.for("react.forward_ref"),ie=Symbol.for("react.suspense"),$e=Symbol.for("react.suspense_list"),re=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),Te=Symbol.for("react.memo_cache_sentinel"),Et=Symbol.iterator;function Oe(e){return e===null||typeof e!="object"?null:(e=Et&&e[Et]||e["@@iterator"],typeof e=="function"?e:null)}var ra=Symbol.for("react.client.reference");function We(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ra?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case B:return"Fragment";case P:return"Profiler";case z:return"StrictMode";case ie:return"Suspense";case $e:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case q:return(e.displayName||"Context")+".Provider";case U:return(e._context.displayName||"Context")+".Consumer";case fe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case re:return t=e.displayName||null,t!==null?t:We(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return We(e(t))}catch{}}return null}var jt=Array.isArray,R=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H={pending:!1,data:null,method:null,action:null},V=[],J=-1;function W(e){return{current:e}}function he(e){0>J||(e.current=V[J],V[J]=null,J--)}function de(e,t){J++,V[J]=e.current,e.current=t}var Me=W(null),gt=W(null),sa=W(null),hi=W(null);function gi(e,t){switch(de(sa,t),de(gt,e),de(Me,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?p1(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=p1(t),e=$1(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}he(Me),de(Me,e)}function al(){he(Me),he(gt),he(sa)}function kr(e){e.memoizedState!==null&&de(hi,e);var t=Me.current,a=$1(t,e.type);t!==a&&(de(gt,e),de(Me,a))}function pi(e){gt.current===e&&(he(Me),he(gt)),hi.current===e&&(he(hi),Yn._currentValue=H)}var Fr=Object.prototype.hasOwnProperty,Vr=i.unstable_scheduleCallback,Ir=i.unstable_cancelCallback,B2=i.unstable_shouldYield,H2=i.unstable_requestPaint,Mt=i.unstable_now,q2=i.unstable_getCurrentPriorityLevel,Xu=i.unstable_ImmediatePriority,Qu=i.unstable_UserBlockingPriority,$i=i.unstable_NormalPriority,G2=i.unstable_LowPriority,Ku=i.unstable_IdlePriority,P2=i.log,Y2=i.unstable_setDisableYieldValue,Ql=null,it=null;function da(e){if(typeof P2=="function"&&Y2(e),it&&typeof it.setStrictMode=="function")try{it.setStrictMode(Ql,e)}catch{}}var rt=Math.clz32?Math.clz32:F2,Z2=Math.log,k2=Math.LN2;function F2(e){return e>>>=0,e===0?32:31-(Z2(e)/k2|0)|0}var vi=256,yi=4194304;function La(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function xi(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,d=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var h=l&134217727;return h!==0?(l=h&~d,l!==0?n=La(l):(f&=h,f!==0?n=La(f):a||(a=h&~e,a!==0&&(n=La(a))))):(h=l&~d,h!==0?n=La(h):f!==0?n=La(f):a||(a=l&~e,a!==0&&(n=La(a)))),n===0?0:t!==0&&t!==n&&(t&d)===0&&(d=n&-n,a=t&-t,d>=a||d===32&&(a&4194048)!==0)?t:n}function Kl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function V2(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ju(){var e=vi;return vi<<=1,(vi&4194048)===0&&(vi=256),e}function Wu(){var e=yi;return yi<<=1,(yi&62914560)===0&&(yi=4194304),e}function Xr(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Jl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function I2(e,t,a,l,n,d){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var h=e.entanglements,p=e.expirationTimes,C=e.hiddenUpdates;for(a=f&~a;0<a;){var T=31-rt(a),M=1<<T;h[T]=0,p[T]=-1;var w=C[T];if(w!==null)for(C[T]=null,T=0;T<w.length;T++){var E=w[T];E!==null&&(E.lane&=-536870913)}a&=~M}l!==0&&eo(e,l,0),d!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=d&~(f&~t))}function eo(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-rt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function to(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-rt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Qr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Kr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function ao(){var e=D.p;return e!==0?e:(e=window.event,e===void 0?32:z1(e.type))}function X2(e,t){var a=D.p;try{return D.p=e,t()}finally{D.p=a}}var ua=Math.random().toString(36).slice(2),Xe="__reactFiber$"+ua,et="__reactProps$"+ua,ll="__reactContainer$"+ua,Jr="__reactEvents$"+ua,Q2="__reactListeners$"+ua,K2="__reactHandles$"+ua,lo="__reactResources$"+ua,Wl="__reactMarker$"+ua;function Wr(e){delete e[Xe],delete e[et],delete e[Jr],delete e[Q2],delete e[K2]}function nl(e){var t=e[Xe];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ll]||a[Xe]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=b1(e);e!==null;){if(a=e[Xe])return a;e=b1(e)}return t}e=a,a=e.parentNode}return null}function il(e){if(e=e[Xe]||e[ll]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function en(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function rl(e){var t=e[lo];return t||(t=e[lo]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Pe(e){e[Wl]=!0}var no=new Set,io={};function Ba(e,t){sl(e,t),sl(e+"Capture",t)}function sl(e,t){for(io[e]=t,e=0;e<t.length;e++)no.add(t[e])}var J2=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ro={},so={};function W2(e){return Fr.call(so,e)?!0:Fr.call(ro,e)?!1:J2.test(e)?so[e]=!0:(ro[e]=!0,!1)}function bi(e,t,a){if(W2(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Si(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function qt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var es,uo;function dl(e){if(es===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);es=t&&t[1]||"",uo=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+es+e+uo}var ts=!1;function as(e,t){if(!e||ts)return"";ts=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var M=function(){throw Error()};if(Object.defineProperty(M.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(M,[])}catch(E){var w=E}Reflect.construct(e,[],M)}else{try{M.call()}catch(E){w=E}e.call(M.prototype)}}else{try{throw Error()}catch(E){w=E}(M=e())&&typeof M.catch=="function"&&M.catch(function(){})}}catch(E){if(E&&w&&typeof E.stack=="string")return[E.stack,w.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=l.DetermineComponentFrameRoot(),f=d[0],h=d[1];if(f&&h){var p=f.split(`
`),C=h.split(`
`);for(n=l=0;l<p.length&&!p[l].includes("DetermineComponentFrameRoot");)l++;for(;n<C.length&&!C[n].includes("DetermineComponentFrameRoot");)n++;if(l===p.length||n===C.length)for(l=p.length-1,n=C.length-1;1<=l&&0<=n&&p[l]!==C[n];)n--;for(;1<=l&&0<=n;l--,n--)if(p[l]!==C[n]){if(l!==1||n!==1)do if(l--,n--,0>n||p[l]!==C[n]){var T=`
`+p[l].replace(" at new "," at ");return e.displayName&&T.includes("<anonymous>")&&(T=T.replace("<anonymous>",e.displayName)),T}while(1<=l&&0<=n);break}}}finally{ts=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?dl(a):""}function e3(e){switch(e.tag){case 26:case 27:case 5:return dl(e.type);case 16:return dl("Lazy");case 13:return dl("Suspense");case 19:return dl("SuspenseList");case 0:case 15:return as(e.type,!1);case 11:return as(e.type.render,!1);case 1:return as(e.type,!0);case 31:return dl("Activity");default:return""}}function oo(e){try{var t="";do t+=e3(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function pt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function co(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function t3(e){var t=co(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,d=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,d.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ni(e){e._valueTracker||(e._valueTracker=t3(e))}function fo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=co(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Ci(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var a3=/[\n"\\]/g;function $t(e){return e.replace(a3,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ls(e,t,a,l,n,d,f,h){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+pt(t)):e.value!==""+pt(t)&&(e.value=""+pt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?ns(e,f,pt(t)):a!=null?ns(e,f,pt(a)):l!=null&&e.removeAttribute("value"),n==null&&d!=null&&(e.defaultChecked=!!d),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+pt(h):e.removeAttribute("name")}function mo(e,t,a,l,n,d,f,h){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.type=d),t!=null||a!=null){if(!(d!=="submit"&&d!=="reset"||t!=null))return;a=a!=null?""+pt(a):"",t=t!=null?""+pt(t):a,h||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=h?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function ns(e,t,a){t==="number"&&Ci(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function ul(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+pt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function ho(e,t,a){if(t!=null&&(t=""+pt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+pt(a):""}function go(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(u(92));if(jt(l)){if(1<l.length)throw Error(u(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=pt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function ol(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var l3=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function po(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||l3.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function $o(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&po(e,n,l)}else for(var d in t)t.hasOwnProperty(d)&&po(e,d,t[d])}function is(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var n3=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),i3=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function wi(e){return i3.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var rs=null;function ss(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var cl=null,fl=null;function vo(e){var t=il(e);if(t&&(e=t.stateNode)){var a=e[et]||null;e:switch(e=t.stateNode,t.type){case"input":if(ls(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+$t(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[et]||null;if(!n)throw Error(u(90));ls(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&fo(l)}break e;case"textarea":ho(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&ul(e,!!a.multiple,t,!1)}}}var ds=!1;function yo(e,t,a){if(ds)return e(t,a);ds=!0;try{var l=e(t);return l}finally{if(ds=!1,(cl!==null||fl!==null)&&(or(),cl&&(t=cl,e=fl,fl=cl=null,vo(t),e)))for(t=0;t<e.length;t++)vo(e[t])}}function tn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[et]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),us=!1;if(Gt)try{var an={};Object.defineProperty(an,"passive",{get:function(){us=!0}}),window.addEventListener("test",an,an),window.removeEventListener("test",an,an)}catch{us=!1}var oa=null,os=null,Ei=null;function xo(){if(Ei)return Ei;var e,t=os,a=t.length,l,n="value"in oa?oa.value:oa.textContent,d=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[d-l];l++);return Ei=n.slice(e,1<l?1-l:void 0)}function ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ai(){return!0}function bo(){return!1}function tt(e){function t(a,l,n,d,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=d,this.target=f,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(a=e[h],this[h]=a?a(d):d[h]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Ai:bo,this.isPropagationStopped=bo,this}return $(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ai)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ai)},persist:function(){},isPersistent:Ai}),t}var Ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ti=tt(Ha),ln=$({},Ha,{view:0,detail:0}),r3=tt(ln),cs,fs,nn,Oi=$({},ln,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==nn&&(nn&&e.type==="mousemove"?(cs=e.screenX-nn.screenX,fs=e.screenY-nn.screenY):fs=cs=0,nn=e),cs)},movementY:function(e){return"movementY"in e?e.movementY:fs}}),So=tt(Oi),s3=$({},Oi,{dataTransfer:0}),d3=tt(s3),u3=$({},ln,{relatedTarget:0}),ms=tt(u3),o3=$({},Ha,{animationName:0,elapsedTime:0,pseudoElement:0}),c3=tt(o3),f3=$({},Ha,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),m3=tt(f3),h3=$({},Ha,{data:0}),No=tt(h3),g3={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},p3={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$3={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function v3(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$3[e])?!!t[e]:!1}function hs(){return v3}var y3=$({},ln,{key:function(e){if(e.key){var t=g3[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?p3[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hs,charCode:function(e){return e.type==="keypress"?ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),x3=tt(y3),b3=$({},Oi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Co=tt(b3),S3=$({},ln,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hs}),N3=tt(S3),C3=$({},Ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),w3=tt(C3),E3=$({},Oi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),j3=tt(E3),A3=$({},Ha,{newState:0,oldState:0}),T3=tt(A3),O3=[9,13,27,32],gs=Gt&&"CompositionEvent"in window,rn=null;Gt&&"documentMode"in document&&(rn=document.documentMode);var R3=Gt&&"TextEvent"in window&&!rn,wo=Gt&&(!gs||rn&&8<rn&&11>=rn),Eo=" ",jo=!1;function Ao(e,t){switch(e){case"keyup":return O3.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function To(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ml=!1;function _3(e,t){switch(e){case"compositionend":return To(t);case"keypress":return t.which!==32?null:(jo=!0,Eo);case"textInput":return e=t.data,e===Eo&&jo?null:e;default:return null}}function M3(e,t){if(ml)return e==="compositionend"||!gs&&Ao(e,t)?(e=xo(),Ei=os=oa=null,ml=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return wo&&t.locale!=="ko"?null:t.data;default:return null}}var D3={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Oo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!D3[e.type]:t==="textarea"}function Ro(e,t,a,l){cl?fl?fl.push(l):fl=[l]:cl=l,t=pr(t,"onChange"),0<t.length&&(a=new Ti("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var sn=null,dn=null;function U3(e){c1(e,0)}function Ri(e){var t=en(e);if(fo(t))return e}function _o(e,t){if(e==="change")return t}var Mo=!1;if(Gt){var ps;if(Gt){var $s="oninput"in document;if(!$s){var Do=document.createElement("div");Do.setAttribute("oninput","return;"),$s=typeof Do.oninput=="function"}ps=$s}else ps=!1;Mo=ps&&(!document.documentMode||9<document.documentMode)}function Uo(){sn&&(sn.detachEvent("onpropertychange",zo),dn=sn=null)}function zo(e){if(e.propertyName==="value"&&Ri(dn)){var t=[];Ro(t,dn,e,ss(e)),yo(U3,t)}}function z3(e,t,a){e==="focusin"?(Uo(),sn=t,dn=a,sn.attachEvent("onpropertychange",zo)):e==="focusout"&&Uo()}function L3(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ri(dn)}function B3(e,t){if(e==="click")return Ri(t)}function H3(e,t){if(e==="input"||e==="change")return Ri(t)}function q3(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var st=typeof Object.is=="function"?Object.is:q3;function un(e,t){if(st(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!Fr.call(t,n)||!st(e[n],t[n]))return!1}return!0}function Lo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bo(e,t){var a=Lo(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Lo(a)}}function Ho(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ho(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function qo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ci(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ci(e.document)}return t}function vs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var G3=Gt&&"documentMode"in document&&11>=document.documentMode,hl=null,ys=null,on=null,xs=!1;function Go(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;xs||hl==null||hl!==Ci(l)||(l=hl,"selectionStart"in l&&vs(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),on&&un(on,l)||(on=l,l=pr(ys,"onSelect"),0<l.length&&(t=new Ti("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=hl)))}function qa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var gl={animationend:qa("Animation","AnimationEnd"),animationiteration:qa("Animation","AnimationIteration"),animationstart:qa("Animation","AnimationStart"),transitionrun:qa("Transition","TransitionRun"),transitionstart:qa("Transition","TransitionStart"),transitioncancel:qa("Transition","TransitionCancel"),transitionend:qa("Transition","TransitionEnd")},bs={},Po={};Gt&&(Po=document.createElement("div").style,"AnimationEvent"in window||(delete gl.animationend.animation,delete gl.animationiteration.animation,delete gl.animationstart.animation),"TransitionEvent"in window||delete gl.transitionend.transition);function Ga(e){if(bs[e])return bs[e];if(!gl[e])return e;var t=gl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Po)return bs[e]=t[a];return e}var Yo=Ga("animationend"),Zo=Ga("animationiteration"),ko=Ga("animationstart"),P3=Ga("transitionrun"),Y3=Ga("transitionstart"),Z3=Ga("transitioncancel"),Fo=Ga("transitionend"),Vo=new Map,Ss="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ss.push("scrollEnd");function At(e,t){Vo.set(e,t),Ba(t,[e])}var Io=new WeakMap;function vt(e,t){if(typeof e=="object"&&e!==null){var a=Io.get(e);return a!==void 0?a:(t={value:e,source:t,stack:oo(t)},Io.set(e,t),t)}return{value:e,source:t,stack:oo(t)}}var yt=[],pl=0,Ns=0;function _i(){for(var e=pl,t=Ns=pl=0;t<e;){var a=yt[t];yt[t++]=null;var l=yt[t];yt[t++]=null;var n=yt[t];yt[t++]=null;var d=yt[t];if(yt[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}d!==0&&Xo(a,n,d)}}function Mi(e,t,a,l){yt[pl++]=e,yt[pl++]=t,yt[pl++]=a,yt[pl++]=l,Ns|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Cs(e,t,a,l){return Mi(e,t,a,l),Di(e)}function $l(e,t){return Mi(e,null,null,t),Di(e)}function Xo(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,d=e.return;d!==null;)d.childLanes|=a,l=d.alternate,l!==null&&(l.childLanes|=a),d.tag===22&&(e=d.stateNode,e===null||e._visibility&1||(n=!0)),e=d,d=d.return;return e.tag===3?(d=e.stateNode,n&&t!==null&&(n=31-rt(a),e=d.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),d):null}function Di(e){if(50<Un)throw Un=0,Od=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var vl={};function k3(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,a,l){return new k3(e,t,a,l)}function ws(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Pt(e,t){var a=e.alternate;return a===null?(a=dt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Qo(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ui(e,t,a,l,n,d){var f=0;if(l=e,typeof e=="function")ws(e)&&(f=1);else if(typeof e=="string")f=Vm(e,a,Me.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=dt(31,a,t,n),e.elementType=oe,e.lanes=d,e;case B:return Pa(a.children,n,d,t);case z:f=8,n|=24;break;case P:return e=dt(12,a,t,n|2),e.elementType=P,e.lanes=d,e;case ie:return e=dt(13,a,t,n),e.elementType=ie,e.lanes=d,e;case $e:return e=dt(19,a,t,n),e.elementType=$e,e.lanes=d,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case q:f=10;break e;case U:f=9;break e;case fe:f=11;break e;case re:f=14;break e;case K:f=16,l=null;break e}f=29,a=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=dt(f,a,t,n),t.elementType=e,t.type=l,t.lanes=d,t}function Pa(e,t,a,l){return e=dt(7,e,l,t),e.lanes=a,e}function Es(e,t,a){return e=dt(6,e,null,t),e.lanes=a,e}function js(e,t,a){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var yl=[],xl=0,zi=null,Li=0,xt=[],bt=0,Ya=null,Yt=1,Zt="";function Za(e,t){yl[xl++]=Li,yl[xl++]=zi,zi=e,Li=t}function Ko(e,t,a){xt[bt++]=Yt,xt[bt++]=Zt,xt[bt++]=Ya,Ya=e;var l=Yt;e=Zt;var n=32-rt(l)-1;l&=~(1<<n),a+=1;var d=32-rt(t)+n;if(30<d){var f=n-n%5;d=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Yt=1<<32-rt(t)+n|a<<n|l,Zt=d+e}else Yt=1<<d|a<<n|l,Zt=e}function As(e){e.return!==null&&(Za(e,1),Ko(e,1,0))}function Ts(e){for(;e===zi;)zi=yl[--xl],yl[xl]=null,Li=yl[--xl],yl[xl]=null;for(;e===Ya;)Ya=xt[--bt],xt[bt]=null,Zt=xt[--bt],xt[bt]=null,Yt=xt[--bt],xt[bt]=null}var Je=null,Re=null,me=!1,ka=null,Dt=!1,Os=Error(u(519));function Fa(e){var t=Error(u(418,""));throw mn(vt(t,e)),Os}function Jo(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Xe]=e,t[et]=l,a){case"dialog":ne("cancel",t),ne("close",t);break;case"iframe":case"object":case"embed":ne("load",t);break;case"video":case"audio":for(a=0;a<Ln.length;a++)ne(Ln[a],t);break;case"source":ne("error",t);break;case"img":case"image":case"link":ne("error",t),ne("load",t);break;case"details":ne("toggle",t);break;case"input":ne("invalid",t),mo(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Ni(t);break;case"select":ne("invalid",t);break;case"textarea":ne("invalid",t),go(t,l.value,l.defaultValue,l.children),Ni(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||g1(t.textContent,a)?(l.popover!=null&&(ne("beforetoggle",t),ne("toggle",t)),l.onScroll!=null&&ne("scroll",t),l.onScrollEnd!=null&&ne("scrollend",t),l.onClick!=null&&(t.onclick=$r),t=!0):t=!1,t||Fa(e)}function Wo(e){for(Je=e.return;Je;)switch(Je.tag){case 5:case 13:Dt=!1;return;case 27:case 3:Dt=!0;return;default:Je=Je.return}}function cn(e){if(e!==Je)return!1;if(!me)return Wo(e),me=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Fd(e.type,e.memoizedProps)),a=!a),a&&Re&&Fa(e),Wo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Re=Ot(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Re=null}}else t===27?(t=Re,Ea(e.type)?(e=Qd,Qd=null,Re=e):Re=t):Re=Je?Ot(e.stateNode.nextSibling):null;return!0}function fn(){Re=Je=null,me=!1}function ec(){var e=ka;return e!==null&&(nt===null?nt=e:nt.push.apply(nt,e),ka=null),e}function mn(e){ka===null?ka=[e]:ka.push(e)}var Rs=W(null),Va=null,kt=null;function ca(e,t,a){de(Rs,t._currentValue),t._currentValue=a}function Ft(e){e._currentValue=Rs.current,he(Rs)}function _s(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Ms(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var d=n.dependencies;if(d!==null){var f=n.child;d=d.firstContext;e:for(;d!==null;){var h=d;d=n;for(var p=0;p<t.length;p++)if(h.context===t[p]){d.lanes|=a,h=d.alternate,h!==null&&(h.lanes|=a),_s(d.return,a,e),l||(f=null);break e}d=h.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(u(341));f.lanes|=a,d=f.alternate,d!==null&&(d.lanes|=a),_s(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function hn(e,t,a,l){e=null;for(var n=t,d=!1;n!==null;){if(!d){if((n.flags&524288)!==0)d=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(u(387));if(f=f.memoizedProps,f!==null){var h=n.type;st(n.pendingProps.value,f.value)||(e!==null?e.push(h):e=[h])}}else if(n===hi.current){if(f=n.alternate,f===null)throw Error(u(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Yn):e=[Yn])}n=n.return}e!==null&&Ms(t,e,a,l),t.flags|=262144}function Bi(e){for(e=e.firstContext;e!==null;){if(!st(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ia(e){Va=e,kt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Qe(e){return tc(Va,e)}function Hi(e,t){return Va===null&&Ia(e),tc(e,t)}function tc(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},kt===null){if(e===null)throw Error(u(308));kt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else kt=kt.next=t;return a}var F3=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},V3=i.unstable_scheduleCallback,I3=i.unstable_NormalPriority,qe={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ds(){return{controller:new F3,data:new Map,refCount:0}}function gn(e){e.refCount--,e.refCount===0&&V3(I3,function(){e.controller.abort()})}var pn=null,Us=0,bl=0,Sl=null;function X3(e,t){if(pn===null){var a=pn=[];Us=0,bl=Ld(),Sl={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Us++,t.then(ac,ac),t}function ac(){if(--Us===0&&pn!==null){Sl!==null&&(Sl.status="fulfilled");var e=pn;pn=null,bl=0,Sl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Q3(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var lc=R.S;R.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&X3(e,t),lc!==null&&lc(e,t)};var Xa=W(null);function zs(){var e=Xa.current;return e!==null?e:Ne.pooledCache}function qi(e,t){t===null?de(Xa,Xa.current):de(Xa,t.pool)}function nc(){var e=zs();return e===null?null:{parent:qe._currentValue,pool:e}}var $n=Error(u(460)),ic=Error(u(474)),Gi=Error(u(542)),Ls={then:function(){}};function rc(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Pi(){}function sc(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Pi,Pi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,uc(e),e;default:if(typeof t.status=="string")t.then(Pi,Pi);else{if(e=Ne,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,uc(e),e}throw vn=t,$n}}var vn=null;function dc(){if(vn===null)throw Error(u(459));var e=vn;return vn=null,e}function uc(e){if(e===$n||e===Gi)throw Error(u(483))}var fa=!1;function Bs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ma(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ha(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ge&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=Di(e),Xo(e,null,a),t}return Mi(e,l,t,a),Di(e)}function yn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,to(e,a)}}function qs(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,d=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};d===null?n=d=f:d=d.next=f,a=a.next}while(a!==null);d===null?n=d=t:d=d.next=t}else n=d=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:d,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Gs=!1;function xn(){if(Gs){var e=Sl;if(e!==null)throw e}}function bn(e,t,a,l){Gs=!1;var n=e.updateQueue;fa=!1;var d=n.firstBaseUpdate,f=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var p=h,C=p.next;p.next=null,f===null?d=C:f.next=C,f=p;var T=e.alternate;T!==null&&(T=T.updateQueue,h=T.lastBaseUpdate,h!==f&&(h===null?T.firstBaseUpdate=C:h.next=C,T.lastBaseUpdate=p))}if(d!==null){var M=n.baseState;f=0,T=C=p=null,h=d;do{var w=h.lane&-536870913,E=w!==h.lane;if(E?(ue&w)===w:(l&w)===w){w!==0&&w===bl&&(Gs=!0),T!==null&&(T=T.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var Q=e,F=h;w=t;var xe=a;switch(F.tag){case 1:if(Q=F.payload,typeof Q=="function"){M=Q.call(xe,M,w);break e}M=Q;break e;case 3:Q.flags=Q.flags&-65537|128;case 0:if(Q=F.payload,w=typeof Q=="function"?Q.call(xe,M,w):Q,w==null)break e;M=$({},M,w);break e;case 2:fa=!0}}w=h.callback,w!==null&&(e.flags|=64,E&&(e.flags|=8192),E=n.callbacks,E===null?n.callbacks=[w]:E.push(w))}else E={lane:w,tag:h.tag,payload:h.payload,callback:h.callback,next:null},T===null?(C=T=E,p=M):T=T.next=E,f|=w;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;E=h,h=E.next,E.next=null,n.lastBaseUpdate=E,n.shared.pending=null}}while(!0);T===null&&(p=M),n.baseState=p,n.firstBaseUpdate=C,n.lastBaseUpdate=T,d===null&&(n.shared.lanes=0),Sa|=f,e.lanes=f,e.memoizedState=M}}function oc(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function cc(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)oc(a[e],t)}var Nl=W(null),Yi=W(0);function fc(e,t){e=Wt,de(Yi,e),de(Nl,t),Wt=e|t.baseLanes}function Ps(){de(Yi,Wt),de(Nl,Nl.current)}function Ys(){Wt=Yi.current,he(Nl),he(Yi)}var ga=0,te=null,ve=null,ze=null,Zi=!1,Cl=!1,Qa=!1,ki=0,Sn=0,wl=null,K3=0;function De(){throw Error(u(321))}function Zs(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!st(e[a],t[a]))return!1;return!0}function ks(e,t,a,l,n,d){return ga=d,te=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=e===null||e.memoizedState===null?Xc:Qc,Qa=!1,d=a(l,n),Qa=!1,Cl&&(d=hc(t,a,l,n)),mc(e),d}function mc(e){R.H=Ki;var t=ve!==null&&ve.next!==null;if(ga=0,ze=ve=te=null,Zi=!1,Sn=0,wl=null,t)throw Error(u(300));e===null||Ye||(e=e.dependencies,e!==null&&Bi(e)&&(Ye=!0))}function hc(e,t,a,l){te=e;var n=0;do{if(Cl&&(wl=null),Sn=0,Cl=!1,25<=n)throw Error(u(301));if(n+=1,ze=ve=null,e.updateQueue!=null){var d=e.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}R.H=nm,d=t(a,l)}while(Cl);return d}function J3(){var e=R.H,t=e.useState()[0];return t=typeof t.then=="function"?Nn(t):t,e=e.useState()[0],(ve!==null?ve.memoizedState:null)!==e&&(te.flags|=1024),t}function Fs(){var e=ki!==0;return ki=0,e}function Vs(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Is(e){if(Zi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Zi=!1}ga=0,ze=ve=te=null,Cl=!1,Sn=ki=0,wl=null}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ze===null?te.memoizedState=ze=e:ze=ze.next=e,ze}function Le(){if(ve===null){var e=te.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=ze===null?te.memoizedState:ze.next;if(t!==null)ze=t,ve=e;else{if(e===null)throw te.alternate===null?Error(u(467)):Error(u(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},ze===null?te.memoizedState=ze=e:ze=ze.next=e}return ze}function Xs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Nn(e){var t=Sn;return Sn+=1,wl===null&&(wl=[]),e=sc(wl,e,t),t=te,(ze===null?t.memoizedState:ze.next)===null&&(t=t.alternate,R.H=t===null||t.memoizedState===null?Xc:Qc),e}function Fi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Nn(e);if(e.$$typeof===q)return Qe(e)}throw Error(u(438,String(e)))}function Qs(e){var t=null,a=te.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=te.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Xs(),te.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Te;return t.index++,a}function Vt(e,t){return typeof t=="function"?t(e):t}function Vi(e){var t=Le();return Ks(t,ve,e)}function Ks(e,t,a){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=a;var n=e.baseQueue,d=l.pending;if(d!==null){if(n!==null){var f=n.next;n.next=d.next,d.next=f}t.baseQueue=n=d,l.pending=null}if(d=e.baseState,n===null)e.memoizedState=d;else{t=n.next;var h=f=null,p=null,C=t,T=!1;do{var M=C.lane&-536870913;if(M!==C.lane?(ue&M)===M:(ga&M)===M){var w=C.revertLane;if(w===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),M===bl&&(T=!0);else if((ga&w)===w){C=C.next,w===bl&&(T=!0);continue}else M={lane:0,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},p===null?(h=p=M,f=d):p=p.next=M,te.lanes|=w,Sa|=w;M=C.action,Qa&&a(d,M),d=C.hasEagerState?C.eagerState:a(d,M)}else w={lane:M,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},p===null?(h=p=w,f=d):p=p.next=w,te.lanes|=M,Sa|=M;C=C.next}while(C!==null&&C!==t);if(p===null?f=d:p.next=h,!st(d,e.memoizedState)&&(Ye=!0,T&&(a=Sl,a!==null)))throw a;e.memoizedState=d,e.baseState=f,e.baseQueue=p,l.lastRenderedState=d}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Js(e){var t=Le(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,d=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do d=e(d,f.action),f=f.next;while(f!==n);st(d,t.memoizedState)||(Ye=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),a.lastRenderedState=d}return[d,l]}function gc(e,t,a){var l=te,n=Le(),d=me;if(d){if(a===void 0)throw Error(u(407));a=a()}else a=t();var f=!st((ve||n).memoizedState,a);f&&(n.memoizedState=a,Ye=!0),n=n.queue;var h=vc.bind(null,l,n,e);if(Cn(2048,8,h,[e]),n.getSnapshot!==t||f||ze!==null&&ze.memoizedState.tag&1){if(l.flags|=2048,El(9,Ii(),$c.bind(null,l,n,a,t),null),Ne===null)throw Error(u(349));d||(ga&124)!==0||pc(l,t,a)}return a}function pc(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=te.updateQueue,t===null?(t=Xs(),te.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function $c(e,t,a,l){t.value=a,t.getSnapshot=l,yc(t)&&xc(e)}function vc(e,t,a){return a(function(){yc(t)&&xc(e)})}function yc(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!st(e,a)}catch{return!0}}function xc(e){var t=$l(e,2);t!==null&&mt(t,e,2)}function Ws(e){var t=at();if(typeof e=="function"){var a=e;if(e=a(),Qa){da(!0);try{a()}finally{da(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:e},t}function bc(e,t,a,l){return e.baseState=a,Ks(e,ve,typeof l=="function"?l:Vt)}function W3(e,t,a,l,n){if(Qi(e))throw Error(u(485));if(e=t.action,e!==null){var d={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){d.listeners.push(f)}};R.T!==null?a(!0):d.isTransition=!1,l(d),a=t.pending,a===null?(d.next=t.pending=d,Sc(t,d)):(d.next=a.next,t.pending=a.next=d)}}function Sc(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var d=R.T,f={};R.T=f;try{var h=a(n,l),p=R.S;p!==null&&p(f,h),Nc(e,t,h)}catch(C){ed(e,t,C)}finally{R.T=d}}else try{d=a(n,l),Nc(e,t,d)}catch(C){ed(e,t,C)}}function Nc(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Cc(e,t,l)},function(l){return ed(e,t,l)}):Cc(e,t,a)}function Cc(e,t,a){t.status="fulfilled",t.value=a,wc(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Sc(e,a)))}function ed(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,wc(t),t=t.next;while(t!==l)}e.action=null}function wc(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ec(e,t){return t}function jc(e,t){if(me){var a=Ne.formState;if(a!==null){e:{var l=te;if(me){if(Re){t:{for(var n=Re,d=Dt;n.nodeType!==8;){if(!d){n=null;break t}if(n=Ot(n.nextSibling),n===null){n=null;break t}}d=n.data,n=d==="F!"||d==="F"?n:null}if(n){Re=Ot(n.nextSibling),l=n.data==="F!";break e}}Fa(l)}l=!1}l&&(t=a[0])}}return a=at(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ec,lastRenderedState:t},a.queue=l,a=Fc.bind(null,te,l),l.dispatch=a,l=Ws(!1),d=id.bind(null,te,!1,l.queue),l=at(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=W3.bind(null,te,n,d,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function Ac(e){var t=Le();return Tc(t,ve,e)}function Tc(e,t,a){if(t=Ks(e,t,Ec)[0],e=Vi(Vt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Nn(t)}catch(f){throw f===$n?Gi:f}else l=t;t=Le();var n=t.queue,d=n.dispatch;return a!==t.memoizedState&&(te.flags|=2048,El(9,Ii(),em.bind(null,n,a),null)),[l,d,e]}function em(e,t){e.action=t}function Oc(e){var t=Le(),a=ve;if(a!==null)return Tc(t,a,e);Le(),t=t.memoizedState,a=Le();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function El(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=te.updateQueue,t===null&&(t=Xs(),te.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Ii(){return{destroy:void 0,resource:void 0}}function Rc(){return Le().memoizedState}function Xi(e,t,a,l){var n=at();l=l===void 0?null:l,te.flags|=e,n.memoizedState=El(1|t,Ii(),a,l)}function Cn(e,t,a,l){var n=Le();l=l===void 0?null:l;var d=n.memoizedState.inst;ve!==null&&l!==null&&Zs(l,ve.memoizedState.deps)?n.memoizedState=El(t,d,a,l):(te.flags|=e,n.memoizedState=El(1|t,d,a,l))}function _c(e,t){Xi(8390656,8,e,t)}function Mc(e,t){Cn(2048,8,e,t)}function Dc(e,t){return Cn(4,2,e,t)}function Uc(e,t){return Cn(4,4,e,t)}function zc(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Lc(e,t,a){a=a!=null?a.concat([e]):null,Cn(4,4,zc.bind(null,t,e),a)}function td(){}function Bc(e,t){var a=Le();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Zs(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Hc(e,t){var a=Le();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Zs(t,l[1]))return l[0];if(l=e(),Qa){da(!0);try{e()}finally{da(!1)}}return a.memoizedState=[l,t],l}function ad(e,t,a){return a===void 0||(ga&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=P0(),te.lanes|=e,Sa|=e,a)}function qc(e,t,a,l){return st(a,t)?a:Nl.current!==null?(e=ad(e,a,l),st(e,t)||(Ye=!0),e):(ga&42)===0?(Ye=!0,e.memoizedState=a):(e=P0(),te.lanes|=e,Sa|=e,t)}function Gc(e,t,a,l,n){var d=D.p;D.p=d!==0&&8>d?d:8;var f=R.T,h={};R.T=h,id(e,!1,t,a);try{var p=n(),C=R.S;if(C!==null&&C(h,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var T=Q3(p,l);wn(e,t,T,ft(e))}else wn(e,t,l,ft(e))}catch(M){wn(e,t,{then:function(){},status:"rejected",reason:M},ft())}finally{D.p=d,R.T=f}}function tm(){}function ld(e,t,a,l){if(e.tag!==5)throw Error(u(476));var n=Pc(e).queue;Gc(e,n,t,H,a===null?tm:function(){return Yc(e),a(l)})}function Pc(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:H,baseState:H,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:H},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Yc(e){var t=Pc(e).next.queue;wn(e,t,{},ft())}function nd(){return Qe(Yn)}function Zc(){return Le().memoizedState}function kc(){return Le().memoizedState}function am(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=ft();e=ma(a);var l=ha(t,e,a);l!==null&&(mt(l,t,a),yn(l,t,a)),t={cache:Ds()},e.payload=t;return}t=t.return}}function lm(e,t,a){var l=ft();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Qi(e)?Vc(t,a):(a=Cs(e,t,a,l),a!==null&&(mt(a,e,l),Ic(a,t,l)))}function Fc(e,t,a){var l=ft();wn(e,t,a,l)}function wn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Qi(e))Vc(t,n);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var f=t.lastRenderedState,h=d(f,a);if(n.hasEagerState=!0,n.eagerState=h,st(h,f))return Mi(e,t,n,0),Ne===null&&_i(),!1}catch{}finally{}if(a=Cs(e,t,n,l),a!==null)return mt(a,e,l),Ic(a,t,l),!0}return!1}function id(e,t,a,l){if(l={lane:2,revertLane:Ld(),action:l,hasEagerState:!1,eagerState:null,next:null},Qi(e)){if(t)throw Error(u(479))}else t=Cs(e,a,l,2),t!==null&&mt(t,e,2)}function Qi(e){var t=e.alternate;return e===te||t!==null&&t===te}function Vc(e,t){Cl=Zi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Ic(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,to(e,a)}}var Ki={readContext:Qe,use:Fi,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useLayoutEffect:De,useInsertionEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useSyncExternalStore:De,useId:De,useHostTransitionStatus:De,useFormState:De,useActionState:De,useOptimistic:De,useMemoCache:De,useCacheRefresh:De},Xc={readContext:Qe,use:Fi,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:Qe,useEffect:_c,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Xi(4194308,4,zc.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){Xi(4,2,e,t)},useMemo:function(e,t){var a=at();t=t===void 0?null:t;var l=e();if(Qa){da(!0);try{e()}finally{da(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=at();if(a!==void 0){var n=a(t);if(Qa){da(!0);try{a(t)}finally{da(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=lm.bind(null,te,e),[l.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:function(e){e=Ws(e);var t=e.queue,a=Fc.bind(null,te,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:td,useDeferredValue:function(e,t){var a=at();return ad(a,e,t)},useTransition:function(){var e=Ws(!1);return e=Gc.bind(null,te,e.queue,!0,!1),at().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=te,n=at();if(me){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),Ne===null)throw Error(u(349));(ue&124)!==0||pc(l,t,a)}n.memoizedState=a;var d={value:a,getSnapshot:t};return n.queue=d,_c(vc.bind(null,l,d,e),[e]),l.flags|=2048,El(9,Ii(),$c.bind(null,l,d,a,t),null),a},useId:function(){var e=at(),t=Ne.identifierPrefix;if(me){var a=Zt,l=Yt;a=(l&~(1<<32-rt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=ki++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=K3++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:nd,useFormState:jc,useActionState:jc,useOptimistic:function(e){var t=at();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=id.bind(null,te,!0,a),a.dispatch=t,[e,t]},useMemoCache:Qs,useCacheRefresh:function(){return at().memoizedState=am.bind(null,te)}},Qc={readContext:Qe,use:Fi,useCallback:Bc,useContext:Qe,useEffect:Mc,useImperativeHandle:Lc,useInsertionEffect:Dc,useLayoutEffect:Uc,useMemo:Hc,useReducer:Vi,useRef:Rc,useState:function(){return Vi(Vt)},useDebugValue:td,useDeferredValue:function(e,t){var a=Le();return qc(a,ve.memoizedState,e,t)},useTransition:function(){var e=Vi(Vt)[0],t=Le().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:gc,useId:Zc,useHostTransitionStatus:nd,useFormState:Ac,useActionState:Ac,useOptimistic:function(e,t){var a=Le();return bc(a,ve,e,t)},useMemoCache:Qs,useCacheRefresh:kc},nm={readContext:Qe,use:Fi,useCallback:Bc,useContext:Qe,useEffect:Mc,useImperativeHandle:Lc,useInsertionEffect:Dc,useLayoutEffect:Uc,useMemo:Hc,useReducer:Js,useRef:Rc,useState:function(){return Js(Vt)},useDebugValue:td,useDeferredValue:function(e,t){var a=Le();return ve===null?ad(a,e,t):qc(a,ve.memoizedState,e,t)},useTransition:function(){var e=Js(Vt)[0],t=Le().memoizedState;return[typeof e=="boolean"?e:Nn(e),t]},useSyncExternalStore:gc,useId:Zc,useHostTransitionStatus:nd,useFormState:Oc,useActionState:Oc,useOptimistic:function(e,t){var a=Le();return ve!==null?bc(a,ve,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Qs,useCacheRefresh:kc},jl=null,En=0;function Ji(e){var t=En;return En+=1,jl===null&&(jl=[]),sc(jl,e,t)}function jn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Wi(e,t){throw t.$$typeof===j?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Kc(e){var t=e._init;return t(e._payload)}function Jc(e){function t(S,b){if(e){var N=S.deletions;N===null?(S.deletions=[b],S.flags|=16):N.push(b)}}function a(S,b){if(!e)return null;for(;b!==null;)t(S,b),b=b.sibling;return null}function l(S){for(var b=new Map;S!==null;)S.key!==null?b.set(S.key,S):b.set(S.index,S),S=S.sibling;return b}function n(S,b){return S=Pt(S,b),S.index=0,S.sibling=null,S}function d(S,b,N){return S.index=N,e?(N=S.alternate,N!==null?(N=N.index,N<b?(S.flags|=67108866,b):N):(S.flags|=67108866,b)):(S.flags|=1048576,b)}function f(S){return e&&S.alternate===null&&(S.flags|=67108866),S}function h(S,b,N,_){return b===null||b.tag!==6?(b=Es(N,S.mode,_),b.return=S,b):(b=n(b,N),b.return=S,b)}function p(S,b,N,_){var Y=N.type;return Y===B?T(S,b,N.props.children,_,N.key):b!==null&&(b.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===K&&Kc(Y)===b.type)?(b=n(b,N.props),jn(b,N),b.return=S,b):(b=Ui(N.type,N.key,N.props,null,S.mode,_),jn(b,N),b.return=S,b)}function C(S,b,N,_){return b===null||b.tag!==4||b.stateNode.containerInfo!==N.containerInfo||b.stateNode.implementation!==N.implementation?(b=js(N,S.mode,_),b.return=S,b):(b=n(b,N.children||[]),b.return=S,b)}function T(S,b,N,_,Y){return b===null||b.tag!==7?(b=Pa(N,S.mode,_,Y),b.return=S,b):(b=n(b,N),b.return=S,b)}function M(S,b,N){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Es(""+b,S.mode,N),b.return=S,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case A:return N=Ui(b.type,b.key,b.props,null,S.mode,N),jn(N,b),N.return=S,N;case O:return b=js(b,S.mode,N),b.return=S,b;case K:var _=b._init;return b=_(b._payload),M(S,b,N)}if(jt(b)||Oe(b))return b=Pa(b,S.mode,N,null),b.return=S,b;if(typeof b.then=="function")return M(S,Ji(b),N);if(b.$$typeof===q)return M(S,Hi(S,b),N);Wi(S,b)}return null}function w(S,b,N,_){var Y=b!==null?b.key:null;if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return Y!==null?null:h(S,b,""+N,_);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case A:return N.key===Y?p(S,b,N,_):null;case O:return N.key===Y?C(S,b,N,_):null;case K:return Y=N._init,N=Y(N._payload),w(S,b,N,_)}if(jt(N)||Oe(N))return Y!==null?null:T(S,b,N,_,null);if(typeof N.then=="function")return w(S,b,Ji(N),_);if(N.$$typeof===q)return w(S,b,Hi(S,N),_);Wi(S,N)}return null}function E(S,b,N,_,Y){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return S=S.get(N)||null,h(b,S,""+_,Y);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case A:return S=S.get(_.key===null?N:_.key)||null,p(b,S,_,Y);case O:return S=S.get(_.key===null?N:_.key)||null,C(b,S,_,Y);case K:var ae=_._init;return _=ae(_._payload),E(S,b,N,_,Y)}if(jt(_)||Oe(_))return S=S.get(N)||null,T(b,S,_,Y,null);if(typeof _.then=="function")return E(S,b,N,Ji(_),Y);if(_.$$typeof===q)return E(S,b,N,Hi(b,_),Y);Wi(b,_)}return null}function Q(S,b,N,_){for(var Y=null,ae=null,k=b,I=b=0,ke=null;k!==null&&I<N.length;I++){k.index>I?(ke=k,k=null):ke=k.sibling;var ce=w(S,k,N[I],_);if(ce===null){k===null&&(k=ke);break}e&&k&&ce.alternate===null&&t(S,k),b=d(ce,b,I),ae===null?Y=ce:ae.sibling=ce,ae=ce,k=ke}if(I===N.length)return a(S,k),me&&Za(S,I),Y;if(k===null){for(;I<N.length;I++)k=M(S,N[I],_),k!==null&&(b=d(k,b,I),ae===null?Y=k:ae.sibling=k,ae=k);return me&&Za(S,I),Y}for(k=l(k);I<N.length;I++)ke=E(k,S,I,N[I],_),ke!==null&&(e&&ke.alternate!==null&&k.delete(ke.key===null?I:ke.key),b=d(ke,b,I),ae===null?Y=ke:ae.sibling=ke,ae=ke);return e&&k.forEach(function(Ra){return t(S,Ra)}),me&&Za(S,I),Y}function F(S,b,N,_){if(N==null)throw Error(u(151));for(var Y=null,ae=null,k=b,I=b=0,ke=null,ce=N.next();k!==null&&!ce.done;I++,ce=N.next()){k.index>I?(ke=k,k=null):ke=k.sibling;var Ra=w(S,k,ce.value,_);if(Ra===null){k===null&&(k=ke);break}e&&k&&Ra.alternate===null&&t(S,k),b=d(Ra,b,I),ae===null?Y=Ra:ae.sibling=Ra,ae=Ra,k=ke}if(ce.done)return a(S,k),me&&Za(S,I),Y;if(k===null){for(;!ce.done;I++,ce=N.next())ce=M(S,ce.value,_),ce!==null&&(b=d(ce,b,I),ae===null?Y=ce:ae.sibling=ce,ae=ce);return me&&Za(S,I),Y}for(k=l(k);!ce.done;I++,ce=N.next())ce=E(k,S,I,ce.value,_),ce!==null&&(e&&ce.alternate!==null&&k.delete(ce.key===null?I:ce.key),b=d(ce,b,I),ae===null?Y=ce:ae.sibling=ce,ae=ce);return e&&k.forEach(function(ih){return t(S,ih)}),me&&Za(S,I),Y}function xe(S,b,N,_){if(typeof N=="object"&&N!==null&&N.type===B&&N.key===null&&(N=N.props.children),typeof N=="object"&&N!==null){switch(N.$$typeof){case A:e:{for(var Y=N.key;b!==null;){if(b.key===Y){if(Y=N.type,Y===B){if(b.tag===7){a(S,b.sibling),_=n(b,N.props.children),_.return=S,S=_;break e}}else if(b.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===K&&Kc(Y)===b.type){a(S,b.sibling),_=n(b,N.props),jn(_,N),_.return=S,S=_;break e}a(S,b);break}else t(S,b);b=b.sibling}N.type===B?(_=Pa(N.props.children,S.mode,_,N.key),_.return=S,S=_):(_=Ui(N.type,N.key,N.props,null,S.mode,_),jn(_,N),_.return=S,S=_)}return f(S);case O:e:{for(Y=N.key;b!==null;){if(b.key===Y)if(b.tag===4&&b.stateNode.containerInfo===N.containerInfo&&b.stateNode.implementation===N.implementation){a(S,b.sibling),_=n(b,N.children||[]),_.return=S,S=_;break e}else{a(S,b);break}else t(S,b);b=b.sibling}_=js(N,S.mode,_),_.return=S,S=_}return f(S);case K:return Y=N._init,N=Y(N._payload),xe(S,b,N,_)}if(jt(N))return Q(S,b,N,_);if(Oe(N)){if(Y=Oe(N),typeof Y!="function")throw Error(u(150));return N=Y.call(N),F(S,b,N,_)}if(typeof N.then=="function")return xe(S,b,Ji(N),_);if(N.$$typeof===q)return xe(S,b,Hi(S,N),_);Wi(S,N)}return typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint"?(N=""+N,b!==null&&b.tag===6?(a(S,b.sibling),_=n(b,N),_.return=S,S=_):(a(S,b),_=Es(N,S.mode,_),_.return=S,S=_),f(S)):a(S,b)}return function(S,b,N,_){try{En=0;var Y=xe(S,b,N,_);return jl=null,Y}catch(k){if(k===$n||k===Gi)throw k;var ae=dt(29,k,null,S.mode);return ae.lanes=_,ae.return=S,ae}finally{}}}var Al=Jc(!0),Wc=Jc(!1),St=W(null),Ut=null;function pa(e){var t=e.alternate;de(Ge,Ge.current&1),de(St,e),Ut===null&&(t===null||Nl.current!==null||t.memoizedState!==null)&&(Ut=e)}function e0(e){if(e.tag===22){if(de(Ge,Ge.current),de(St,e),Ut===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ut=e)}}else $a()}function $a(){de(Ge,Ge.current),de(St,St.current)}function It(e){he(St),Ut===e&&(Ut=null),he(Ge)}var Ge=W(0);function er(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Xd(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function rd(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:$({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var sd={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=ft(),n=ma(l);n.payload=t,a!=null&&(n.callback=a),t=ha(e,n,l),t!==null&&(mt(t,e,l),yn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=ft(),n=ma(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=ha(e,n,l),t!==null&&(mt(t,e,l),yn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=ft(),l=ma(a);l.tag=2,t!=null&&(l.callback=t),t=ha(e,l,a),t!==null&&(mt(t,e,a),yn(t,e,a))}};function t0(e,t,a,l,n,d,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,d,f):t.prototype&&t.prototype.isPureReactComponent?!un(a,l)||!un(n,d):!0}function a0(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&sd.enqueueReplaceState(t,t.state,null)}function Ka(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=$({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var tr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function l0(e){tr(e)}function n0(e){console.error(e)}function i0(e){tr(e)}function ar(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function r0(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function dd(e,t,a){return a=ma(a),a.tag=3,a.payload={element:null},a.callback=function(){ar(e,t)},a}function s0(e){return e=ma(e),e.tag=3,e}function d0(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var d=l.value;e.payload=function(){return n(d)},e.callback=function(){r0(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){r0(t,a,l),typeof n!="function"&&(Na===null?Na=new Set([this]):Na.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function im(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&hn(t,a,n,!0),a=St.current,a!==null){switch(a.tag){case 13:return Ut===null?_d():a.alternate===null&&_e===0&&(_e=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===Ls?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Dd(e,l,n)),!1;case 22:return a.flags|=65536,l===Ls?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Dd(e,l,n)),!1}throw Error(u(435,a.tag))}return Dd(e,l,n),_d(),!1}if(me)return t=St.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Os&&(e=Error(u(422),{cause:l}),mn(vt(e,a)))):(l!==Os&&(t=Error(u(423),{cause:l}),mn(vt(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=vt(l,a),n=dd(e.stateNode,l,n),qs(e,n),_e!==4&&(_e=2)),!1;var d=Error(u(520),{cause:l});if(d=vt(d,a),Dn===null?Dn=[d]:Dn.push(d),_e!==4&&(_e=2),t===null)return!0;l=vt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=dd(a.stateNode,l,e),qs(a,e),!1;case 1:if(t=a.type,d=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(Na===null||!Na.has(d))))return a.flags|=65536,n&=-n,a.lanes|=n,n=s0(n),d0(n,e,a,l),qs(a,n),!1}a=a.return}while(a!==null);return!1}var u0=Error(u(461)),Ye=!1;function Fe(e,t,a,l){t.child=e===null?Wc(t,null,a,l):Al(t,e.child,a,l)}function o0(e,t,a,l,n){a=a.render;var d=t.ref;if("ref"in l){var f={};for(var h in l)h!=="ref"&&(f[h]=l[h])}else f=l;return Ia(t),l=ks(e,t,a,f,d,n),h=Fs(),e!==null&&!Ye?(Vs(e,t,n),Xt(e,t,n)):(me&&h&&As(t),t.flags|=1,Fe(e,t,l,n),t.child)}function c0(e,t,a,l,n){if(e===null){var d=a.type;return typeof d=="function"&&!ws(d)&&d.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=d,f0(e,t,d,l,n)):(e=Ui(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,!pd(e,n)){var f=d.memoizedProps;if(a=a.compare,a=a!==null?a:un,a(f,l)&&e.ref===t.ref)return Xt(e,t,n)}return t.flags|=1,e=Pt(d,l),e.ref=t.ref,e.return=t,t.child=e}function f0(e,t,a,l,n){if(e!==null){var d=e.memoizedProps;if(un(d,l)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=l=d,pd(e,n))(e.flags&131072)!==0&&(Ye=!0);else return t.lanes=e.lanes,Xt(e,t,n)}return ud(e,t,a,l,n)}function m0(e,t,a){var l=t.pendingProps,n=l.children,d=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=d!==null?d.baseLanes|a:a,e!==null){for(n=t.child=e.child,d=0;n!==null;)d=d|n.lanes|n.childLanes,n=n.sibling;t.childLanes=d&~l}else t.childLanes=0,t.child=null;return h0(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&qi(t,d!==null?d.cachePool:null),d!==null?fc(t,d):Ps(),e0(t);else return t.lanes=t.childLanes=536870912,h0(e,t,d!==null?d.baseLanes|a:a,a)}else d!==null?(qi(t,d.cachePool),fc(t,d),$a(),t.memoizedState=null):(e!==null&&qi(t,null),Ps(),$a());return Fe(e,t,n,a),t.child}function h0(e,t,a,l){var n=zs();return n=n===null?null:{parent:qe._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&qi(t,null),Ps(),e0(t),e!==null&&hn(e,t,l,!0),null}function lr(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function ud(e,t,a,l,n){return Ia(t),a=ks(e,t,a,l,void 0,n),l=Fs(),e!==null&&!Ye?(Vs(e,t,n),Xt(e,t,n)):(me&&l&&As(t),t.flags|=1,Fe(e,t,a,n),t.child)}function g0(e,t,a,l,n,d){return Ia(t),t.updateQueue=null,a=hc(t,l,a,n),mc(e),l=Fs(),e!==null&&!Ye?(Vs(e,t,d),Xt(e,t,d)):(me&&l&&As(t),t.flags|=1,Fe(e,t,a,d),t.child)}function p0(e,t,a,l,n){if(Ia(t),t.stateNode===null){var d=vl,f=a.contextType;typeof f=="object"&&f!==null&&(d=Qe(f)),d=new a(l,d),t.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=sd,t.stateNode=d,d._reactInternals=t,d=t.stateNode,d.props=l,d.state=t.memoizedState,d.refs={},Bs(t),f=a.contextType,d.context=typeof f=="object"&&f!==null?Qe(f):vl,d.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(rd(t,a,f,l),d.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(f=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),f!==d.state&&sd.enqueueReplaceState(d,d.state,null),bn(t,l,d,n),xn(),d.state=t.memoizedState),typeof d.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){d=t.stateNode;var h=t.memoizedProps,p=Ka(a,h);d.props=p;var C=d.context,T=a.contextType;f=vl,typeof T=="object"&&T!==null&&(f=Qe(T));var M=a.getDerivedStateFromProps;T=typeof M=="function"||typeof d.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,T||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(h||C!==f)&&a0(t,d,l,f),fa=!1;var w=t.memoizedState;d.state=w,bn(t,l,d,n),xn(),C=t.memoizedState,h||w!==C||fa?(typeof M=="function"&&(rd(t,a,M,l),C=t.memoizedState),(p=fa||t0(t,a,p,l,w,C,f))?(T||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(t.flags|=4194308)):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=C),d.props=l,d.state=C,d.context=f,l=p):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{d=t.stateNode,Hs(e,t),f=t.memoizedProps,T=Ka(a,f),d.props=T,M=t.pendingProps,w=d.context,C=a.contextType,p=vl,typeof C=="object"&&C!==null&&(p=Qe(C)),h=a.getDerivedStateFromProps,(C=typeof h=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(f!==M||w!==p)&&a0(t,d,l,p),fa=!1,w=t.memoizedState,d.state=w,bn(t,l,d,n),xn();var E=t.memoizedState;f!==M||w!==E||fa||e!==null&&e.dependencies!==null&&Bi(e.dependencies)?(typeof h=="function"&&(rd(t,a,h,l),E=t.memoizedState),(T=fa||t0(t,a,T,l,w,E,p)||e!==null&&e.dependencies!==null&&Bi(e.dependencies))?(C||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(l,E,p),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(l,E,p)),typeof d.componentDidUpdate=="function"&&(t.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof d.componentDidUpdate!="function"||f===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=E),d.props=l,d.state=E,d.context=p,l=T):(typeof d.componentDidUpdate!="function"||f===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),l=!1)}return d=l,lr(e,t),l=(t.flags&128)!==0,d||l?(d=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:d.render(),t.flags|=1,e!==null&&l?(t.child=Al(t,e.child,null,n),t.child=Al(t,null,a,n)):Fe(e,t,a,n),t.memoizedState=d.state,e=t.child):e=Xt(e,t,n),e}function $0(e,t,a,l){return fn(),t.flags|=256,Fe(e,t,a,l),t.child}var od={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function cd(e){return{baseLanes:e,cachePool:nc()}}function fd(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Nt),e}function v0(e,t,a){var l=t.pendingProps,n=!1,d=(t.flags&128)!==0,f;if((f=d)||(f=e!==null&&e.memoizedState===null?!1:(Ge.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(me){if(n?pa(t):$a(),me){var h=Re,p;if(p=h){e:{for(p=h,h=Dt;p.nodeType!==8;){if(!h){h=null;break e}if(p=Ot(p.nextSibling),p===null){h=null;break e}}h=p}h!==null?(t.memoizedState={dehydrated:h,treeContext:Ya!==null?{id:Yt,overflow:Zt}:null,retryLane:536870912,hydrationErrors:null},p=dt(18,null,null,0),p.stateNode=h,p.return=t,t.child=p,Je=t,Re=null,p=!0):p=!1}p||Fa(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Xd(h)?t.lanes=32:t.lanes=536870912,null;It(t)}return h=l.children,l=l.fallback,n?($a(),n=t.mode,h=nr({mode:"hidden",children:h},n),l=Pa(l,n,a,null),h.return=t,l.return=t,h.sibling=l,t.child=h,n=t.child,n.memoizedState=cd(a),n.childLanes=fd(e,f,a),t.memoizedState=od,l):(pa(t),md(t,h))}if(p=e.memoizedState,p!==null&&(h=p.dehydrated,h!==null)){if(d)t.flags&256?(pa(t),t.flags&=-257,t=hd(e,t,a)):t.memoizedState!==null?($a(),t.child=e.child,t.flags|=128,t=null):($a(),n=l.fallback,h=t.mode,l=nr({mode:"visible",children:l.children},h),n=Pa(n,h,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,Al(t,e.child,null,a),l=t.child,l.memoizedState=cd(a),l.childLanes=fd(e,f,a),t.memoizedState=od,t=n);else if(pa(t),Xd(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var C=f.dgst;f=C,l=Error(u(419)),l.stack="",l.digest=f,mn({value:l,source:null,stack:null}),t=hd(e,t,a)}else if(Ye||hn(e,t,a,!1),f=(a&e.childLanes)!==0,Ye||f){if(f=Ne,f!==null&&(l=a&-a,l=(l&42)!==0?1:Qr(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==p.retryLane))throw p.retryLane=l,$l(e,l),mt(f,e,l),u0;h.data==="$?"||_d(),t=hd(e,t,a)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=p.treeContext,Re=Ot(h.nextSibling),Je=t,me=!0,ka=null,Dt=!1,e!==null&&(xt[bt++]=Yt,xt[bt++]=Zt,xt[bt++]=Ya,Yt=e.id,Zt=e.overflow,Ya=t),t=md(t,l.children),t.flags|=4096);return t}return n?($a(),n=l.fallback,h=t.mode,p=e.child,C=p.sibling,l=Pt(p,{mode:"hidden",children:l.children}),l.subtreeFlags=p.subtreeFlags&65011712,C!==null?n=Pt(C,n):(n=Pa(n,h,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,h=e.child.memoizedState,h===null?h=cd(a):(p=h.cachePool,p!==null?(C=qe._currentValue,p=p.parent!==C?{parent:C,pool:C}:p):p=nc(),h={baseLanes:h.baseLanes|a,cachePool:p}),n.memoizedState=h,n.childLanes=fd(e,f,a),t.memoizedState=od,l):(pa(t),a=e.child,e=a.sibling,a=Pt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function md(e,t){return t=nr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function nr(e,t){return e=dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function hd(e,t,a){return Al(t,e.child,null,a),e=md(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function y0(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),_s(e.return,t,a)}function gd(e,t,a,l,n){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=l,d.tail=a,d.tailMode=n)}function x0(e,t,a){var l=t.pendingProps,n=l.revealOrder,d=l.tail;if(Fe(e,t,l.children,a),l=Ge.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&y0(e,a,t);else if(e.tag===19)y0(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(de(Ge,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&er(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),gd(t,!1,n,a,d);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&er(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}gd(t,!0,a,null,d);break;case"together":gd(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xt(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Sa|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(hn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=Pt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Pt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function pd(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Bi(e)))}function rm(e,t,a){switch(t.tag){case 3:gi(t,t.stateNode.containerInfo),ca(t,qe,e.memoizedState.cache),fn();break;case 27:case 5:kr(t);break;case 4:gi(t,t.stateNode.containerInfo);break;case 10:ca(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(pa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?v0(e,t,a):(pa(t),e=Xt(e,t,a),e!==null?e.sibling:null);pa(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(hn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return x0(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),de(Ge,Ge.current),l)break;return null;case 22:case 23:return t.lanes=0,m0(e,t,a);case 24:ca(t,qe,e.memoizedState.cache)}return Xt(e,t,a)}function b0(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ye=!0;else{if(!pd(e,a)&&(t.flags&128)===0)return Ye=!1,rm(e,t,a);Ye=(e.flags&131072)!==0}else Ye=!1,me&&(t.flags&1048576)!==0&&Ko(t,Li,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")ws(l)?(e=Ka(l,e),t.tag=1,t=p0(null,t,l,e,a)):(t.tag=0,t=ud(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===fe){t.tag=11,t=o0(null,t,l,e,a);break e}else if(n===re){t.tag=14,t=c0(null,t,l,e,a);break e}}throw t=We(l)||l,Error(u(306,t,""))}}return t;case 0:return ud(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=Ka(l,t.pendingProps),p0(e,t,l,n,a);case 3:e:{if(gi(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var d=t.memoizedState;n=d.element,Hs(e,t),bn(t,l,null,a);var f=t.memoizedState;if(l=f.cache,ca(t,qe,l),l!==d.cache&&Ms(t,[qe],a,!0),xn(),l=f.element,d.isDehydrated)if(d={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){t=$0(e,t,l,a);break e}else if(l!==n){n=vt(Error(u(424)),t),mn(n),t=$0(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Re=Ot(e.firstChild),Je=t,me=!0,ka=null,Dt=!0,a=Wc(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(fn(),l===n){t=Xt(e,t,a);break e}Fe(e,t,l,a)}t=t.child}return t;case 26:return lr(e,t),e===null?(a=w1(t.type,null,t.pendingProps,null))?t.memoizedState=a:me||(a=t.type,e=t.pendingProps,l=vr(sa.current).createElement(a),l[Xe]=t,l[et]=e,Ie(l,a,e),Pe(l),t.stateNode=l):t.memoizedState=w1(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return kr(t),e===null&&me&&(l=t.stateNode=S1(t.type,t.pendingProps,sa.current),Je=t,Dt=!0,n=Re,Ea(t.type)?(Qd=n,Re=Ot(l.firstChild)):Re=n),Fe(e,t,t.pendingProps.children,a),lr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&me&&((n=l=Re)&&(l=Dm(l,t.type,t.pendingProps,Dt),l!==null?(t.stateNode=l,Je=t,Re=Ot(l.firstChild),Dt=!1,n=!0):n=!1),n||Fa(t)),kr(t),n=t.type,d=t.pendingProps,f=e!==null?e.memoizedProps:null,l=d.children,Fd(n,d)?l=null:f!==null&&Fd(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=ks(e,t,J3,null,null,a),Yn._currentValue=n),lr(e,t),Fe(e,t,l,a),t.child;case 6:return e===null&&me&&((e=a=Re)&&(a=Um(a,t.pendingProps,Dt),a!==null?(t.stateNode=a,Je=t,Re=null,e=!0):e=!1),e||Fa(t)),null;case 13:return v0(e,t,a);case 4:return gi(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Al(t,null,l,a):Fe(e,t,l,a),t.child;case 11:return o0(e,t,t.type,t.pendingProps,a);case 7:return Fe(e,t,t.pendingProps,a),t.child;case 8:return Fe(e,t,t.pendingProps.children,a),t.child;case 12:return Fe(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ca(t,t.type,l.value),Fe(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Ia(t),n=Qe(n),l=l(n),t.flags|=1,Fe(e,t,l,a),t.child;case 14:return c0(e,t,t.type,t.pendingProps,a);case 15:return f0(e,t,t.type,t.pendingProps,a);case 19:return x0(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=nr(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Pt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return m0(e,t,a);case 24:return Ia(t),l=Qe(qe),e===null?(n=zs(),n===null&&(n=Ne,d=Ds(),n.pooledCache=d,d.refCount++,d!==null&&(n.pooledCacheLanes|=a),n=d),t.memoizedState={parent:l,cache:n},Bs(t),ca(t,qe,n)):((e.lanes&a)!==0&&(Hs(e,t),bn(t,null,null,a),xn()),n=e.memoizedState,d=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ca(t,qe,l)):(l=d.cache,ca(t,qe,l),l!==n.cache&&Ms(t,[qe],a,!0))),Fe(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function Qt(e){e.flags|=4}function S0(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!O1(t)){if(t=St.current,t!==null&&((ue&4194048)===ue?Ut!==null:(ue&62914560)!==ue&&(ue&536870912)===0||t!==Ut))throw vn=Ls,ic;e.flags|=8192}}function ir(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Wu():536870912,e.lanes|=t,_l|=t)}function An(e,t){if(!me)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function je(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function sm(e,t,a){var l=t.pendingProps;switch(Ts(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(t),null;case 1:return je(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Ft(qe),al(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(cn(t)?Qt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ec())),je(t),null;case 26:return a=t.memoizedState,e===null?(Qt(t),a!==null?(je(t),S0(t,a)):(je(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Qt(t),je(t),S0(t,a)):(je(t),t.flags&=-16777217):(e.memoizedProps!==l&&Qt(t),je(t),t.flags&=-16777217),null;case 27:pi(t),a=sa.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Qt(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return je(t),null}e=Me.current,cn(t)?Jo(t):(e=S1(n,l,a),t.stateNode=e,Qt(t))}return je(t),null;case 5:if(pi(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Qt(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return je(t),null}if(e=Me.current,cn(t))Jo(t);else{switch(n=vr(sa.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[Xe]=t,e[et]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Ie(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Qt(t)}}return je(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Qt(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=sa.current,cn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=Je,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[Xe]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||g1(e.nodeValue,a)),e||Fa(t)}else e=vr(e).createTextNode(l),e[Xe]=t,t.stateNode=e}return je(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=cn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(u(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(u(317));n[Xe]=t}else fn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;je(t),n=!1}else n=ec(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(It(t),t):(It(t),null)}if(It(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var d=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(d=l.memoizedState.cachePool.pool),d!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),ir(t,t.updateQueue),je(t),null;case 4:return al(),e===null&&Gd(t.stateNode.containerInfo),je(t),null;case 10:return Ft(t.type),je(t),null;case 19:if(he(Ge),n=t.memoizedState,n===null)return je(t),null;if(l=(t.flags&128)!==0,d=n.rendering,d===null)if(l)An(n,!1);else{if(_e!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(d=er(e),d!==null){for(t.flags|=128,An(n,!1),e=d.updateQueue,t.updateQueue=e,ir(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Qo(a,e),a=a.sibling;return de(Ge,Ge.current&1|2),t.child}e=e.sibling}n.tail!==null&&Mt()>dr&&(t.flags|=128,l=!0,An(n,!1),t.lanes=4194304)}else{if(!l)if(e=er(d),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,ir(t,e),An(n,!0),n.tail===null&&n.tailMode==="hidden"&&!d.alternate&&!me)return je(t),null}else 2*Mt()-n.renderingStartTime>dr&&a!==536870912&&(t.flags|=128,l=!0,An(n,!1),t.lanes=4194304);n.isBackwards?(d.sibling=t.child,t.child=d):(e=n.last,e!==null?e.sibling=d:t.child=d,n.last=d)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Mt(),t.sibling=null,e=Ge.current,de(Ge,l?e&1|2:e&1),t):(je(t),null);case 22:case 23:return It(t),Ys(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(je(t),t.subtreeFlags&6&&(t.flags|=8192)):je(t),a=t.updateQueue,a!==null&&ir(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&he(Xa),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Ft(qe),je(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function dm(e,t){switch(Ts(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ft(qe),al(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return pi(t),null;case 13:if(It(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));fn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return he(Ge),null;case 4:return al(),null;case 10:return Ft(t.type),null;case 22:case 23:return It(t),Ys(),e!==null&&he(Xa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ft(qe),null;case 25:return null;default:return null}}function N0(e,t){switch(Ts(t),t.tag){case 3:Ft(qe),al();break;case 26:case 27:case 5:pi(t);break;case 4:al();break;case 13:It(t);break;case 19:he(Ge);break;case 10:Ft(t.type);break;case 22:case 23:It(t),Ys(),e!==null&&he(Xa);break;case 24:Ft(qe)}}function Tn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var d=a.create,f=a.inst;l=d(),f.destroy=l}a=a.next}while(a!==n)}}catch(h){be(t,t.return,h)}}function va(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var d=n.next;l=d;do{if((l.tag&e)===e){var f=l.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,n=t;var p=a,C=h;try{C()}catch(T){be(n,p,T)}}}l=l.next}while(l!==d)}}catch(T){be(t,t.return,T)}}function C0(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{cc(t,a)}catch(l){be(e,e.return,l)}}}function w0(e,t,a){a.props=Ka(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){be(e,t,l)}}function On(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){be(e,t,n)}}function zt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){be(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){be(e,t,n)}else a.current=null}function E0(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){be(e,e.return,n)}}function $d(e,t,a){try{var l=e.stateNode;Tm(l,e.type,a,t),l[et]=t}catch(n){be(e,e.return,n)}}function j0(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ea(e.type)||e.tag===4}function vd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||j0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ea(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function yd(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=$r));else if(l!==4&&(l===27&&Ea(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(yd(e,t,a),e=e.sibling;e!==null;)yd(e,t,a),e=e.sibling}function rr(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Ea(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(rr(e,t,a),e=e.sibling;e!==null;)rr(e,t,a),e=e.sibling}function A0(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Ie(t,l,a),t[Xe]=e,t[et]=a}catch(d){be(e,e.return,d)}}var Kt=!1,Ue=!1,xd=!1,T0=typeof WeakSet=="function"?WeakSet:Set,Ze=null;function um(e,t){if(e=e.containerInfo,Zd=Cr,e=qo(e),vs(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,d=l.focusNode;l=l.focusOffset;try{a.nodeType,d.nodeType}catch{a=null;break e}var f=0,h=-1,p=-1,C=0,T=0,M=e,w=null;t:for(;;){for(var E;M!==a||n!==0&&M.nodeType!==3||(h=f+n),M!==d||l!==0&&M.nodeType!==3||(p=f+l),M.nodeType===3&&(f+=M.nodeValue.length),(E=M.firstChild)!==null;)w=M,M=E;for(;;){if(M===e)break t;if(w===a&&++C===n&&(h=f),w===d&&++T===l&&(p=f),(E=M.nextSibling)!==null)break;M=w,w=M.parentNode}M=E}a=h===-1||p===-1?null:{start:h,end:p}}else a=null}a=a||{start:0,end:0}}else a=null;for(kd={focusedElem:e,selectionRange:a},Cr=!1,Ze=t;Ze!==null;)if(t=Ze,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ze=e;else for(;Ze!==null;){switch(t=Ze,d=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&d!==null){e=void 0,a=t,n=d.memoizedProps,d=d.memoizedState,l=a.stateNode;try{var Q=Ka(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(Q,d),l.__reactInternalSnapshotBeforeUpdate=e}catch(F){be(a,a.return,F)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Id(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Id(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,Ze=e;break}Ze=t.return}}function O0(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:ya(e,a),l&4&&Tn(5,a);break;case 1:if(ya(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){be(a,a.return,f)}else{var n=Ka(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){be(a,a.return,f)}}l&64&&C0(a),l&512&&On(a,a.return);break;case 3:if(ya(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{cc(e,t)}catch(f){be(a,a.return,f)}}break;case 27:t===null&&l&4&&A0(a);case 26:case 5:ya(e,a),t===null&&l&4&&E0(a),l&512&&On(a,a.return);break;case 12:ya(e,a);break;case 13:ya(e,a),l&4&&M0(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=vm.bind(null,a),zm(e,a))));break;case 22:if(l=a.memoizedState!==null||Kt,!l){t=t!==null&&t.memoizedState!==null||Ue,n=Kt;var d=Ue;Kt=l,(Ue=t)&&!d?xa(e,a,(a.subtreeFlags&8772)!==0):ya(e,a),Kt=n,Ue=d}break;case 30:break;default:ya(e,a)}}function R0(e){var t=e.alternate;t!==null&&(e.alternate=null,R0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Wr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var we=null,lt=!1;function Jt(e,t,a){for(a=a.child;a!==null;)_0(e,t,a),a=a.sibling}function _0(e,t,a){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(Ql,a)}catch{}switch(a.tag){case 26:Ue||zt(a,t),Jt(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ue||zt(a,t);var l=we,n=lt;Ea(a.type)&&(we=a.stateNode,lt=!1),Jt(e,t,a),Hn(a.stateNode),we=l,lt=n;break;case 5:Ue||zt(a,t);case 6:if(l=we,n=lt,we=null,Jt(e,t,a),we=l,lt=n,we!==null)if(lt)try{(we.nodeType===9?we.body:we.nodeName==="HTML"?we.ownerDocument.body:we).removeChild(a.stateNode)}catch(d){be(a,t,d)}else try{we.removeChild(a.stateNode)}catch(d){be(a,t,d)}break;case 18:we!==null&&(lt?(e=we,x1(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Vn(e)):x1(we,a.stateNode));break;case 4:l=we,n=lt,we=a.stateNode.containerInfo,lt=!0,Jt(e,t,a),we=l,lt=n;break;case 0:case 11:case 14:case 15:Ue||va(2,a,t),Ue||va(4,a,t),Jt(e,t,a);break;case 1:Ue||(zt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&w0(a,t,l)),Jt(e,t,a);break;case 21:Jt(e,t,a);break;case 22:Ue=(l=Ue)||a.memoizedState!==null,Jt(e,t,a),Ue=l;break;default:Jt(e,t,a)}}function M0(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Vn(e)}catch(a){be(t,t.return,a)}}function om(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new T0),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new T0),t;default:throw Error(u(435,e.tag))}}function bd(e,t){var a=om(e);t.forEach(function(l){var n=ym.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function ut(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],d=e,f=t,h=f;e:for(;h!==null;){switch(h.tag){case 27:if(Ea(h.type)){we=h.stateNode,lt=!1;break e}break;case 5:we=h.stateNode,lt=!1;break e;case 3:case 4:we=h.stateNode.containerInfo,lt=!0;break e}h=h.return}if(we===null)throw Error(u(160));_0(d,f,n),we=null,lt=!1,d=n.alternate,d!==null&&(d.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)D0(t,e),t=t.sibling}var Tt=null;function D0(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:ut(t,e),ot(e),l&4&&(va(3,e,e.return),Tn(3,e),va(5,e,e.return));break;case 1:ut(t,e),ot(e),l&512&&(Ue||a===null||zt(a,a.return)),l&64&&Kt&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Tt;if(ut(t,e),ot(e),l&512&&(Ue||a===null||zt(a,a.return)),l&4){var d=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":d=n.getElementsByTagName("title")[0],(!d||d[Wl]||d[Xe]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=n.createElement(l),n.head.insertBefore(d,n.querySelector("head > title"))),Ie(d,l,a),d[Xe]=e,Pe(d),l=d;break e;case"link":var f=A1("link","href",n).get(l+(a.href||""));if(f){for(var h=0;h<f.length;h++)if(d=f[h],d.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&d.getAttribute("rel")===(a.rel==null?null:a.rel)&&d.getAttribute("title")===(a.title==null?null:a.title)&&d.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(h,1);break t}}d=n.createElement(l),Ie(d,l,a),n.head.appendChild(d);break;case"meta":if(f=A1("meta","content",n).get(l+(a.content||""))){for(h=0;h<f.length;h++)if(d=f[h],d.getAttribute("content")===(a.content==null?null:""+a.content)&&d.getAttribute("name")===(a.name==null?null:a.name)&&d.getAttribute("property")===(a.property==null?null:a.property)&&d.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&d.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(h,1);break t}}d=n.createElement(l),Ie(d,l,a),n.head.appendChild(d);break;default:throw Error(u(468,l))}d[Xe]=e,Pe(d),l=d}e.stateNode=l}else T1(n,e.type,e.stateNode);else e.stateNode=j1(n,l,e.memoizedProps);else d!==l?(d===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):d.count--,l===null?T1(n,e.type,e.stateNode):j1(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&$d(e,e.memoizedProps,a.memoizedProps)}break;case 27:ut(t,e),ot(e),l&512&&(Ue||a===null||zt(a,a.return)),a!==null&&l&4&&$d(e,e.memoizedProps,a.memoizedProps);break;case 5:if(ut(t,e),ot(e),l&512&&(Ue||a===null||zt(a,a.return)),e.flags&32){n=e.stateNode;try{ol(n,"")}catch(E){be(e,e.return,E)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,$d(e,n,a!==null?a.memoizedProps:n)),l&1024&&(xd=!0);break;case 6:if(ut(t,e),ot(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(E){be(e,e.return,E)}}break;case 3:if(br=null,n=Tt,Tt=yr(t.containerInfo),ut(t,e),Tt=n,ot(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Vn(t.containerInfo)}catch(E){be(e,e.return,E)}xd&&(xd=!1,U0(e));break;case 4:l=Tt,Tt=yr(e.stateNode.containerInfo),ut(t,e),ot(e),Tt=l;break;case 12:ut(t,e),ot(e);break;case 13:ut(t,e),ot(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(jd=Mt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,bd(e,l)));break;case 22:n=e.memoizedState!==null;var p=a!==null&&a.memoizedState!==null,C=Kt,T=Ue;if(Kt=C||n,Ue=T||p,ut(t,e),Ue=T,Kt=C,ot(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||p||Kt||Ue||Ja(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){p=a=t;try{if(d=p.stateNode,n)f=d.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=p.stateNode;var M=p.memoizedProps.style,w=M!=null&&M.hasOwnProperty("display")?M.display:null;h.style.display=w==null||typeof w=="boolean"?"":(""+w).trim()}}catch(E){be(p,p.return,E)}}}else if(t.tag===6){if(a===null){p=t;try{p.stateNode.nodeValue=n?"":p.memoizedProps}catch(E){be(p,p.return,E)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,bd(e,a))));break;case 19:ut(t,e),ot(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,bd(e,l)));break;case 30:break;case 21:break;default:ut(t,e),ot(e)}}function ot(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(j0(l)){a=l;break}l=l.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var n=a.stateNode,d=vd(e);rr(e,d,n);break;case 5:var f=a.stateNode;a.flags&32&&(ol(f,""),a.flags&=-33);var h=vd(e);rr(e,h,f);break;case 3:case 4:var p=a.stateNode.containerInfo,C=vd(e);yd(e,C,p);break;default:throw Error(u(161))}}catch(T){be(e,e.return,T)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function U0(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;U0(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ya(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)O0(e,t.alternate,t),t=t.sibling}function Ja(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:va(4,t,t.return),Ja(t);break;case 1:zt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&w0(t,t.return,a),Ja(t);break;case 27:Hn(t.stateNode);case 26:case 5:zt(t,t.return),Ja(t);break;case 22:t.memoizedState===null&&Ja(t);break;case 30:Ja(t);break;default:Ja(t)}e=e.sibling}}function xa(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,d=t,f=d.flags;switch(d.tag){case 0:case 11:case 15:xa(n,d,a),Tn(4,d);break;case 1:if(xa(n,d,a),l=d,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(C){be(l,l.return,C)}if(l=d,n=l.updateQueue,n!==null){var h=l.stateNode;try{var p=n.shared.hiddenCallbacks;if(p!==null)for(n.shared.hiddenCallbacks=null,n=0;n<p.length;n++)oc(p[n],h)}catch(C){be(l,l.return,C)}}a&&f&64&&C0(d),On(d,d.return);break;case 27:A0(d);case 26:case 5:xa(n,d,a),a&&l===null&&f&4&&E0(d),On(d,d.return);break;case 12:xa(n,d,a);break;case 13:xa(n,d,a),a&&f&4&&M0(n,d);break;case 22:d.memoizedState===null&&xa(n,d,a),On(d,d.return);break;case 30:break;default:xa(n,d,a)}t=t.sibling}}function Sd(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&gn(a))}function Nd(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&gn(e))}function Lt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)z0(e,t,a,l),t=t.sibling}function z0(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Lt(e,t,a,l),n&2048&&Tn(9,t);break;case 1:Lt(e,t,a,l);break;case 3:Lt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&gn(e)));break;case 12:if(n&2048){Lt(e,t,a,l),e=t.stateNode;try{var d=t.memoizedProps,f=d.id,h=d.onPostCommit;typeof h=="function"&&h(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(p){be(t,t.return,p)}}else Lt(e,t,a,l);break;case 13:Lt(e,t,a,l);break;case 23:break;case 22:d=t.stateNode,f=t.alternate,t.memoizedState!==null?d._visibility&2?Lt(e,t,a,l):Rn(e,t):d._visibility&2?Lt(e,t,a,l):(d._visibility|=2,Tl(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Sd(f,t);break;case 24:Lt(e,t,a,l),n&2048&&Nd(t.alternate,t);break;default:Lt(e,t,a,l)}}function Tl(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var d=e,f=t,h=a,p=l,C=f.flags;switch(f.tag){case 0:case 11:case 15:Tl(d,f,h,p,n),Tn(8,f);break;case 23:break;case 22:var T=f.stateNode;f.memoizedState!==null?T._visibility&2?Tl(d,f,h,p,n):Rn(d,f):(T._visibility|=2,Tl(d,f,h,p,n)),n&&C&2048&&Sd(f.alternate,f);break;case 24:Tl(d,f,h,p,n),n&&C&2048&&Nd(f.alternate,f);break;default:Tl(d,f,h,p,n)}t=t.sibling}}function Rn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Rn(a,l),n&2048&&Sd(l.alternate,l);break;case 24:Rn(a,l),n&2048&&Nd(l.alternate,l);break;default:Rn(a,l)}t=t.sibling}}var _n=8192;function Ol(e){if(e.subtreeFlags&_n)for(e=e.child;e!==null;)L0(e),e=e.sibling}function L0(e){switch(e.tag){case 26:Ol(e),e.flags&_n&&e.memoizedState!==null&&Xm(Tt,e.memoizedState,e.memoizedProps);break;case 5:Ol(e);break;case 3:case 4:var t=Tt;Tt=yr(e.stateNode.containerInfo),Ol(e),Tt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=_n,_n=16777216,Ol(e),_n=t):Ol(e));break;default:Ol(e)}}function B0(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Mn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,q0(l,e)}B0(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)H0(e),e=e.sibling}function H0(e){switch(e.tag){case 0:case 11:case 15:Mn(e),e.flags&2048&&va(9,e,e.return);break;case 3:Mn(e);break;case 12:Mn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,sr(e)):Mn(e);break;default:Mn(e)}}function sr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,q0(l,e)}B0(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:va(8,t,t.return),sr(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,sr(t));break;default:sr(t)}e=e.sibling}}function q0(e,t){for(;Ze!==null;){var a=Ze;switch(a.tag){case 0:case 11:case 15:va(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:gn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ze=l;else e:for(a=e;Ze!==null;){l=Ze;var n=l.sibling,d=l.return;if(R0(l),l===a){Ze=null;break e}if(n!==null){n.return=d,Ze=n;break e}Ze=d}}}var cm={getCacheForType:function(e){var t=Qe(qe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},fm=typeof WeakMap=="function"?WeakMap:Map,ge=0,Ne=null,le=null,ue=0,pe=0,ct=null,ba=!1,Rl=!1,Cd=!1,Wt=0,_e=0,Sa=0,Wa=0,wd=0,Nt=0,_l=0,Dn=null,nt=null,Ed=!1,jd=0,dr=1/0,ur=null,Na=null,Ve=0,Ca=null,Ml=null,Dl=0,Ad=0,Td=null,G0=null,Un=0,Od=null;function ft(){if((ge&2)!==0&&ue!==0)return ue&-ue;if(R.T!==null){var e=bl;return e!==0?e:Ld()}return ao()}function P0(){Nt===0&&(Nt=(ue&536870912)===0||me?Ju():536870912);var e=St.current;return e!==null&&(e.flags|=32),Nt}function mt(e,t,a){(e===Ne&&(pe===2||pe===9)||e.cancelPendingCommit!==null)&&(Ul(e,0),wa(e,ue,Nt,!1)),Jl(e,a),((ge&2)===0||e!==Ne)&&(e===Ne&&((ge&2)===0&&(Wa|=a),_e===4&&wa(e,ue,Nt,!1)),Bt(e))}function Y0(e,t,a){if((ge&6)!==0)throw Error(u(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Kl(e,t),n=l?gm(e,t):Md(e,t,!0),d=l;do{if(n===0){Rl&&!l&&wa(e,t,0,!1);break}else{if(a=e.current.alternate,d&&!mm(a)){n=Md(e,t,!1),d=!1;continue}if(n===2){if(d=t,e.errorRecoveryDisabledLanes&d)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var h=e;n=Dn;var p=h.current.memoizedState.isDehydrated;if(p&&(Ul(h,f).flags|=256),f=Md(h,f,!1),f!==2){if(Cd&&!p){h.errorRecoveryDisabledLanes|=d,Wa|=d,n=4;break e}d=nt,nt=n,d!==null&&(nt===null?nt=d:nt.push.apply(nt,d))}n=f}if(d=!1,n!==2)continue}}if(n===1){Ul(e,0),wa(e,t,0,!0);break}e:{switch(l=e,d=n,d){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:wa(l,t,Nt,!ba);break e;case 2:nt=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(n=jd+300-Mt(),10<n)){if(wa(l,t,Nt,!ba),xi(l,0,!0)!==0)break e;l.timeoutHandle=v1(Z0.bind(null,l,a,nt,ur,Ed,t,Nt,Wa,_l,ba,d,2,-0,0),n);break e}Z0(l,a,nt,ur,Ed,t,Nt,Wa,_l,ba,d,0,-0,0)}}break}while(!0);Bt(e)}function Z0(e,t,a,l,n,d,f,h,p,C,T,M,w,E){if(e.timeoutHandle=-1,M=t.subtreeFlags,(M&8192||(M&16785408)===16785408)&&(Pn={stylesheets:null,count:0,unsuspend:Im},L0(t),M=Qm(),M!==null)){e.cancelPendingCommit=M(K0.bind(null,e,t,d,a,l,n,f,h,p,T,1,w,E)),wa(e,d,f,!C);return}K0(e,t,d,a,l,n,f,h,p)}function mm(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],d=n.getSnapshot;n=n.value;try{if(!st(d(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function wa(e,t,a,l){t&=~wd,t&=~Wa,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var d=31-rt(n),f=1<<d;l[d]=-1,n&=~f}a!==0&&eo(e,a,t)}function or(){return(ge&6)===0?(zn(0),!1):!0}function Rd(){if(le!==null){if(pe===0)var e=le.return;else e=le,kt=Va=null,Is(e),jl=null,En=0,e=le;for(;e!==null;)N0(e.alternate,e),e=e.return;le=null}}function Ul(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Rm(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Rd(),Ne=e,le=a=Pt(e.current,null),ue=t,pe=0,ct=null,ba=!1,Rl=Kl(e,t),Cd=!1,_l=Nt=wd=Wa=Sa=_e=0,nt=Dn=null,Ed=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-rt(l),d=1<<n;t|=e[n],l&=~d}return Wt=t,_i(),a}function k0(e,t){te=null,R.H=Ki,t===$n||t===Gi?(t=dc(),pe=3):t===ic?(t=dc(),pe=4):pe=t===u0?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ct=t,le===null&&(_e=1,ar(e,vt(t,e.current)))}function F0(){var e=R.H;return R.H=Ki,e===null?Ki:e}function V0(){var e=R.A;return R.A=cm,e}function _d(){_e=4,ba||(ue&4194048)!==ue&&St.current!==null||(Rl=!0),(Sa&134217727)===0&&(Wa&134217727)===0||Ne===null||wa(Ne,ue,Nt,!1)}function Md(e,t,a){var l=ge;ge|=2;var n=F0(),d=V0();(Ne!==e||ue!==t)&&(ur=null,Ul(e,t)),t=!1;var f=_e;e:do try{if(pe!==0&&le!==null){var h=le,p=ct;switch(pe){case 8:Rd(),f=6;break e;case 3:case 2:case 9:case 6:St.current===null&&(t=!0);var C=pe;if(pe=0,ct=null,zl(e,h,p,C),a&&Rl){f=0;break e}break;default:C=pe,pe=0,ct=null,zl(e,h,p,C)}}hm(),f=_e;break}catch(T){k0(e,T)}while(!0);return t&&e.shellSuspendCounter++,kt=Va=null,ge=l,R.H=n,R.A=d,le===null&&(Ne=null,ue=0,_i()),f}function hm(){for(;le!==null;)I0(le)}function gm(e,t){var a=ge;ge|=2;var l=F0(),n=V0();Ne!==e||ue!==t?(ur=null,dr=Mt()+500,Ul(e,t)):Rl=Kl(e,t);e:do try{if(pe!==0&&le!==null){t=le;var d=ct;t:switch(pe){case 1:pe=0,ct=null,zl(e,t,d,1);break;case 2:case 9:if(rc(d)){pe=0,ct=null,X0(t);break}t=function(){pe!==2&&pe!==9||Ne!==e||(pe=7),Bt(e)},d.then(t,t);break e;case 3:pe=7;break e;case 4:pe=5;break e;case 7:rc(d)?(pe=0,ct=null,X0(t)):(pe=0,ct=null,zl(e,t,d,7));break;case 5:var f=null;switch(le.tag){case 26:f=le.memoizedState;case 5:case 27:var h=le;if(!f||O1(f)){pe=0,ct=null;var p=h.sibling;if(p!==null)le=p;else{var C=h.return;C!==null?(le=C,cr(C)):le=null}break t}}pe=0,ct=null,zl(e,t,d,5);break;case 6:pe=0,ct=null,zl(e,t,d,6);break;case 8:Rd(),_e=6;break e;default:throw Error(u(462))}}pm();break}catch(T){k0(e,T)}while(!0);return kt=Va=null,R.H=l,R.A=n,ge=a,le!==null?0:(Ne=null,ue=0,_i(),_e)}function pm(){for(;le!==null&&!B2();)I0(le)}function I0(e){var t=b0(e.alternate,e,Wt);e.memoizedProps=e.pendingProps,t===null?cr(e):le=t}function X0(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=g0(a,t,t.pendingProps,t.type,void 0,ue);break;case 11:t=g0(a,t,t.pendingProps,t.type.render,t.ref,ue);break;case 5:Is(t);default:N0(a,t),t=le=Qo(t,Wt),t=b0(a,t,Wt)}e.memoizedProps=e.pendingProps,t===null?cr(e):le=t}function zl(e,t,a,l){kt=Va=null,Is(t),jl=null,En=0;var n=t.return;try{if(im(e,n,t,a,ue)){_e=1,ar(e,vt(a,e.current)),le=null;return}}catch(d){if(n!==null)throw le=n,d;_e=1,ar(e,vt(a,e.current)),le=null;return}t.flags&32768?(me||l===1?e=!0:Rl||(ue&536870912)!==0?e=!1:(ba=e=!0,(l===2||l===9||l===3||l===6)&&(l=St.current,l!==null&&l.tag===13&&(l.flags|=16384))),Q0(t,e)):cr(t)}function cr(e){var t=e;do{if((t.flags&32768)!==0){Q0(t,ba);return}e=t.return;var a=sm(t.alternate,t,Wt);if(a!==null){le=a;return}if(t=t.sibling,t!==null){le=t;return}le=t=e}while(t!==null);_e===0&&(_e=5)}function Q0(e,t){do{var a=dm(e.alternate,e);if(a!==null){a.flags&=32767,le=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){le=e;return}le=e=a}while(e!==null);_e=6,le=null}function K0(e,t,a,l,n,d,f,h,p){e.cancelPendingCommit=null;do fr();while(Ve!==0);if((ge&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(d=t.lanes|t.childLanes,d|=Ns,I2(e,a,d,f,h,p),e===Ne&&(le=Ne=null,ue=0),Ml=t,Ca=e,Dl=a,Ad=d,Td=n,G0=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,xm($i,function(){return a1(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=R.T,R.T=null,n=D.p,D.p=2,f=ge,ge|=4;try{um(e,t,a)}finally{ge=f,D.p=n,R.T=l}}Ve=1,J0(),W0(),e1()}}function J0(){if(Ve===1){Ve=0;var e=Ca,t=Ml,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=R.T,R.T=null;var l=D.p;D.p=2;var n=ge;ge|=4;try{D0(t,e);var d=kd,f=qo(e.containerInfo),h=d.focusedElem,p=d.selectionRange;if(f!==h&&h&&h.ownerDocument&&Ho(h.ownerDocument.documentElement,h)){if(p!==null&&vs(h)){var C=p.start,T=p.end;if(T===void 0&&(T=C),"selectionStart"in h)h.selectionStart=C,h.selectionEnd=Math.min(T,h.value.length);else{var M=h.ownerDocument||document,w=M&&M.defaultView||window;if(w.getSelection){var E=w.getSelection(),Q=h.textContent.length,F=Math.min(p.start,Q),xe=p.end===void 0?F:Math.min(p.end,Q);!E.extend&&F>xe&&(f=xe,xe=F,F=f);var S=Bo(h,F),b=Bo(h,xe);if(S&&b&&(E.rangeCount!==1||E.anchorNode!==S.node||E.anchorOffset!==S.offset||E.focusNode!==b.node||E.focusOffset!==b.offset)){var N=M.createRange();N.setStart(S.node,S.offset),E.removeAllRanges(),F>xe?(E.addRange(N),E.extend(b.node,b.offset)):(N.setEnd(b.node,b.offset),E.addRange(N))}}}}for(M=[],E=h;E=E.parentNode;)E.nodeType===1&&M.push({element:E,left:E.scrollLeft,top:E.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<M.length;h++){var _=M[h];_.element.scrollLeft=_.left,_.element.scrollTop=_.top}}Cr=!!Zd,kd=Zd=null}finally{ge=n,D.p=l,R.T=a}}e.current=t,Ve=2}}function W0(){if(Ve===2){Ve=0;var e=Ca,t=Ml,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=R.T,R.T=null;var l=D.p;D.p=2;var n=ge;ge|=4;try{O0(e,t.alternate,t)}finally{ge=n,D.p=l,R.T=a}}Ve=3}}function e1(){if(Ve===4||Ve===3){Ve=0,H2();var e=Ca,t=Ml,a=Dl,l=G0;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ve=5:(Ve=0,Ml=Ca=null,t1(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Na=null),Kr(a),t=t.stateNode,it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(Ql,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=R.T,n=D.p,D.p=2,R.T=null;try{for(var d=e.onRecoverableError,f=0;f<l.length;f++){var h=l[f];d(h.value,{componentStack:h.stack})}}finally{R.T=t,D.p=n}}(Dl&3)!==0&&fr(),Bt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Od?Un++:(Un=0,Od=e):Un=0,zn(0)}}function t1(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,gn(t)))}function fr(e){return J0(),W0(),e1(),a1()}function a1(){if(Ve!==5)return!1;var e=Ca,t=Ad;Ad=0;var a=Kr(Dl),l=R.T,n=D.p;try{D.p=32>a?32:a,R.T=null,a=Td,Td=null;var d=Ca,f=Dl;if(Ve=0,Ml=Ca=null,Dl=0,(ge&6)!==0)throw Error(u(331));var h=ge;if(ge|=4,H0(d.current),z0(d,d.current,f,a),ge=h,zn(0,!1),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(Ql,d)}catch{}return!0}finally{D.p=n,R.T=l,t1(e,t)}}function l1(e,t,a){t=vt(a,t),t=dd(e.stateNode,t,2),e=ha(e,t,2),e!==null&&(Jl(e,2),Bt(e))}function be(e,t,a){if(e.tag===3)l1(e,e,a);else for(;t!==null;){if(t.tag===3){l1(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Na===null||!Na.has(l))){e=vt(a,e),a=s0(2),l=ha(t,a,2),l!==null&&(d0(a,l,t,e),Jl(l,2),Bt(l));break}}t=t.return}}function Dd(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new fm;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(Cd=!0,n.add(a),e=$m.bind(null,e,t,a),t.then(e,e))}function $m(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ne===e&&(ue&a)===a&&(_e===4||_e===3&&(ue&62914560)===ue&&300>Mt()-jd?(ge&2)===0&&Ul(e,0):wd|=a,_l===ue&&(_l=0)),Bt(e)}function n1(e,t){t===0&&(t=Wu()),e=$l(e,t),e!==null&&(Jl(e,t),Bt(e))}function vm(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),n1(e,a)}function ym(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),n1(e,a)}function xm(e,t){return Vr(e,t)}var mr=null,Ll=null,Ud=!1,hr=!1,zd=!1,el=0;function Bt(e){e!==Ll&&e.next===null&&(Ll===null?mr=Ll=e:Ll=Ll.next=e),hr=!0,Ud||(Ud=!0,Sm())}function zn(e,t){if(!zd&&hr){zd=!0;do for(var a=!1,l=mr;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var d=0;else{var f=l.suspendedLanes,h=l.pingedLanes;d=(1<<31-rt(42|e)+1)-1,d&=n&~(f&~h),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(a=!0,d1(l,d))}else d=ue,d=xi(l,l===Ne?d:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(d&3)===0||Kl(l,d)||(a=!0,d1(l,d));l=l.next}while(a);zd=!1}}function bm(){i1()}function i1(){hr=Ud=!1;var e=0;el!==0&&(Om()&&(e=el),el=0);for(var t=Mt(),a=null,l=mr;l!==null;){var n=l.next,d=r1(l,t);d===0?(l.next=null,a===null?mr=n:a.next=n,n===null&&(Ll=a)):(a=l,(e!==0||(d&3)!==0)&&(hr=!0)),l=n}zn(e)}function r1(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,d=e.pendingLanes&-62914561;0<d;){var f=31-rt(d),h=1<<f,p=n[f];p===-1?((h&a)===0||(h&l)!==0)&&(n[f]=V2(h,t)):p<=t&&(e.expiredLanes|=h),d&=~h}if(t=Ne,a=ue,a=xi(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(pe===2||pe===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Ir(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Kl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Ir(l),Kr(a)){case 2:case 8:a=Qu;break;case 32:a=$i;break;case 268435456:a=Ku;break;default:a=$i}return l=s1.bind(null,e),a=Vr(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Ir(l),e.callbackPriority=2,e.callbackNode=null,2}function s1(e,t){if(Ve!==0&&Ve!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(fr()&&e.callbackNode!==a)return null;var l=ue;return l=xi(e,e===Ne?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Y0(e,l,t),r1(e,Mt()),e.callbackNode!=null&&e.callbackNode===a?s1.bind(null,e):null)}function d1(e,t){if(fr())return null;Y0(e,t,!0)}function Sm(){_m(function(){(ge&6)!==0?Vr(Xu,bm):i1()})}function Ld(){return el===0&&(el=Ju()),el}function u1(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:wi(""+e)}function o1(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Nm(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var d=u1((n[et]||null).action),f=l.submitter;f&&(t=(t=f[et]||null)?u1(t.formAction):f.getAttribute("formAction"),t!==null&&(d=t,f=null));var h=new Ti("action","action",null,l,n);e.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(el!==0){var p=f?o1(n,f):new FormData(n);ld(a,{pending:!0,data:p,method:n.method,action:d},null,p)}}else typeof d=="function"&&(h.preventDefault(),p=f?o1(n,f):new FormData(n),ld(a,{pending:!0,data:p,method:n.method,action:d},d,p))},currentTarget:n}]})}}for(var Bd=0;Bd<Ss.length;Bd++){var Hd=Ss[Bd],Cm=Hd.toLowerCase(),wm=Hd[0].toUpperCase()+Hd.slice(1);At(Cm,"on"+wm)}At(Yo,"onAnimationEnd"),At(Zo,"onAnimationIteration"),At(ko,"onAnimationStart"),At("dblclick","onDoubleClick"),At("focusin","onFocus"),At("focusout","onBlur"),At(P3,"onTransitionRun"),At(Y3,"onTransitionStart"),At(Z3,"onTransitionCancel"),At(Fo,"onTransitionEnd"),sl("onMouseEnter",["mouseout","mouseover"]),sl("onMouseLeave",["mouseout","mouseover"]),sl("onPointerEnter",["pointerout","pointerover"]),sl("onPointerLeave",["pointerout","pointerover"]),Ba("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ba("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ba("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ba("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ba("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ba("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ln="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Em=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ln));function c1(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var d=void 0;if(t)for(var f=l.length-1;0<=f;f--){var h=l[f],p=h.instance,C=h.currentTarget;if(h=h.listener,p!==d&&n.isPropagationStopped())break e;d=h,n.currentTarget=C;try{d(n)}catch(T){tr(T)}n.currentTarget=null,d=p}else for(f=0;f<l.length;f++){if(h=l[f],p=h.instance,C=h.currentTarget,h=h.listener,p!==d&&n.isPropagationStopped())break e;d=h,n.currentTarget=C;try{d(n)}catch(T){tr(T)}n.currentTarget=null,d=p}}}}function ne(e,t){var a=t[Jr];a===void 0&&(a=t[Jr]=new Set);var l=e+"__bubble";a.has(l)||(f1(t,e,2,!1),a.add(l))}function qd(e,t,a){var l=0;t&&(l|=4),f1(a,e,l,t)}var gr="_reactListening"+Math.random().toString(36).slice(2);function Gd(e){if(!e[gr]){e[gr]=!0,no.forEach(function(a){a!=="selectionchange"&&(Em.has(a)||qd(a,!1,e),qd(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[gr]||(t[gr]=!0,qd("selectionchange",!1,t))}}function f1(e,t,a,l){switch(z1(t)){case 2:var n=Wm;break;case 8:n=eh;break;default:n=tu}a=n.bind(null,t,a,e),n=void 0,!us||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Pd(e,t,a,l,n){var d=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var h=l.stateNode.containerInfo;if(h===n)break;if(f===4)for(f=l.return;f!==null;){var p=f.tag;if((p===3||p===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;h!==null;){if(f=nl(h),f===null)return;if(p=f.tag,p===5||p===6||p===26||p===27){l=d=f;continue e}h=h.parentNode}}l=l.return}yo(function(){var C=d,T=ss(a),M=[];e:{var w=Vo.get(e);if(w!==void 0){var E=Ti,Q=e;switch(e){case"keypress":if(ji(a)===0)break e;case"keydown":case"keyup":E=x3;break;case"focusin":Q="focus",E=ms;break;case"focusout":Q="blur",E=ms;break;case"beforeblur":case"afterblur":E=ms;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=So;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=d3;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=N3;break;case Yo:case Zo:case ko:E=c3;break;case Fo:E=w3;break;case"scroll":case"scrollend":E=r3;break;case"wheel":E=j3;break;case"copy":case"cut":case"paste":E=m3;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=Co;break;case"toggle":case"beforetoggle":E=T3}var F=(t&4)!==0,xe=!F&&(e==="scroll"||e==="scrollend"),S=F?w!==null?w+"Capture":null:w;F=[];for(var b=C,N;b!==null;){var _=b;if(N=_.stateNode,_=_.tag,_!==5&&_!==26&&_!==27||N===null||S===null||(_=tn(b,S),_!=null&&F.push(Bn(b,_,N))),xe)break;b=b.return}0<F.length&&(w=new E(w,Q,null,a,T),M.push({event:w,listeners:F}))}}if((t&7)===0){e:{if(w=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",w&&a!==rs&&(Q=a.relatedTarget||a.fromElement)&&(nl(Q)||Q[ll]))break e;if((E||w)&&(w=T.window===T?T:(w=T.ownerDocument)?w.defaultView||w.parentWindow:window,E?(Q=a.relatedTarget||a.toElement,E=C,Q=Q?nl(Q):null,Q!==null&&(xe=m(Q),F=Q.tag,Q!==xe||F!==5&&F!==27&&F!==6)&&(Q=null)):(E=null,Q=C),E!==Q)){if(F=So,_="onMouseLeave",S="onMouseEnter",b="mouse",(e==="pointerout"||e==="pointerover")&&(F=Co,_="onPointerLeave",S="onPointerEnter",b="pointer"),xe=E==null?w:en(E),N=Q==null?w:en(Q),w=new F(_,b+"leave",E,a,T),w.target=xe,w.relatedTarget=N,_=null,nl(T)===C&&(F=new F(S,b+"enter",Q,a,T),F.target=N,F.relatedTarget=xe,_=F),xe=_,E&&Q)t:{for(F=E,S=Q,b=0,N=F;N;N=Bl(N))b++;for(N=0,_=S;_;_=Bl(_))N++;for(;0<b-N;)F=Bl(F),b--;for(;0<N-b;)S=Bl(S),N--;for(;b--;){if(F===S||S!==null&&F===S.alternate)break t;F=Bl(F),S=Bl(S)}F=null}else F=null;E!==null&&m1(M,w,E,F,!1),Q!==null&&xe!==null&&m1(M,xe,Q,F,!0)}}e:{if(w=C?en(C):window,E=w.nodeName&&w.nodeName.toLowerCase(),E==="select"||E==="input"&&w.type==="file")var Y=_o;else if(Oo(w))if(Mo)Y=H3;else{Y=L3;var ae=z3}else E=w.nodeName,!E||E.toLowerCase()!=="input"||w.type!=="checkbox"&&w.type!=="radio"?C&&is(C.elementType)&&(Y=_o):Y=B3;if(Y&&(Y=Y(e,C))){Ro(M,Y,a,T);break e}ae&&ae(e,w,C),e==="focusout"&&C&&w.type==="number"&&C.memoizedProps.value!=null&&ns(w,"number",w.value)}switch(ae=C?en(C):window,e){case"focusin":(Oo(ae)||ae.contentEditable==="true")&&(hl=ae,ys=C,on=null);break;case"focusout":on=ys=hl=null;break;case"mousedown":xs=!0;break;case"contextmenu":case"mouseup":case"dragend":xs=!1,Go(M,a,T);break;case"selectionchange":if(G3)break;case"keydown":case"keyup":Go(M,a,T)}var k;if(gs)e:{switch(e){case"compositionstart":var I="onCompositionStart";break e;case"compositionend":I="onCompositionEnd";break e;case"compositionupdate":I="onCompositionUpdate";break e}I=void 0}else ml?Ao(e,a)&&(I="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(I="onCompositionStart");I&&(wo&&a.locale!=="ko"&&(ml||I!=="onCompositionStart"?I==="onCompositionEnd"&&ml&&(k=xo()):(oa=T,os="value"in oa?oa.value:oa.textContent,ml=!0)),ae=pr(C,I),0<ae.length&&(I=new No(I,e,null,a,T),M.push({event:I,listeners:ae}),k?I.data=k:(k=To(a),k!==null&&(I.data=k)))),(k=R3?_3(e,a):M3(e,a))&&(I=pr(C,"onBeforeInput"),0<I.length&&(ae=new No("onBeforeInput","beforeinput",null,a,T),M.push({event:ae,listeners:I}),ae.data=k)),Nm(M,e,C,a,T)}c1(M,t)})}function Bn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function pr(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,d=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||d===null||(n=tn(e,a),n!=null&&l.unshift(Bn(e,n,d)),n=tn(e,t),n!=null&&l.push(Bn(e,n,d))),e.tag===3)return l;e=e.return}return[]}function Bl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function m1(e,t,a,l,n){for(var d=t._reactName,f=[];a!==null&&a!==l;){var h=a,p=h.alternate,C=h.stateNode;if(h=h.tag,p!==null&&p===l)break;h!==5&&h!==26&&h!==27||C===null||(p=C,n?(C=tn(a,d),C!=null&&f.unshift(Bn(a,C,p))):n||(C=tn(a,d),C!=null&&f.push(Bn(a,C,p)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var jm=/\r\n?/g,Am=/\u0000|\uFFFD/g;function h1(e){return(typeof e=="string"?e:""+e).replace(jm,`
`).replace(Am,"")}function g1(e,t){return t=h1(t),h1(e)===t}function $r(){}function ye(e,t,a,l,n,d){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||ol(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&ol(e,""+l);break;case"className":Si(e,"class",l);break;case"tabIndex":Si(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Si(e,a,l);break;case"style":$o(e,l,d);break;case"data":if(t!=="object"){Si(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=wi(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(a==="formAction"?(t!=="input"&&ye(e,t,"name",n.name,n,null),ye(e,t,"formEncType",n.formEncType,n,null),ye(e,t,"formMethod",n.formMethod,n,null),ye(e,t,"formTarget",n.formTarget,n,null)):(ye(e,t,"encType",n.encType,n,null),ye(e,t,"method",n.method,n,null),ye(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=wi(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=$r);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=wi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":ne("beforetoggle",e),ne("toggle",e),bi(e,"popover",l);break;case"xlinkActuate":qt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":qt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":qt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":qt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":qt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":qt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":qt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":qt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":qt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":bi(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=n3.get(a)||a,bi(e,a,l))}}function Yd(e,t,a,l,n,d){switch(a){case"style":$o(e,l,d);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof l=="string"?ol(e,l):(typeof l=="number"||typeof l=="bigint")&&ol(e,""+l);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"onClick":l!=null&&(e.onclick=$r);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!io.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),d=e[et]||null,d=d!=null?d[a]:null,typeof d=="function"&&e.removeEventListener(t,d,n),typeof l=="function")){typeof d!="function"&&d!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):bi(e,a,l)}}}function Ie(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ne("error",e),ne("load",e);var l=!1,n=!1,d;for(d in a)if(a.hasOwnProperty(d)){var f=a[d];if(f!=null)switch(d){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:ye(e,t,d,f,a,null)}}n&&ye(e,t,"srcSet",a.srcSet,a,null),l&&ye(e,t,"src",a.src,a,null);return;case"input":ne("invalid",e);var h=d=f=n=null,p=null,C=null;for(l in a)if(a.hasOwnProperty(l)){var T=a[l];if(T!=null)switch(l){case"name":n=T;break;case"type":f=T;break;case"checked":p=T;break;case"defaultChecked":C=T;break;case"value":d=T;break;case"defaultValue":h=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(u(137,t));break;default:ye(e,t,l,T,a,null)}}mo(e,d,h,p,C,f,n,!1),Ni(e);return;case"select":ne("invalid",e),l=f=d=null;for(n in a)if(a.hasOwnProperty(n)&&(h=a[n],h!=null))switch(n){case"value":d=h;break;case"defaultValue":f=h;break;case"multiple":l=h;default:ye(e,t,n,h,a,null)}t=d,a=f,e.multiple=!!l,t!=null?ul(e,!!l,t,!1):a!=null&&ul(e,!!l,a,!0);return;case"textarea":ne("invalid",e),d=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(h=a[f],h!=null))switch(f){case"value":l=h;break;case"defaultValue":n=h;break;case"children":d=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(u(91));break;default:ye(e,t,f,h,a,null)}go(e,l,n,d),Ni(e);return;case"option":for(p in a)if(a.hasOwnProperty(p)&&(l=a[p],l!=null))switch(p){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:ye(e,t,p,l,a,null)}return;case"dialog":ne("beforetoggle",e),ne("toggle",e),ne("cancel",e),ne("close",e);break;case"iframe":case"object":ne("load",e);break;case"video":case"audio":for(l=0;l<Ln.length;l++)ne(Ln[l],e);break;case"image":ne("error",e),ne("load",e);break;case"details":ne("toggle",e);break;case"embed":case"source":case"link":ne("error",e),ne("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(C in a)if(a.hasOwnProperty(C)&&(l=a[C],l!=null))switch(C){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:ye(e,t,C,l,a,null)}return;default:if(is(t)){for(T in a)a.hasOwnProperty(T)&&(l=a[T],l!==void 0&&Yd(e,t,T,l,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(l=a[h],l!=null&&ye(e,t,h,l,a,null))}function Tm(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,d=null,f=null,h=null,p=null,C=null,T=null;for(E in a){var M=a[E];if(a.hasOwnProperty(E)&&M!=null)switch(E){case"checked":break;case"value":break;case"defaultValue":p=M;default:l.hasOwnProperty(E)||ye(e,t,E,null,l,M)}}for(var w in l){var E=l[w];if(M=a[w],l.hasOwnProperty(w)&&(E!=null||M!=null))switch(w){case"type":d=E;break;case"name":n=E;break;case"checked":C=E;break;case"defaultChecked":T=E;break;case"value":f=E;break;case"defaultValue":h=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(u(137,t));break;default:E!==M&&ye(e,t,w,E,l,M)}}ls(e,f,h,p,C,T,d,n);return;case"select":E=f=h=w=null;for(d in a)if(p=a[d],a.hasOwnProperty(d)&&p!=null)switch(d){case"value":break;case"multiple":E=p;default:l.hasOwnProperty(d)||ye(e,t,d,null,l,p)}for(n in l)if(d=l[n],p=a[n],l.hasOwnProperty(n)&&(d!=null||p!=null))switch(n){case"value":w=d;break;case"defaultValue":h=d;break;case"multiple":f=d;default:d!==p&&ye(e,t,n,d,l,p)}t=h,a=f,l=E,w!=null?ul(e,!!a,w,!1):!!l!=!!a&&(t!=null?ul(e,!!a,t,!0):ul(e,!!a,a?[]:"",!1));return;case"textarea":E=w=null;for(h in a)if(n=a[h],a.hasOwnProperty(h)&&n!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:ye(e,t,h,null,l,n)}for(f in l)if(n=l[f],d=a[f],l.hasOwnProperty(f)&&(n!=null||d!=null))switch(f){case"value":w=n;break;case"defaultValue":E=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(u(91));break;default:n!==d&&ye(e,t,f,n,l,d)}ho(e,w,E);return;case"option":for(var Q in a)if(w=a[Q],a.hasOwnProperty(Q)&&w!=null&&!l.hasOwnProperty(Q))switch(Q){case"selected":e.selected=!1;break;default:ye(e,t,Q,null,l,w)}for(p in l)if(w=l[p],E=a[p],l.hasOwnProperty(p)&&w!==E&&(w!=null||E!=null))switch(p){case"selected":e.selected=w&&typeof w!="function"&&typeof w!="symbol";break;default:ye(e,t,p,w,l,E)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var F in a)w=a[F],a.hasOwnProperty(F)&&w!=null&&!l.hasOwnProperty(F)&&ye(e,t,F,null,l,w);for(C in l)if(w=l[C],E=a[C],l.hasOwnProperty(C)&&w!==E&&(w!=null||E!=null))switch(C){case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(u(137,t));break;default:ye(e,t,C,w,l,E)}return;default:if(is(t)){for(var xe in a)w=a[xe],a.hasOwnProperty(xe)&&w!==void 0&&!l.hasOwnProperty(xe)&&Yd(e,t,xe,void 0,l,w);for(T in l)w=l[T],E=a[T],!l.hasOwnProperty(T)||w===E||w===void 0&&E===void 0||Yd(e,t,T,w,l,E);return}}for(var S in a)w=a[S],a.hasOwnProperty(S)&&w!=null&&!l.hasOwnProperty(S)&&ye(e,t,S,null,l,w);for(M in l)w=l[M],E=a[M],!l.hasOwnProperty(M)||w===E||w==null&&E==null||ye(e,t,M,w,l,E)}var Zd=null,kd=null;function vr(e){return e.nodeType===9?e:e.ownerDocument}function p1(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function $1(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Fd(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vd=null;function Om(){var e=window.event;return e&&e.type==="popstate"?e===Vd?!1:(Vd=e,!0):(Vd=null,!1)}var v1=typeof setTimeout=="function"?setTimeout:void 0,Rm=typeof clearTimeout=="function"?clearTimeout:void 0,y1=typeof Promise=="function"?Promise:void 0,_m=typeof queueMicrotask=="function"?queueMicrotask:typeof y1<"u"?function(e){return y1.resolve(null).then(e).catch(Mm)}:v1;function Mm(e){setTimeout(function(){throw e})}function Ea(e){return e==="head"}function x1(e,t){var a=t,l=0,n=0;do{var d=a.nextSibling;if(e.removeChild(a),d&&d.nodeType===8)if(a=d.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&Hn(f.documentElement),a&2&&Hn(f.body),a&4)for(a=f.head,Hn(a),f=a.firstChild;f;){var h=f.nextSibling,p=f.nodeName;f[Wl]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=h}}if(n===0){e.removeChild(d),Vn(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=d}while(a);Vn(t)}function Id(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Id(a),Wr(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Dm(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Wl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(d=e.getAttribute("rel"),d==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(d!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(d=e.getAttribute("src"),(d!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&d&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var d=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===d)return e}else return e;if(e=Ot(e.nextSibling),e===null)break}return null}function Um(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Ot(e.nextSibling),e===null))return null;return e}function Xd(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function zm(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Ot(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Qd=null;function b1(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function S1(e,t,a){switch(t=vr(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function Hn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Wr(e)}var Ct=new Map,N1=new Set;function yr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ea=D.d;D.d={f:Lm,r:Bm,D:Hm,C:qm,L:Gm,m:Pm,X:Zm,S:Ym,M:km};function Lm(){var e=ea.f(),t=or();return e||t}function Bm(e){var t=il(e);t!==null&&t.tag===5&&t.type==="form"?Yc(t):ea.r(e)}var Hl=typeof document>"u"?null:document;function C1(e,t,a){var l=Hl;if(l&&typeof t=="string"&&t){var n=$t(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),N1.has(n)||(N1.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),Ie(t,"link",e),Pe(t),l.head.appendChild(t)))}}function Hm(e){ea.D(e),C1("dns-prefetch",e,null)}function qm(e,t){ea.C(e,t),C1("preconnect",e,t)}function Gm(e,t,a){ea.L(e,t,a);var l=Hl;if(l&&e&&t){var n='link[rel="preload"][as="'+$t(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+$t(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+$t(a.imageSizes)+'"]')):n+='[href="'+$t(e)+'"]';var d=n;switch(t){case"style":d=ql(e);break;case"script":d=Gl(e)}Ct.has(d)||(e=$({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Ct.set(d,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(qn(d))||t==="script"&&l.querySelector(Gn(d))||(t=l.createElement("link"),Ie(t,"link",e),Pe(t),l.head.appendChild(t)))}}function Pm(e,t){ea.m(e,t);var a=Hl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+$t(l)+'"][href="'+$t(e)+'"]',d=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=Gl(e)}if(!Ct.has(d)&&(e=$({rel:"modulepreload",href:e},t),Ct.set(d,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Gn(d)))return}l=a.createElement("link"),Ie(l,"link",e),Pe(l),a.head.appendChild(l)}}}function Ym(e,t,a){ea.S(e,t,a);var l=Hl;if(l&&e){var n=rl(l).hoistableStyles,d=ql(e);t=t||"default";var f=n.get(d);if(!f){var h={loading:0,preload:null};if(f=l.querySelector(qn(d)))h.loading=5;else{e=$({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Ct.get(d))&&Kd(e,a);var p=f=l.createElement("link");Pe(p),Ie(p,"link",e),p._p=new Promise(function(C,T){p.onload=C,p.onerror=T}),p.addEventListener("load",function(){h.loading|=1}),p.addEventListener("error",function(){h.loading|=2}),h.loading|=4,xr(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:h},n.set(d,f)}}}function Zm(e,t){ea.X(e,t);var a=Hl;if(a&&e){var l=rl(a).hoistableScripts,n=Gl(e),d=l.get(n);d||(d=a.querySelector(Gn(n)),d||(e=$({src:e,async:!0},t),(t=Ct.get(n))&&Jd(e,t),d=a.createElement("script"),Pe(d),Ie(d,"link",e),a.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},l.set(n,d))}}function km(e,t){ea.M(e,t);var a=Hl;if(a&&e){var l=rl(a).hoistableScripts,n=Gl(e),d=l.get(n);d||(d=a.querySelector(Gn(n)),d||(e=$({src:e,async:!0,type:"module"},t),(t=Ct.get(n))&&Jd(e,t),d=a.createElement("script"),Pe(d),Ie(d,"link",e),a.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},l.set(n,d))}}function w1(e,t,a,l){var n=(n=sa.current)?yr(n):null;if(!n)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=ql(a.href),a=rl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=ql(a.href);var d=rl(n).hoistableStyles,f=d.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,f),(d=n.querySelector(qn(e)))&&!d._p&&(f.instance=d,f.state.loading=5),Ct.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Ct.set(e,a),d||Fm(n,e,a,f.state))),t&&l===null)throw Error(u(528,""));return f}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Gl(a),a=rl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function ql(e){return'href="'+$t(e)+'"'}function qn(e){return'link[rel="stylesheet"]['+e+"]"}function E1(e){return $({},e,{"data-precedence":e.precedence,precedence:null})}function Fm(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Ie(t,"link",a),Pe(t),e.head.appendChild(t))}function Gl(e){return'[src="'+$t(e)+'"]'}function Gn(e){return"script[async]"+e}function j1(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+$t(a.href)+'"]');if(l)return t.instance=l,Pe(l),l;var n=$({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Pe(l),Ie(l,"style",n),xr(l,a.precedence,e),t.instance=l;case"stylesheet":n=ql(a.href);var d=e.querySelector(qn(n));if(d)return t.state.loading|=4,t.instance=d,Pe(d),d;l=E1(a),(n=Ct.get(n))&&Kd(l,n),d=(e.ownerDocument||e).createElement("link"),Pe(d);var f=d;return f._p=new Promise(function(h,p){f.onload=h,f.onerror=p}),Ie(d,"link",l),t.state.loading|=4,xr(d,a.precedence,e),t.instance=d;case"script":return d=Gl(a.src),(n=e.querySelector(Gn(d)))?(t.instance=n,Pe(n),n):(l=a,(n=Ct.get(d))&&(l=$({},a),Jd(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Pe(n),Ie(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,xr(l,a.precedence,e));return t.instance}function xr(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,d=n,f=0;f<l.length;f++){var h=l[f];if(h.dataset.precedence===t)d=h;else if(d!==n)break}d?d.parentNode.insertBefore(e,d.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Kd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Jd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var br=null;function A1(e,t,a){if(br===null){var l=new Map,n=br=new Map;n.set(a,l)}else n=br,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var d=a[n];if(!(d[Wl]||d[Xe]||e==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var f=d.getAttribute(t)||"";f=e+f;var h=l.get(f);h?h.push(d):l.set(f,[d])}}return l}function T1(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Vm(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function O1(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Pn=null;function Im(){}function Xm(e,t,a){if(Pn===null)throw Error(u(475));var l=Pn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=ql(a.href),d=e.querySelector(qn(n));if(d){e=d._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Sr.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=d,Pe(d);return}d=e.ownerDocument||e,a=E1(a),(n=Ct.get(n))&&Kd(a,n),d=d.createElement("link"),Pe(d);var f=d;f._p=new Promise(function(h,p){f.onload=h,f.onerror=p}),Ie(d,"link",a),t.instance=d}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Sr.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Qm(){if(Pn===null)throw Error(u(475));var e=Pn;return e.stylesheets&&e.count===0&&Wd(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Wd(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Sr(){if(this.count--,this.count===0){if(this.stylesheets)Wd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Nr=null;function Wd(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Nr=new Map,t.forEach(Km,e),Nr=null,Sr.call(e))}function Km(e,t){if(!(t.state.loading&4)){var a=Nr.get(e);if(a)var l=a.get(null);else{a=new Map,Nr.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<n.length;d++){var f=n[d];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),d=a.get(f)||l,d===l&&a.set(null,n),a.set(f,n),this.count++,l=Sr.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),d?d.parentNode.insertBefore(n,d.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Yn={$$typeof:q,Provider:null,Consumer:null,_currentValue:H,_currentValue2:H,_threadCount:0};function Jm(e,t,a,l,n,d,f,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Xr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Xr(0),this.hiddenUpdates=Xr(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=d,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function R1(e,t,a,l,n,d,f,h,p,C,T,M){return e=new Jm(e,t,a,f,h,p,C,M),t=1,d===!0&&(t|=24),d=dt(3,null,null,t),e.current=d,d.stateNode=e,t=Ds(),t.refCount++,e.pooledCache=t,t.refCount++,d.memoizedState={element:l,isDehydrated:a,cache:t},Bs(d),e}function _1(e){return e?(e=vl,e):vl}function M1(e,t,a,l,n,d){n=_1(n),l.context===null?l.context=n:l.pendingContext=n,l=ma(t),l.payload={element:a},d=d===void 0?null:d,d!==null&&(l.callback=d),a=ha(e,l,t),a!==null&&(mt(a,e,t),yn(a,e,t))}function D1(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function eu(e,t){D1(e,t),(e=e.alternate)&&D1(e,t)}function U1(e){if(e.tag===13){var t=$l(e,67108864);t!==null&&mt(t,e,67108864),eu(e,67108864)}}var Cr=!0;function Wm(e,t,a,l){var n=R.T;R.T=null;var d=D.p;try{D.p=2,tu(e,t,a,l)}finally{D.p=d,R.T=n}}function eh(e,t,a,l){var n=R.T;R.T=null;var d=D.p;try{D.p=8,tu(e,t,a,l)}finally{D.p=d,R.T=n}}function tu(e,t,a,l){if(Cr){var n=au(l);if(n===null)Pd(e,t,l,wr,a),L1(e,l);else if(ah(n,e,t,a,l))l.stopPropagation();else if(L1(e,l),t&4&&-1<th.indexOf(e)){for(;n!==null;){var d=il(n);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var f=La(d.pendingLanes);if(f!==0){var h=d;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var p=1<<31-rt(f);h.entanglements[1]|=p,f&=~p}Bt(d),(ge&6)===0&&(dr=Mt()+500,zn(0))}}break;case 13:h=$l(d,2),h!==null&&mt(h,d,2),or(),eu(d,2)}if(d=au(l),d===null&&Pd(e,t,l,wr,a),d===n)break;n=d}n!==null&&l.stopPropagation()}else Pd(e,t,l,null,a)}}function au(e){return e=ss(e),lu(e)}var wr=null;function lu(e){if(wr=null,e=nl(e),e!==null){var t=m(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=g(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return wr=e,null}function z1(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(q2()){case Xu:return 2;case Qu:return 8;case $i:case G2:return 32;case Ku:return 268435456;default:return 32}default:return 32}}var nu=!1,ja=null,Aa=null,Ta=null,Zn=new Map,kn=new Map,Oa=[],th="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function L1(e,t){switch(e){case"focusin":case"focusout":ja=null;break;case"dragenter":case"dragleave":Aa=null;break;case"mouseover":case"mouseout":Ta=null;break;case"pointerover":case"pointerout":Zn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":kn.delete(t.pointerId)}}function Fn(e,t,a,l,n,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:d,targetContainers:[n]},t!==null&&(t=il(t),t!==null&&U1(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function ah(e,t,a,l,n){switch(t){case"focusin":return ja=Fn(ja,e,t,a,l,n),!0;case"dragenter":return Aa=Fn(Aa,e,t,a,l,n),!0;case"mouseover":return Ta=Fn(Ta,e,t,a,l,n),!0;case"pointerover":var d=n.pointerId;return Zn.set(d,Fn(Zn.get(d)||null,e,t,a,l,n)),!0;case"gotpointercapture":return d=n.pointerId,kn.set(d,Fn(kn.get(d)||null,e,t,a,l,n)),!0}return!1}function B1(e){var t=nl(e.target);if(t!==null){var a=m(t);if(a!==null){if(t=a.tag,t===13){if(t=g(a),t!==null){e.blockedOn=t,X2(e.priority,function(){if(a.tag===13){var l=ft();l=Qr(l);var n=$l(a,l);n!==null&&mt(n,a,l),eu(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Er(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=au(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);rs=l,a.target.dispatchEvent(l),rs=null}else return t=il(a),t!==null&&U1(t),e.blockedOn=a,!1;t.shift()}return!0}function H1(e,t,a){Er(e)&&a.delete(t)}function lh(){nu=!1,ja!==null&&Er(ja)&&(ja=null),Aa!==null&&Er(Aa)&&(Aa=null),Ta!==null&&Er(Ta)&&(Ta=null),Zn.forEach(H1),kn.forEach(H1)}function jr(e,t){e.blockedOn===t&&(e.blockedOn=null,nu||(nu=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,lh)))}var Ar=null;function q1(e){Ar!==e&&(Ar=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Ar===e&&(Ar=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(lu(l||a)===null)continue;break}var d=il(a);d!==null&&(e.splice(t,3),t-=3,ld(d,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Vn(e){function t(p){return jr(p,e)}ja!==null&&jr(ja,e),Aa!==null&&jr(Aa,e),Ta!==null&&jr(Ta,e),Zn.forEach(t),kn.forEach(t);for(var a=0;a<Oa.length;a++){var l=Oa[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Oa.length&&(a=Oa[0],a.blockedOn===null);)B1(a),a.blockedOn===null&&Oa.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],d=a[l+1],f=n[et]||null;if(typeof d=="function")f||q1(a);else if(f){var h=null;if(d&&d.hasAttribute("formAction")){if(n=d,f=d[et]||null)h=f.formAction;else if(lu(n)!==null)continue}else h=f.action;typeof h=="function"?a[l+1]=h:(a.splice(l,3),l-=3),q1(a)}}}function iu(e){this._internalRoot=e}Tr.prototype.render=iu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,l=ft();M1(a,l,e,t,null,null)},Tr.prototype.unmount=iu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;M1(e.current,2,null,e,null,null),or(),t[ll]=null}};function Tr(e){this._internalRoot=e}Tr.prototype.unstable_scheduleHydration=function(e){if(e){var t=ao();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Oa.length&&t!==0&&t<Oa[a].priority;a++);Oa.splice(a,0,e),a===0&&B1(e)}};var G1=r.version;if(G1!=="19.1.0")throw Error(u(527,G1,"19.1.0"));D.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=x(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var nh={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Or=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Or.isDisabled&&Or.supportsFiber)try{Ql=Or.inject(nh),it=Or}catch{}}return Xn.createRoot=function(e,t){if(!c(e))throw Error(u(299));var a=!1,l="",n=l0,d=n0,f=i0,h=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(d=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=R1(e,1,!1,null,null,a,l,n,d,f,h,null),e[ll]=t.current,Gd(e),new iu(t)},Xn.hydrateRoot=function(e,t,a){if(!c(e))throw Error(u(299));var l=!1,n="",d=l0,f=n0,h=i0,p=null,C=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(d=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(p=a.unstable_transitionCallbacks),a.formState!==void 0&&(C=a.formState)),t=R1(e,1,!0,t,a??null,l,n,d,f,h,p,C),t.context=_1(null),a=t.current,l=ft(),l=Qr(l),n=ma(l),n.callback=null,ha(a,n,l),a=l,t.current.lanes=a,Jl(t,a),Bt(t),e[ll]=t.current,Gd(e),new Tr(t)},Xn.version="19.1.0",Xn}var K1;function Rh(){if(K1)return du.exports;K1=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),du.exports=Oh(),du.exports}var h2=Rh(),Qn={},cu={exports:{}},J1;function za(){return J1||(J1=1,function(i){function r(s){return s&&s.__esModule?s:{default:s}}i.exports=r,i.exports.__esModule=!0,i.exports.default=i.exports}(cu)),cu.exports}var Kn={},W1;function _h(){if(W1)return Kn;W1=1,Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var i={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};return Kn.default=i,Kn}var Jn={},Wn={},ei={},fu={exports:{}},mu={exports:{}},hu={exports:{}},gu={exports:{}},ef;function g2(){return ef||(ef=1,function(i){function r(s){"@babel/helpers - typeof";return i.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},i.exports.__esModule=!0,i.exports.default=i.exports,r(s)}i.exports=r,i.exports.__esModule=!0,i.exports.default=i.exports}(gu)),gu.exports}var pu={exports:{}},tf;function Mh(){return tf||(tf=1,function(i){var r=g2().default;function s(u,c){if(r(u)!="object"||!u)return u;var m=u[Symbol.toPrimitive];if(m!==void 0){var g=m.call(u,c||"default");if(r(g)!="object")return g;throw new TypeError("@@toPrimitive must return a primitive value.")}return(c==="string"?String:Number)(u)}i.exports=s,i.exports.__esModule=!0,i.exports.default=i.exports}(pu)),pu.exports}var af;function Dh(){return af||(af=1,function(i){var r=g2().default,s=Mh();function u(c){var m=s(c,"string");return r(m)=="symbol"?m:m+""}i.exports=u,i.exports.__esModule=!0,i.exports.default=i.exports}(hu)),hu.exports}var lf;function Uh(){return lf||(lf=1,function(i){var r=Dh();function s(u,c,m){return(c=r(c))in u?Object.defineProperty(u,c,{value:m,enumerable:!0,configurable:!0,writable:!0}):u[c]=m,u}i.exports=s,i.exports.__esModule=!0,i.exports.default=i.exports}(mu)),mu.exports}var nf;function p2(){return nf||(nf=1,function(i){var r=Uh();function s(c,m){var g=Object.keys(c);if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(c);m&&(v=v.filter(function(x){return Object.getOwnPropertyDescriptor(c,x).enumerable})),g.push.apply(g,v)}return g}function u(c){for(var m=1;m<arguments.length;m++){var g=arguments[m]!=null?arguments[m]:{};m%2?s(Object(g),!0).forEach(function(v){r(c,v,g[v])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(g)):s(Object(g)).forEach(function(v){Object.defineProperty(c,v,Object.getOwnPropertyDescriptor(g,v))})}return c}i.exports=u,i.exports.__esModule=!0,i.exports.default=i.exports}(fu)),fu.exports}var ti={},rf;function $2(){return rf||(rf=1,Object.defineProperty(ti,"__esModule",{value:!0}),ti.commonLocale=void 0,ti.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),ti}var sf;function zh(){if(sf)return ei;sf=1;var i=za().default;Object.defineProperty(ei,"__esModule",{value:!0}),ei.default=void 0;var r=i(p2()),s=$2(),u=(0,r.default)((0,r.default)({},s.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});return ei.default=u,ei}var ai={},df;function v2(){if(df)return ai;df=1,Object.defineProperty(ai,"__esModule",{value:!0}),ai.default=void 0;const i={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};return ai.default=i,ai}var uf;function y2(){if(uf)return Wn;uf=1;var i=za().default;Object.defineProperty(Wn,"__esModule",{value:!0}),Wn.default=void 0;var r=i(zh()),s=i(v2());const u={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},r.default),timePickerLocale:Object.assign({},s.default)};return u.lang.ok="确定",Wn.default=u,Wn}var of;function Lh(){if(of)return Jn;of=1;var i=za().default;Object.defineProperty(Jn,"__esModule",{value:!0}),Jn.default=void 0;var r=i(y2());return Jn.default=r.default,Jn}var cf;function Bh(){if(cf)return Qn;cf=1;var i=za().default;Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var r=i(_h()),s=i(Lh()),u=i(y2()),c=i(v2());const m="${label}不是一个有效的${type}",g={locale:"zh-cn",Pagination:r.default,DatePicker:u.default,TimePicker:c.default,Calendar:s.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:m,method:m,array:m,object:m,number:m,date:m,boolean:m,integer:m,float:m,regexp:m,email:m,url:m,hex:m},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};return Qn.default=g,Qn}var $u,ff;function Hh(){return ff||(ff=1,$u=Bh()),$u}var qh=Hh();const Gh=n2(qh);var li={},ni={},mf;function Ph(){if(mf)return ni;mf=1,Object.defineProperty(ni,"__esModule",{value:!0}),ni.default=void 0;var i={items_per_page:"/ page",jump_to:"Go to",jump_to_confirm:"confirm",page:"Page",prev_page:"Previous Page",next_page:"Next Page",prev_5:"Previous 5 Pages",next_5:"Next 5 Pages",prev_3:"Previous 3 Pages",next_3:"Next 3 Pages",page_size:"Page Size"};return ni.default=i,ni}var ii={},ri={},si={},hf;function Yh(){if(hf)return si;hf=1;var i=za().default;Object.defineProperty(si,"__esModule",{value:!0}),si.default=void 0;var r=i(p2()),s=$2(),u=(0,r.default)((0,r.default)({},s.commonLocale),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"});return si.default=u,si}var di={},gf;function x2(){if(gf)return di;gf=1,Object.defineProperty(di,"__esModule",{value:!0}),di.default=void 0;const i={placeholder:"Select time",rangePlaceholder:["Start time","End time"]};return di.default=i,di}var pf;function b2(){if(pf)return ri;pf=1;var i=za().default;Object.defineProperty(ri,"__esModule",{value:!0}),ri.default=void 0;var r=i(Yh()),s=i(x2());const u={lang:Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},r.default),timePickerLocale:Object.assign({},s.default)};return ri.default=u,ri}var $f;function Zh(){if($f)return ii;$f=1;var i=za().default;Object.defineProperty(ii,"__esModule",{value:!0}),ii.default=void 0;var r=i(b2());return ii.default=r.default,ii}var vf;function kh(){if(vf)return li;vf=1;var i=za().default;Object.defineProperty(li,"__esModule",{value:!0}),li.default=void 0;var r=i(Ph()),s=i(Zh()),u=i(b2()),c=i(x2());const m="${label} is not a valid ${type}",g={locale:"en",Pagination:r.default,DatePicker:u.default,TimePicker:c.default,Calendar:s.default,global:{placeholder:"Please select",close:"Close"},Table:{filterTitle:"Filter menu",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"No filters",filterCheckAll:"Select all items",filterSearchPlaceholder:"Search in filters",emptyText:"No data",selectAll:"Select current page",selectInvert:"Invert current page",selectNone:"Clear all data",selectionAll:"Select all data",sortTitle:"Sort",expand:"Expand row",collapse:"Collapse row",triggerDesc:"Click to sort descending",triggerAsc:"Click to sort ascending",cancelSort:"Click to cancel sorting"},Tour:{Next:"Next",Previous:"Previous",Finish:"Finish"},Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Cancel"},Transfer:{titles:["",""],searchPlaceholder:"Search here",itemUnit:"item",itemsUnit:"items",remove:"Remove",selectCurrent:"Select current page",removeCurrent:"Remove current page",selectAll:"Select all data",deselectAll:"Deselect all data",removeAll:"Remove all data",selectInvert:"Invert current page"},Upload:{uploading:"Uploading...",removeFile:"Remove file",uploadError:"Upload error",previewFile:"Preview file",downloadFile:"Download file"},Empty:{description:"No data"},Icon:{icon:"icon"},Text:{edit:"Edit",copy:"Copy",copied:"Copied",expand:"Expand",collapse:"Collapse"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:m,method:m,array:m,object:m,number:m,date:m,boolean:m,integer:m,float:m,regexp:m,email:m,url:m,hex:m},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}},Image:{preview:"Preview"},QRCode:{expired:"QR code expired",refresh:"Refresh",scanned:"Scanned"},ColorPicker:{presetEmpty:"Empty",transparent:"Transparent",singleColor:"Single",gradientColor:"Gradient"}};return li.default=g,li}var vu,yf;function Fh(){return yf||(yf=1,vu=kh()),vu}var Vh=Fh();const Ih=n2(Vh),wt="/assets/cover-B-bydeXp.png",xf={BASE_URL:"/",DEV:!1,MODE:"staging",PROD:!0,SSR:!1,VITE_API_BASE_URL:"https://musicapi.renee-arts.com/api/v1"},ui=new Map,_r=i=>{const r=ui.get(i);return r?Object.fromEntries(Object.entries(r.stores).map(([s,u])=>[s,u.getState()])):{}},Xh=(i,r,s)=>{if(i===void 0)return{type:"untracked",connection:r.connect(s)};const u=ui.get(s.name);if(u)return{type:"tracked",store:i,...u};const c={connection:r.connect(s),stores:{}};return ui.set(s.name,c),{type:"tracked",store:i,...c}},Qh=(i,r)=>{if(r===void 0)return;const s=ui.get(i);s&&(delete s.stores[r],Object.keys(s.stores).length===0&&ui.delete(i))},Kh=i=>{var r,s;if(!i)return;const u=i.split(`
`),c=u.findIndex(g=>g.includes("api.setState"));if(c<0)return;const m=((r=u[c+1])==null?void 0:r.trim())||"";return(s=/.+ (.+) .+/.exec(m))==null?void 0:s[1]},Jh=(i,r={})=>(s,u,c)=>{const{enabled:m,anonymousActionType:g,store:v,...x}=r;let y;try{y=(m??(xf?"staging":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!y)return i(s,u,c);const{connection:$,...j}=Xh(v,y,x);let A=!0;c.setState=(z,P,L)=>{const U=s(z,P);if(!A)return U;const q=L===void 0?{type:g||Kh(new Error().stack)||"anonymous"}:typeof L=="string"?{type:L}:L;return v===void 0?($?.send(q,u()),U):($?.send({...q,type:`${v}/${q.type}`},{..._r(x.name),[v]:c.getState()}),U)},c.devtools={cleanup:()=>{$&&typeof $.unsubscribe=="function"&&$.unsubscribe(),Qh(x.name,v)}};const O=(...z)=>{const P=A;A=!1,s(...z),A=P},B=i(c.setState,u,c);if(j.type==="untracked"?$?.init(B):(j.stores[j.store]=c,$?.init(Object.fromEntries(Object.entries(j.stores).map(([z,P])=>[z,z===j.store?B:P.getState()])))),c.dispatchFromDevtools&&typeof c.dispatch=="function"){let z=!1;const P=c.dispatch;c.dispatch=(...L)=>{(xf?"staging":void 0)!=="production"&&L[0].type==="__setState"&&!z&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),z=!0),P(...L)}}return $.subscribe(z=>{var P;switch(z.type){case"ACTION":if(typeof z.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return yu(z.payload,L=>{if(L.type==="__setState"){if(v===void 0){O(L.state);return}Object.keys(L.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const U=L.state[v];if(U==null)return;JSON.stringify(c.getState())!==JSON.stringify(U)&&O(U);return}c.dispatchFromDevtools&&typeof c.dispatch=="function"&&c.dispatch(L)});case"DISPATCH":switch(z.payload.type){case"RESET":return O(B),v===void 0?$?.init(c.getState()):$?.init(_r(x.name));case"COMMIT":if(v===void 0){$?.init(c.getState());return}return $?.init(_r(x.name));case"ROLLBACK":return yu(z.state,L=>{if(v===void 0){O(L),$?.init(c.getState());return}O(L[v]),$?.init(_r(x.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return yu(z.state,L=>{if(v===void 0){O(L);return}JSON.stringify(c.getState())!==JSON.stringify(L[v])&&O(L[v])});case"IMPORT_STATE":{const{nextLiftedState:L}=z.payload,U=(P=L.computedStates.slice(-1)[0])==null?void 0:P.state;if(!U)return;O(v===void 0?U:U[v]),$?.send(null,L);return}case"PAUSE_RECORDING":return A=!A}return}}),B},Bu=Jh,yu=(i,r)=>{let s;try{s=JSON.parse(i)}catch(u){console.error("[zustand devtools middleware] Could not parse the received json",u)}s!==void 0&&r(s)};function Wh(i,r){let s;try{s=i()}catch{return}return{getItem:c=>{var m;const g=x=>x===null?null:JSON.parse(x,void 0),v=(m=s.getItem(c))!=null?m:null;return v instanceof Promise?v.then(g):g(v)},setItem:(c,m)=>s.setItem(c,JSON.stringify(m,void 0)),removeItem:c=>s.removeItem(c)}}const Au=i=>r=>{try{const s=i(r);return s instanceof Promise?s:{then(u){return Au(u)(s)},catch(u){return this}}}catch(s){return{then(u){return this},catch(u){return Au(u)(s)}}}},e4=(i,r)=>(s,u,c)=>{let m={storage:Wh(()=>localStorage),partialize:z=>z,version:0,merge:(z,P)=>({...P,...z}),...r},g=!1;const v=new Set,x=new Set;let y=m.storage;if(!y)return i((...z)=>{console.warn(`[zustand persist middleware] Unable to update item '${m.name}', the given storage is currently unavailable.`),s(...z)},u,c);const $=()=>{const z=m.partialize({...u()});return y.setItem(m.name,{state:z,version:m.version})},j=c.setState;c.setState=(z,P)=>{j(z,P),$()};const A=i((...z)=>{s(...z),$()},u,c);c.getInitialState=()=>A;let O;const B=()=>{var z,P;if(!y)return;g=!1,v.forEach(U=>{var q;return U((q=u())!=null?q:A)});const L=((P=m.onRehydrateStorage)==null?void 0:P.call(m,(z=u())!=null?z:A))||void 0;return Au(y.getItem.bind(y))(m.name).then(U=>{if(U)if(typeof U.version=="number"&&U.version!==m.version){if(m.migrate){const q=m.migrate(U.state,U.version);return q instanceof Promise?q.then(fe=>[!0,fe]):[!0,q]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,U.state];return[!1,void 0]}).then(U=>{var q;const[fe,ie]=U;if(O=m.merge(ie,(q=u())!=null?q:A),s(O,!0),fe)return $()}).then(()=>{L?.(O,void 0),O=u(),g=!0,x.forEach(U=>U(O))}).catch(U=>{L?.(void 0,U)})};return c.persist={setOptions:z=>{m={...m,...z},z.storage&&(y=z.storage)},clearStorage:()=>{y?.removeItem(m.name)},getOptions:()=>m,rehydrate:()=>B(),hasHydrated:()=>g,onHydrate:z=>(v.add(z),()=>{v.delete(z)}),onFinishHydration:z=>(x.add(z),()=>{x.delete(z)})},m.skipHydration||B(),O||A},Hu=e4,{slice:t4,forEach:a4}=[];function l4(i){return a4.call(t4.call(arguments,1),r=>{if(r)for(const s in r)i[s]===void 0&&(i[s]=r[s])}),i}function n4(i){return typeof i!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(s=>s.test(i))}const bf=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,i4=function(i,r){const u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},c=encodeURIComponent(r);let m=`${i}=${c}`;if(u.maxAge>0){const g=u.maxAge-0;if(Number.isNaN(g))throw new Error("maxAge should be a Number");m+=`; Max-Age=${Math.floor(g)}`}if(u.domain){if(!bf.test(u.domain))throw new TypeError("option domain is invalid");m+=`; Domain=${u.domain}`}if(u.path){if(!bf.test(u.path))throw new TypeError("option path is invalid");m+=`; Path=${u.path}`}if(u.expires){if(typeof u.expires.toUTCString!="function")throw new TypeError("option expires is invalid");m+=`; Expires=${u.expires.toUTCString()}`}if(u.httpOnly&&(m+="; HttpOnly"),u.secure&&(m+="; Secure"),u.sameSite)switch(typeof u.sameSite=="string"?u.sameSite.toLowerCase():u.sameSite){case!0:m+="; SameSite=Strict";break;case"lax":m+="; SameSite=Lax";break;case"strict":m+="; SameSite=Strict";break;case"none":m+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return u.partitioned&&(m+="; Partitioned"),m},Sf={create(i,r,s,u){let c=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};s&&(c.expires=new Date,c.expires.setTime(c.expires.getTime()+s*60*1e3)),u&&(c.domain=u),document.cookie=i4(i,r,c)},read(i){const r=`${i}=`,s=document.cookie.split(";");for(let u=0;u<s.length;u++){let c=s[u];for(;c.charAt(0)===" ";)c=c.substring(1,c.length);if(c.indexOf(r)===0)return c.substring(r.length,c.length)}return null},remove(i,r){this.create(i,"",-1,r)}};var r4={name:"cookie",lookup(i){let{lookupCookie:r}=i;if(r&&typeof document<"u")return Sf.read(r)||void 0},cacheUserLanguage(i,r){let{lookupCookie:s,cookieMinutes:u,cookieDomain:c,cookieOptions:m}=r;s&&typeof document<"u"&&Sf.create(s,i,u,c,m)}},s4={name:"querystring",lookup(i){let{lookupQuerystring:r}=i,s;if(typeof window<"u"){let{search:u}=window.location;!window.location.search&&window.location.hash?.indexOf("?")>-1&&(u=window.location.hash.substring(window.location.hash.indexOf("?")));const m=u.substring(1).split("&");for(let g=0;g<m.length;g++){const v=m[g].indexOf("=");v>0&&m[g].substring(0,v)===r&&(s=m[g].substring(v+1))}}return s}},d4={name:"hash",lookup(i){let{lookupHash:r,lookupFromHashIndex:s}=i,u;if(typeof window<"u"){const{hash:c}=window.location;if(c&&c.length>2){const m=c.substring(1);if(r){const g=m.split("&");for(let v=0;v<g.length;v++){const x=g[v].indexOf("=");x>0&&g[v].substring(0,x)===r&&(u=g[v].substring(x+1))}}if(u)return u;if(!u&&s>-1){const g=c.match(/\/([a-zA-Z-]*)/g);return Array.isArray(g)?g[typeof s=="number"?s:0]?.replace("/",""):void 0}}}return u}};let Pl=null;const Nf=()=>{if(Pl!==null)return Pl;try{if(Pl=typeof window<"u"&&window.localStorage!==null,!Pl)return!1;const i="i18next.translate.boo";window.localStorage.setItem(i,"foo"),window.localStorage.removeItem(i)}catch{Pl=!1}return Pl};var u4={name:"localStorage",lookup(i){let{lookupLocalStorage:r}=i;if(r&&Nf())return window.localStorage.getItem(r)||void 0},cacheUserLanguage(i,r){let{lookupLocalStorage:s}=r;s&&Nf()&&window.localStorage.setItem(s,i)}};let Yl=null;const Cf=()=>{if(Yl!==null)return Yl;try{if(Yl=typeof window<"u"&&window.sessionStorage!==null,!Yl)return!1;const i="i18next.translate.boo";window.sessionStorage.setItem(i,"foo"),window.sessionStorage.removeItem(i)}catch{Yl=!1}return Yl};var o4={name:"sessionStorage",lookup(i){let{lookupSessionStorage:r}=i;if(r&&Cf())return window.sessionStorage.getItem(r)||void 0},cacheUserLanguage(i,r){let{lookupSessionStorage:s}=r;s&&Cf()&&window.sessionStorage.setItem(s,i)}},c4={name:"navigator",lookup(i){const r=[];if(typeof navigator<"u"){const{languages:s,userLanguage:u,language:c}=navigator;if(s)for(let m=0;m<s.length;m++)r.push(s[m]);u&&r.push(u),c&&r.push(c)}return r.length>0?r:void 0}},f4={name:"htmlTag",lookup(i){let{htmlTag:r}=i,s;const u=r||(typeof document<"u"?document.documentElement:null);return u&&typeof u.getAttribute=="function"&&(s=u.getAttribute("lang")),s}},m4={name:"path",lookup(i){let{lookupFromPathIndex:r}=i;if(typeof window>"u")return;const s=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(s)?s[typeof r=="number"?r:0]?.replace("/",""):void 0}},h4={name:"subdomain",lookup(i){let{lookupFromSubdomainIndex:r}=i;const s=typeof r=="number"?r+1:1,u=typeof window<"u"&&window.location?.hostname?.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(u)return u[s]}};let S2=!1;try{document.cookie,S2=!0}catch{}const N2=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];S2||N2.splice(1,1);const g4=()=>({order:N2,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:i=>i});class C2{constructor(r){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(r,s)}init(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=r,this.options=l4(s,this.options||{},g4()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=c=>c.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=u,this.addDetector(r4),this.addDetector(s4),this.addDetector(u4),this.addDetector(o4),this.addDetector(c4),this.addDetector(f4),this.addDetector(m4),this.addDetector(h4),this.addDetector(d4)}addDetector(r){return this.detectors[r.name]=r,this}detect(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,s=[];return r.forEach(u=>{if(this.detectors[u]){let c=this.detectors[u].lookup(this.options);c&&typeof c=="string"&&(c=[c]),c&&(s=s.concat(c))}}),s=s.filter(u=>u!=null&&!n4(u)).map(u=>this.options.convertDetectedLanguage(u)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?s:s.length>0?s[0]:null}cacheUserLanguage(r){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;s&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(r)>-1||s.forEach(u=>{this.detectors[u]&&this.detectors[u].cacheUserLanguage(r,this.options)}))}}C2.type="languageDetector";const p4={appName:"樂趣",back:"返回",profile:"个人中心",slogan:"专业的音频资源交易平台",cropAvatar:"裁切头像",cancel:"取消",save:"保存",confirm:"确认",close:"关闭",alias:"别名",firstName:"名",lastName:"姓",email:"邮箱",address:"地址",phone:"电话号码",countryOrRegion:"国家/地区",state:"省/州",city:"城市",postalZipCode:"邮政编码",china:"中国",stageName:"艺名",name:"姓名",artistBio:"艺人简介",changePassword:"修改密码",saveSuccess:"保存成功",saveFailed:"保存失败",emailCode:"验证码",verificationCode:"验证码",resendCode:"重新发送",sendCode:"发送验证码",emailCodeRequired:"请输入邮箱验证码",verify:"验证",defaultRole:"默认角色",investor:"投资人",artist:"音乐人",sending:"发送中...",navigation:{home:"首页",about:"关于",profile:"个人中心",settings:"设置",login:"登录",register:"注册",logout:"退出登录",help:"帮助",aboutUs:"关于我们",musicMarket:"音乐市场",myAssets:"我的资产",myBalance:"我的余额",myOrders:"我的订单",submitMusic:"提交音乐"},menu:{musicMarket:"音乐市场",myAssets:"我的资产",myBalance:"我的余额",myOrders:"我的订单",submitMusic:"提交音乐"},buttons:{browse:"浏览音频",become:"成为创作者",view:"查看收藏",backHome:"返回首页",confirm:"确认",cancel:"取消",save:"保存",edit:"编辑",delete:"删除",upload:"上传",download:"下载",continue:"继续",next:"下一步",sendVerificationCode:"发送验证码"},form:{username:"用户名",email:"邮箱",password:"密码",confirmPassword:"确认密码",required:"必填项",invalidEmail:"邮箱格式不正确",passwordMismatch:"密码不匹配",requiredMsg:"{{label}} 是必填项",emailRequired:"请输入邮箱",currentPasswordRequired:"请输入当前密码",currentPasswordPlaceholder:"请输入当前密码",emailInvalid:"请输入有效的邮箱",phonePlaceholder:"请输入手机号码",phoneInvalid:"请输入有效的手机号码",countryRegionPlaceholder:"请选择国家和地区",selectCountry:"选择国家和地区",selectState:"选择省/州",statePlaceholder:"请输入省/州",enterVerificationCode:"请输入验证码"},messages:{loading:"加载中...",success:"操作成功",error:"操作失败",retry:"重试",networkError:"网络错误",serverError:"服务器错误",verificationCodeSent:"验证码已发送",emailUnavailable:"邮箱不可用，已经被注册",emailSameAsOld:"新邮箱不能与原邮箱重复",sendVerificationCodeFailed:"发送验证码失败",verificationSuccess:"验证成功",verificationCodeInvalid:"验证码无效或已过期",loadCountriesFailed:"加载国家列表失败",loadSubdivisionsFailed:"加载州省列表失败",loadRolesFailed:"加载角色列表失败",unauthorized:"未授权访问",forbidden:"权限不足",notFound:"资源不存在",unknownError:"未知错误",loginExpired:"登录已过期，请重新登录。"},footer:{copyright:"樂趣 ©{{year}} Created with ❤️"}},$4={login:{title:"用户登录",subtitle:"登录您的账号以访问更多功能",form:{username:"用户名",password:"密码",remember:"记住我",submit:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账号？",register:"立即注册",loginWithPassword:"使用密码登录",loginWithEmailCode:"使用邮箱验证码登录",sendCode:"发送验证码",resend:"重新发送",emailCodeLabel:"请输入验证码",sending:"发送中..."},loginAs:"登录为",changeAccount:"更换账号",codeSentTo:"验证码已发送到",success:"登录成功",error:"登录失败，请检查用户名和密码",otpSent:"验证码已发送",otpSendFailed:"验证码发送失败，请重试"},register:{title:"注册樂趣",subtitle:"创建您的账号以开始使用樂趣",success:"注册成功",error:"注册失败，请重试",steps:{email:"邮箱验证",alias:"设置别名",personal:"个人信息",password:"设置密码"},step1:{title:"邮箱验证",subtitle:"请输入您的邮箱进行验证",hasAccount:"已有账号？",loginHere:"立即登录",form:{email:"邮箱"},buttons:{next:"下一步"},messages:{emailAlreadyExists:"该邮箱已被注册，请使用其他邮箱或直接登录"}},step2:{title:"设置别名",subtitle:"为您的账号设置一个独特的别名",form:{alias:"别名",aliasPlaceholder:"请输入别名",aliasRequired:"请输入别名",aliasMinLength:"别名需不少于{{min}}个字符，仅限字母或数字，不能包含特殊字符",aliasMaxLength:"别名最多{{max}}个字符",aliasPattern:"别名只能包含字母、数字",aliasNoLeadingTrailingSpaces:"别名不能以空格开头或结尾"},messages:{aliasUnavailable:"别名不可用",aliasSuggestions:"别名已被占用，建议使用：{{suggestions}}",aliasCheckFailed:"检查别名可用性失败"},buttons:{prev:"上一步",next:"下一步"}},step3:{title:"个人信息",form:{firstName:"名",lastName:"姓",phone:"电话号码",address:"地址",country:"国家/地区",state:"省/州",city:"城市",postalZipCode:"邮政编码",cityPlaceholder:"请输入城市",firstNamePlaceholder:"请输入名",lastNamePlaceholder:"请输入姓",phonePlaceholder:"请输入电话号码",addressPlaceholder:"请输入地址",countryPlaceholder:"请输入国家/地区",statePlaceholder:"请输入省/州",postalZipCodePlaceholder:"请输入邮政编码",firstNameRequired:"请输入您的名",lastNameRequired:"请输入您的姓"},validation:{nameLength:"姓名长度必须在 {{min}} 到 {{max}} 个字符之间",namePattern:"姓名只能包含中英文、空格和常用符号"},buttons:{prev:"上一步",next:"下一步"}},step4:{title:"设置密码",subtitle:"为您的账号设置安全密码",form:{password:"密码",confirmPassword:"确认密码",passwordPlaceholder:"请输入密码",confirmPasswordPlaceholder:"请再次输入密码",passwordRequired:"请输入密码",confirmPasswordRequired:"请确认您的密码",passwordMismatch:"两次输入的密码不匹配",passwordRequirements:{title:"密码要求",length:"至少8个字符并包含",uppercase:"一个大写字母",lowercase:"一个小写字母",number:"一个数字",special:"一个特殊字符 (!@#$ ... )"},agreeTerms:"我同意",termsOfService:"服务条款",and:"和",privacyPolicy:"隐私政策",agreeTermsRequired:"请同意服务条款和隐私政策",signUp:"注册"},buttons:{prev:"上一步",finish:"完成注册"},messages:{emailVerificationRequired:"请先完成邮箱验证",aliasRequired:"请先设置别名",personalInfoRequired:"请先完善个人信息"}},form:{username:"用户名",email:"邮箱",password:"密码",confirmPassword:"确认密码",agreeTerms:"我已阅读并同意服务条款",submit:"注册",hasAccount:"已有账号？",login:"立即登录"}},logout:{success:"已成功退出登录",confirm:"确定要退出登录吗？"},profile:{title:"个人资料",info:"基本信息",security:"安全设置",preferences:"偏好设置"},permission:{denied:"您没有权限访问此页面",login:"请先登录",redirect:"正在跳转到登录页面..."}},v4={title:"欢迎来到樂趣",subtitle:"发现和购买高质量的音频资源",sections:{audioResources:{title:"音频资源",description:"浏览和购买各种音频文件，包括音乐、音效、配音等。",button:"浏览音频"},creatorCenter:{title:"创作者中心",description:"上传和销售您的音频作品，成为平台创作者。",button:"成为创作者"},myCollection:{title:"我的收藏",description:"管理您购买的音频资源和收藏的内容。",button:"查看收藏"}}},y4={title:"关于樂趣",subtitle:"专业的音频资源交易平台",features:{richResources:{title:"丰富资源",description:"提供各种类型的高质量音频资源，满足不同需求。"},creatorSupport:{title:"创作者支持",description:"为音频创作者提供完善的上传、管理和销售工具。"},security:{title:"安全保障",description:"确保所有交易安全，保护用户权益和知识产权。"}},mission:{title:"我们的使命",description1:"樂趣致力于构建一个开放、公平、安全的音频资源交易平台。我们相信每个创作者都应该得到应有的回报，每个用户都应该能够轻松找到所需的音频资源。",description2:"通过先进的技术和用户友好的界面，我们连接创作者与消费者，促进音频创作生态的健康发展。"}},x4={uploadSuccess:"上传成功",uploadFailed:"上传失败",loadFailed:"加载失败",sendFailed:"发送失败",currentPassword:"当前密码",loading:"加载中...",retry:"重试",close:"关闭",noData:"暂无数据",uploadImageFailed:"只能上传图片文件！",uploadImageSizeFailed:"图片大小不能超过 5MB！"},b4={404:{title:"404",subtitle:"抱歉，您访问的页面不存在。",backHome:"返回首页",goBack:"返回上页"},500:{title:"500",subtitle:"服务器内部错误，请稍后重试。",backHome:"返回首页"},network:{title:"网络错误",subtitle:"网络连接失败，请检查您的网络设置。",retry:"重试"}},S4={title:"音乐市场",searchPlaceholder:"搜索音乐...",filters:{genreFilter:"类型筛选",releasedBetween:"发布时间",sharesAvailableOnly:"仅显示可购买股份",allGenres:"所有类型"},table:{title:"标题",genre:"类型",revenue:"收入",streams:"播放量"},genres:{pop:"流行",jazz:"爵士",instrumental:"器乐",electronic:"电子",acoustic:"原声"}},N4={common:p4,auth:$4,home:v4,about:y4,messages:x4,error:b4,musicMarket:S4},C4={appName:"Yuequ",back:"Back",profile:"Profile",slogan:"Professional Audio Resource Trading Platform",cropAvatar:"Crop Avatar",cancel:"Cancel",save:"Save",confirm:"Confirm",close:"Close",alias:"Alias",firstName:"First Name",lastName:"Last Name",email:"Email",address:"Address",phone:"Phone Number",countryOrRegion:"Country/Region",state:"State/Province",city:"City",postalZipCode:"Postal Code",china:"China",stageName:"Stage Name",name:"Name",artistBio:"Artist Bio",changePassword:"Change Password",saveSuccess:"Save Success",saveFailed:"Save Failed",emailCode:"Code",verificationCode:"Verification Code",resendCode:"Resend Code",sendCode:"Send Code",emailCodeRequired:"Please enter the email verification code",verify:"Verify",defaultRole:"Default Role",artist:"Artist",investor:"Investor",navigation:{home:"Home",about:"About",profile:"Profile",settings:"Settings",login:"Login",register:"Register",logout:"Logout",help:"Help",aboutUs:"About Us",musicMarket:"Music Marketplace",myAssets:"My Assets",myBalance:"My Balance",myOrders:"My Orders",submitMusic:"Submit Music"},menu:{musicMarket:"Music Marketplace",myAssets:"My Assets",myBalance:"My Balance",myOrders:"My Orders",submitMusic:"Submit Music"},buttons:{browse:"Browse Audio",become:"Become Creator",view:"View Collection",backHome:"Back to Home",confirm:"Confirm",cancel:"Cancel",save:"Save",edit:"Edit",delete:"Delete",upload:"Upload",download:"Download",continue:"Continue",next:"Next",send:"Send",sendVerificationCode:"Send Verification Code"},form:{username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",required:"Required",invalidEmail:"Invalid email format",passwordMismatch:"Passwords do not match",requiredMsg:"{{label}} is required",emailRequired:"Please enter email",currentPasswordRequired:"Please enter current password",currentPasswordPlaceholder:"Please enter current password",phonePlaceholder:"Enter phone number",phoneInvalid:"Please enter a valid phone number",countryRegionPlaceholder:"Select country and region",selectCountry:"Choose a country/region",selectState:"Choose a state/province",statePlaceholder:"Enter state/province",enterVerificationCode:"Please enter the verification code"},messages:{loading:"Loading...",success:"Operation successful",error:"Operation failed",retry:"Retry",networkError:"Network error",serverError:"Server error",verificationCodeSent:"Verification code sent",emailUnavailable:"Email unavailable, already registered",emailSameAsOld:"New email cannot be the same as the old email",sendVerificationCodeFailed:"Failed to send verification code",verificationSuccess:"Verification successful",verificationCodeInvalid:"Invalid or expired verification code",loadCountriesFailed:"Failed to load countries",loadSubdivisionsFailed:"Failed to load states/provinces",loadRolesFailed:"Failed to load roles",unauthorized:"Unauthorized access",forbidden:"Access forbidden",notFound:"Resource not found",unknownError:"Unknown error",loginExpired:"Login expired, please login again."},footer:{copyright:"Yuequ ©{{year}} Created with ❤️"}},w4={login:{title:"User Login",subtitle:"Sign in to your account to access more features",form:{username:"Username",password:"Password",remember:"Remember me",submit:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",register:"Register now",loginWithPassword:"Login with password",loginWithEmailCode:"Login with Email Verification Code",resend:"Resend",emailCodeLabel:"Please enter the verification code",sending:"Sending...",sendCode:"Send"},loginAs:"Login as",changeAccount:"Change Account",codeSentTo:"Code sent to",success:"Login successful",error:"Login failed, please check your username and password",otpSent:"Verification code sent",otpSendFailed:"Failed to send verification code, please try again"},register:{title:"Signup to Yuequ",subtitle:"Create your account to start using Yuequ",success:"Registration successful",error:"Registration failed, please try again",steps:{email:"Email Verification",alias:"Set Alias",personal:"Personal Info",password:"Set Password"},step1:{title:"Email Verification",subtitle:"Please enter your email address for verification",hasAccount:"Already have an account?",loginHere:"Login here",form:{email:"Email Address",emailRequired:"Please enter your email",emailInvalid:"Please enter a valid email address"},buttons:{next:"Next"},messages:{emailAlreadyExists:"This email is already registered, please use another email or login directly"}},step2:{title:"Set Alias",subtitle:"This name will appear on your profile",form:{alias:"Alias",aliasPlaceholder:"Enter your alias",aliasRequired:"Please enter an alias",aliasMaxLength:"Alias cannot exceed {{max}} characters",aliasPattern:"Alias can only contain letters and numbers",aliasMinLength:"Please enter at least {{min}} characters, which can be numbers or letters. Special characters are not allowed.",aliasNoLeadingTrailingSpaces:"Alias cannot start or end with a space"},messages:{aliasUnavailable:"Alias unavailable",aliasSuggestions:"Alias already taken, suggestions: {{suggestions}}",aliasCheckFailed:"Failed to check alias availability"},buttons:{prev:"Previous",next:"Next"}},step3:{title:"Personal Information",form:{firstName:"First Name",lastName:"Last Name",phone:"Phone Number",address:"Address",city:"City",country:"Country/Region",state:"State/Province",postalZipCode:"Postal Code",cityPlaceholder:"Enter your city",firstNamePlaceholder:"Enter your first name",lastNamePlaceholder:"Enter your last name",phonePlaceholder:"Enter your phone number",addressPlaceholder:"Enter your address",countryPlaceholder:"Enter your country",statePlaceholder:"Enter your state/province",postalZipCodePlaceholder:"Enter your postal code",firstNameRequired:"Please enter your first name",lastNameRequired:"Please enter your last name"},validation:{nameLength:"Name length must be between {{min}} and {{max}} characters",namePattern:"Name can only contain English, Chinese, spaces, and common symbols"},buttons:{prev:"Previous",next:"Next"}},step4:{title:"Set Password",subtitle:"Set a secure password for your account",form:{password:"Password",confirmPassword:"Confirm Password",passwordPlaceholder:"Enter your password",confirmPasswordPlaceholder:"Enter your password again",passwordRequired:"Please enter your password",confirmPasswordRequired:"Please confirm your password",passwordMismatch:"The passwords don't match",passwordRequirements:{title:"Password Requirements",length:"At least 8 characters and include",uppercase:"One uppercase letter",lowercase:"One lowercase letter",number:"One number",special:"One special character (!@#$ ... )"},agreeTerms:"I agree to the",termsOfService:"Terms of Service",and:"and",privacyPolicy:"Privacy Policy",agreeTermsRequired:"Please agree to the Terms of Service and Privacy Policy",signUp:"Sign Up"},buttons:{prev:"Previous",finish:"Complete Registration"},messages:{emailVerificationRequired:"Please complete email verification first",aliasRequired:"Please set an alias first",personalInfoRequired:"Please complete personal information first"}},form:{username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",agreeTerms:"I have read and agree to the terms of service",submit:"Register",hasAccount:"Already have an account?",login:"Login now"}},logout:{success:"Successfully logged out",confirm:"Are you sure you want to log out?"},profile:{title:"User Profile",info:"Basic Information",security:"Security Settings",preferences:"Preferences"},permission:{denied:"You don't have permission to access this page",login:"Please login first",redirect:"Redirecting to login page..."}},E4={title:"Welcome to Yuequ",subtitle:"Discover and purchase high-quality audio resources",sections:{audioResources:{title:"Audio Resources",description:"Browse and purchase various audio files including music, sound effects, voiceovers, and more.",button:"Browse Audio"},creatorCenter:{title:"Creator Center",description:"Upload and sell your audio works to become a platform creator.",button:"Become Creator"},myCollection:{title:"My Collection",description:"Manage your purchased audio resources and collected content.",button:"View Collection"}}},j4={title:"About Yuequ",subtitle:"Professional Audio Resource Trading Platform",features:{richResources:{title:"Rich Resources",description:"Provides various types of high-quality audio resources to meet different needs."},creatorSupport:{title:"Creator Support",description:"Provides comprehensive upload, management and sales tools for audio creators."},security:{title:"Security Guarantee",description:"Ensures all transactions are secure and protects user rights and intellectual property."}},mission:{title:"Our Mission",description1:"Yuequ is committed to building an open, fair, and secure audio resource trading platform. We believe that every creator should be rewarded accordingly, and every user should be able to easily find the audio resources they need.",description2:"Through advanced technology and user-friendly interfaces, we connect creators with consumers to promote the healthy development of the audio creation ecosystem."}},A4={uploadSuccess:"Upload successful",uploadFailed:"Upload failed",loadFailed:"Load failed",sendFailed:"Send failed",currentPassword:"Current Password",loading:"Loading...",retry:"Retry",close:"Close",noData:"No data available",uploadImageFailed:"Only image files can be uploaded!",uploadImageSizeFailed:"Image size cannot exceed 5MB!"},T4={404:{title:"404",subtitle:"Sorry, the page you visited does not exist.",backHome:"Back to Home",goBack:"Go Back"},500:{title:"500",subtitle:"Internal server error, please try again later.",backHome:"Back to Home"},network:{title:"Network Error",subtitle:"Network connection failed, please check your network settings.",retry:"Retry"}},O4={title:"Music Marketplace",searchPlaceholder:"Search music...",filters:{genreFilter:"Genre Filter",releasedBetween:"Released Between",sharesAvailableOnly:"Shares Available Only",allGenres:"All Genres"},table:{title:"Title",genre:"Genre",revenue:"Revenue",streams:"Streams"},genres:{pop:"Pop",jazz:"Jazz",instrumental:"Instrumental Music",electronic:"Electronic",acoustic:"Acoustic"}},R4={common:C4,auth:w4,home:E4,about:j4,messages:A4,error:T4,musicMarket:O4},Tu=[{code:"zh",name:"中文"},{code:"en",name:"English"}],_4={zh:{translation:N4},en:{translation:R4}};Ce.use(C2).use(oh).init({resources:_4,fallbackLng:"zh",interpolation:{escapeValue:!1},detection:{order:["localStorage","sessionStorage","navigator","htmlTag"],caches:["localStorage","sessionStorage"],lookupLocalStorage:"language",lookupSessionStorage:"language"}});const M4=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,w2=i=>{const r=`${i}`.split("~");return{phone:+r[0],phoneCountry:r[1]||"CN"}},D4=i=>{if(!i)return"";let s="";switch(i.countryCode){case"CN":{const u=[i.stateProvince,i.addressLine2,i.addressLine1],c=[...new Set(u.filter(Boolean))];s=[[Ce.t("common.china"),...c].join(""),i.postalZipCode].filter(Boolean).join(" ");break}default:s=[i.addressLine1,i.addressLine2,i.stateProvince,i.countryCode].filter(Boolean).join(", "),s=s+(i.postalZipCode?` ${i.postalZipCode}`:"");break}return s.length>60?`${s.slice(0,60)}...`:s};function U4(){const i=Ce.language;return i.startsWith("zh-TW")?i:i.startsWith("zh")?"zh":"en"}function wf(){Ht.getState().logout()}class z4{requestQueue=new Set;loadingText=Ce.t("common.messages.loading");listeners=new Set;subscribe(r){return this.listeners.add(r),()=>{this.listeners.delete(r)}}notify(){const r={isLoading:this.requestQueue.size>0,loadingText:this.loadingText,requestCount:this.requestQueue.size};this.listeners.forEach(s=>s(r))}addRequest(r,s=!0){s&&(this.requestQueue.add(r),this.notify())}removeRequest(r){this.requestQueue.delete(r),this.notify()}setLoadingText(r){this.loadingText=r,this.requestQueue.size>0&&this.notify()}showLoading(r){r&&this.setLoadingText(r),this.addRequest("manual-loading")}hideLoading(){this.removeRequest("manual-loading")}getState(){return{isLoading:this.requestQueue.size>0,loadingText:this.loadingText,requestCount:this.requestQueue.size}}}const Ua=new z4,L4=()=>`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,B4="https://musicapi.renee-arts.com/api/v1",tl=ch.create({baseURL:B4,timeout:1e4,headers:{"Accept-Language":U4(),"X-Timezone":M4(),"X-Realm":1,common:{"Content-Type":"application/json"}}});tl.interceptors.request.use(i=>{const r=L4();i.metadata={requestId:r};const s=localStorage.getItem("token");s&&(i.headers.Authorization=`Bearer ${s}`);const u=i.showLoading!==!1;return i.loadingText&&Ua.setLoadingText(i.loadingText),Ua.addRequest(r,u),i},i=>(console.error("Request Error:",i),Promise.reject(i)));tl.interceptors.response.use(i=>{const r=i.config.metadata?.requestId;return r&&Ua.removeRequest(r),console.log("response----",i),[400,401].includes(i.data.code)&&(Z.error(Ce.t("common.messages.loginExpired")),wf()),i},i=>{const r=i.config?.metadata?.requestId;if(r&&Ua.removeRequest(r),console.error("Response Error:",{message:i.message,status:i.response?.status,url:i.config?.url}),i.response)switch(i.response.status){case 401:Z.error(Ce.t("common.messages.loginExpired")),wf();break;case 403:Z.error(Ce.t("common.messages.forbidden"));break;case 404:Z.error(Ce.t("common.messages.notFound"));break;case 500:Z.error(Ce.t("common.messages.serverError"));break;default:Z.error(Ce.t("common.messages.unknownError"))}else i.request&&Z.error(Ce.t("common.messages.networkError"));return Promise.reject(i)});class Be{static async get(r,s){return(await tl.get(r,s)).data}static async post(r,s,u){return(await tl.post(r,s,u)).data}static async put(r,s,u){return(await tl.put(r,s,u)).data}static async delete(r,s){return(await tl.delete(r,s)).data}static async patch(r,s,u){return(await tl.patch(r,s,u)).data}static showLoading=r=>Ua.showLoading(r);static hideLoading=()=>Ua.hideLoading()}const Lr={INVESTOR:"account.role.investor",ARTIST:"account.role.artist"},He={AUTH:{SIGNUP:"/auth/signup",LOGIN:"/auth/login",LOGIN_OTP:"/auth/login/otp",SEND_SIGNUP_OTP:"/auth/otp/signup",SEND_LOGIN_OTP:"/auth/otp/login",SEND_CHANGE_USERNAME_OTP:"/auth/otp/change-username",VERIFY_OTP:"/auth/otp/verify",CHECK_USERNAME:"/auth/check-username",CHECK_ALIAS:"/auth/check-alias"},USER:{PROFILE:"/member/profile",CHANGE_PASSWORD:"/member/profile/password"},META:{COUNTRIES:"/meta/countries",COUNTRY_BY_CODE:"/meta/countries",SUBDIVISIONS:"/meta/countries",SUBDIVISION_BY_CODE:"/meta/countries",DEFAULT_ROLES:"/meta/default/member-roles",DEFAULT_ROLE_BY_ID:"/meta/default/member-roles",AVATAR_PRESIGNED_URL:"/meta/avatar/presigned-url"}},H4={async signup(i){return Be.post(He.AUTH.SIGNUP,i)},async login(i){return Be.post(He.AUTH.LOGIN,i)},async loginWithOtp(i){return Be.post(He.AUTH.LOGIN_OTP,i)},async sendSignupOtp(i){return Be.post(He.AUTH.SEND_SIGNUP_OTP,i)},async sendLoginOtp(i){return Be.post(He.AUTH.SEND_LOGIN_OTP,i)},async sendChangeUsernameOtp(i){return Be.post(He.AUTH.SEND_CHANGE_USERNAME_OTP,i)},async verifyOtp(i){return Be.put(He.AUTH.VERIFY_OTP,i)},async checkUsername(i){return Be.get(`${He.AUTH.CHECK_USERNAME}?username=${encodeURIComponent(i)}`)},async checkAlias(i){return Be.get(`${He.AUTH.CHECK_ALIAS}?alias=${encodeURIComponent(i)}`)}},q4={async getProfile(){return Be.get(He.USER.PROFILE)},async updateProfile(i){return Be.put(He.USER.PROFILE,i)},async changePassword(i){return Be.put(He.USER.CHANGE_PASSWORD,i)},async getAvatarPresignedUrl(i){return Be.get(`${He.META.AVATAR_PRESIGNED_URL}?filename=${encodeURIComponent(i)}`)},async uploadAvatarToS3(i,r){const s=await fetch(i,{method:"PUT",body:r,headers:{"Content-Type":r.type}});if(!s.ok)throw new Error(`上传失败: ${s.status} ${s.statusText}`)},async uploadAvatar(i){const r=await this.getAvatarPresignedUrl(i.name);if(r.code!==200)throw new Error(r.message||"获取上传URL失败");const{presignedUrl:s}=r.body;return await this.uploadAvatarToS3(s,i),{url:s.split("?")[0]}},async sendChangeEmailOtp(i){return Be.post(He.AUTH.SEND_CHANGE_USERNAME_OTP,i)}},G4={async getCountries(){return Be.get(He.META.COUNTRIES)},async getCountryByCode(i){return Be.get(`${He.META.COUNTRY_BY_CODE}/${i}`)},async getSubdivisions(i){return Be.get(`${He.META.SUBDIVISIONS}/${i}/subdivisions`)},async getSubdivisionByCode(i,r){return Be.get(`${He.META.SUBDIVISION_BY_CODE}/${i}/subdivisions/${r}`)},async getDefaultRoles(){return Be.get(He.META.DEFAULT_ROLES)},async getDefaultRoleById(i){return Be.get(`${He.META.DEFAULT_ROLE_BY_ID}/${i}`)}},P4={isSupportedCountryForSubdivisions(i){return["US","CA","CN"].includes(i.toUpperCase())},formatCountryDisplayName(i){return`${i.name} (${i.code})`},formatSubdivisionDisplayName(i){return`${i.name} (${i.code})`},getRoleDisplayName(i){return{"account.role.investor":"Investor","account.role.artist":"Artist"}[i]||i}},Ae={auth:H4,user:q4,meta:G4},Y4=i=>!i||!i.roles||i.roles.length===0?null:i.roles[0].id,E2=(i,r)=>!i||!i.roles?!1:i.roles.some(s=>s.id===r),Z4=i=>{switch(Y4(i)){case Lr.ARTIST:return"/submit-music";case Lr.INVESTOR:return"/music-market";default:return"/music-market"}},k4=i=>E2(i,Lr.ARTIST),F4=i=>E2(i,Lr.INVESTOR),Ht=Lu()(Bu(Hu((i,r)=>({user:null,profile:null,token:null,isAuthenticated:!1,profileLoading:!1,profileError:null,get isArtist(){return k4(r()?.user)},get isInvestor(){return F4(r()?.user)},login:async s=>{try{let u;if(s.verificationCode?u=await Ae.auth.loginWithOtp({username:s.username,verificationCode:s.verificationCode}):u=await Ae.auth.login({username:s.username,password:s.password}),u.code===200){const c=u.body,m={accountId:c.accountId,email:"",alias:c.alias,firstName:"",lastName:"",displayName:c.displayName,avatarUrl:c.avatarUrl||null,stageName:c.stageName,roles:c.roles};i({token:c.token,isAuthenticated:!0,user:m}),localStorage.setItem("token",c.token);try{await r().fetchUserProfile(),console.log("获取完整用户数据成功")}catch(g){console.error("获取完整用户信息失败，但登录成功:",g)}}else{let c="登录失败";switch(u.code){case 400:c="请求参数错误，请检查用户名和密码格式";break;case 401:c="用户名或密码错误";break;case 403:c="账户被禁用，请联系管理员";break;case 404:c="用户不存在";break;case 500:c="服务器错误，请稍后重试";break;default:c=u.message||"登录失败，请重试"}throw new Error(c)}}catch(u){throw console.error("登录失败----",u),!u.message||u.message.includes("fetch")?new Error("网络连接失败，请检查网络设置"):u}},setAuthData:(s,u)=>{i({token:s,user:u,isAuthenticated:!0}),localStorage.setItem("token",s)},fetchUserInfo:()=>{try{console.log("fetchUserInfo: 不再需要获取权限信息")}catch(s){throw console.error("获取用户信息失败:",s),s}},fetchUserProfile:async()=>{try{i({profileLoading:!0,profileError:null});const s=await Ae.user.getProfile();if(s.code===200&&s.body){const u=s.body,c=r();if(c.user){const m={...c.user,email:u.email,firstName:u.firstName,lastName:u.lastName,displayName:u.displayName,avatarUrl:u.avatarUrl,stageName:u.stageName,alias:u.alias,mobile:u.mobile,addressLine1:u.addressLine1,addressLine2:u.addressLine2,stateProvince:u.stateProvince,countryCode:u.countryCode,postalZipCode:u.postalZipCode,bio:u.bio};i({profile:u,user:m,profileLoading:!1,profileError:null})}else i({profile:u,profileLoading:!1,profileError:null})}else{const u=s.message||"加载用户资料失败";throw i({profileError:u,profileLoading:!1}),new Error(u)}}catch(s){console.error("获取用户资料失败:",s);const u=s instanceof Error?s.message:"加载用户资料失败";throw i({profileError:u,profileLoading:!1}),s}},updateProfileData:s=>{const u=r();if(u.user){const c={...u.user,email:s.email,firstName:s.firstName,lastName:s.lastName,displayName:s.displayName,avatarUrl:s.avatarUrl,stageName:s.stageName,alias:s.alias,mobile:s.mobile,addressLine1:s.addressLine1,addressLine2:s.addressLine2,stateProvince:s.stateProvince,countryCode:s.countryCode,postalZipCode:s.postalZipCode,bio:s.bio};i({profile:s,user:c})}else i({profile:s})},initializeAuth:()=>{const s=localStorage.getItem("token");s?(i({token:s,isAuthenticated:!0}),r().fetchUserProfile().catch(u=>{console.error("初始化时获取用户信息失败:",u),r().logout()})):r().logout()},logout:()=>{i({user:null,profile:null,token:null,isAuthenticated:!1,profileLoading:!1,profileError:null}),localStorage.removeItem("token")}}),{name:"auth-store"}),{name:"AuthStore"})),qu=Lu()(Bu(Hu((i,r)=>({theme:"light",language:"zh",sidebarCollapsed:!1,loading:!1,error:null,setTheme:s=>{i({theme:s}),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${s}`)},setLanguage:s=>{i({language:s}),Ce.changeLanguage(s)},toggleSidebar:()=>{i(s=>({sidebarCollapsed:!s.sidebarCollapsed}))},setSidebarCollapsed:s=>{i({sidebarCollapsed:s})},setLoading:s=>{i({loading:s})},setError:s=>{i({error:s})},clearError:()=>{i({error:null})}}),{name:"app-store",onRehydrateStorage:()=>i=>{i?.theme&&(document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${i.theme}`))}}),{name:"AppStore"})),Se=()=>{const{i18n:i,t:r}=s2(),{language:s,setLanguage:u}=qu();return{language:s,isZhCN:s==="zh",isEnUS:s==="en",t:r,i18n:i,changeLanguage:x=>{u(x)},getCurrentLanguageName:()=>Tu.find(x=>x.code===s)?.name}},{Option:s8}=la,{RangePicker:V4}=Il,I4=[{id:1,title:"Summer Vibes",artist:"DJ Alex",genre:"Pop",revenue:"22,345.60",streams:"1.2M",cover:wt,releaseDate:"2024-01-15",sharesAvailable:!0},{id:2,title:"Jazz Night",artist:"Sarah Johnson",genre:"Jazz",revenue:"9,345.30",streams:"856K",cover:wt,releaseDate:"2024-02-20",sharesAvailable:!1},{id:3,title:"Peaceful Mind",artist:"Meditation Masters",genre:"Instrumental Music",revenue:"8,345.61",streams:"743K",cover:wt,releaseDate:"2024-01-10",sharesAvailable:!0},{id:4,title:"City Lights",artist:"Urban Beats",genre:"Pop",revenue:"8,041.70",streams:"692K",cover:wt,releaseDate:"2024-03-05",sharesAvailable:!0},{id:5,title:"Smooth Jazz",artist:"The Jazz Collective",genre:"Jazz",revenue:"7,441.23",streams:"634K",cover:wt,releaseDate:"2024-02-14",sharesAvailable:!1},{id:6,title:"Electronic Dreams",artist:"Synth Wave",genre:"Pop",revenue:"5,441.58",streams:"521K",cover:wt,releaseDate:"2024-01-28",sharesAvailable:!0},{id:7,title:"Acoustic Soul",artist:"Guitar Hero",genre:"Jazz",revenue:"5,449.36",streams:"498K",cover:wt,releaseDate:"2024-03-12",sharesAvailable:!1},{id:8,title:"Morning Coffee",artist:"Cafe Sounds",genre:"Pop",revenue:"6,442.14",streams:"567K",cover:wt,releaseDate:"2024-02-08",sharesAvailable:!0}],X4=()=>{const{t:i}=Se(),[r,s]=G.useState(""),[u,c]=G.useState(""),[m,g]=G.useState(null),[v,x]=G.useState(!1),y=I4.filter($=>{const j=$.title.toLowerCase().includes(r.toLowerCase())||$.artist.toLowerCase().includes(r.toLowerCase()),A=!u||$.genre===u,O=!v||$.sharesAvailable;return j&&A&&O});return o.jsxs("div",{className:"p-8",children:[o.jsxs("div",{className:"mb-8 space-y-4",children:[o.jsx("div",{className:"flex items-center space-x-4",children:o.jsx("div",{className:"relative flex-1 max-w-md",children:o.jsx(se,{placeholder:i("musicMarket.searchPlaceholder"),value:r,onChange:$=>s($.target.value),className:"h-12 pl-4 pr-12 bg-[#1a1a1a] border-[#333] text-white placeholder:text-[#666] rounded-lg",prefix:o.jsx(d2,{className:"text-[#666] mr-2"})})})}),o.jsxs("div",{className:"flex items-center space-x-6",children:[o.jsx("div",{className:"flex items-center space-x-2"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:"text-[#999] text-14px",children:"Released Between:"}),o.jsx(V4,{value:m,onChange:g,className:"bg-[#1a1a1a] border-[#333]"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:"text-[#999] text-14px",children:"Shares Available Only:"}),o.jsx(u2,{checked:v,onChange:x,size:"small"})]})]})]}),o.jsx("div",{className:"grid grid-cols-4 gap-4 mb-12",children:y.map($=>o.jsxs("div",{className:"bg-transparent rounded-lg overflow-hidden hover:opacity-80 transition-opacity cursor-pointer",children:[o.jsx("div",{className:"aspect-square bg-[#333] relative rounded-lg overflow-hidden",children:o.jsx("img",{src:$.cover,alt:$.title,className:"w-full h-full object-cover",onError:j=>{j.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgZmlsbD0iIzY2NiIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIj5NdXNpYyBDb3ZlcjwvdGV4dD4KPHN2Zz4="}})}),o.jsxs("div",{className:"pt-3",children:[o.jsx("h3",{className:"text-white font-medium text-14px mb-1 truncate",children:$.title}),o.jsx("p",{className:"text-[#999] text-12px truncate",children:$.artist})]})]},$.id))}),o.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg overflow-hidden",children:[o.jsxs("div",{className:"grid grid-cols-4 gap-4 p-4 border-b border-[#333] bg-[#222]",children:[o.jsx("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:o.jsx("span",{children:i("musicMarket.table.title")})}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.genre")}),o.jsx(Xl,{className:"ml-1 text-10px"})]}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.revenue")}),o.jsx(Xl,{className:"ml-1 text-10px"})]}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.streams")}),o.jsx(Xl,{className:"ml-1 text-10px"})]})]}),o.jsx("div",{className:"divide-y divide-[#333]",children:y.map($=>o.jsxs("div",{className:"grid grid-cols-4 gap-4 p-4 hover:bg-[#222] transition-colors",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-12 h-12 bg-[#333] rounded mr-3 flex-shrink-0",children:o.jsx("img",{src:$.cover,alt:$.title,className:"w-full h-full object-cover rounded",onError:j=>{j.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4="}})}),o.jsxs("div",{children:[o.jsx("div",{className:"text-white text-14px font-medium",children:$.title}),o.jsx("div",{className:"text-[#999] text-12px",children:$.artist})]})]}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.genre}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.revenue}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.streams})]},$.id))})]})]})},{TextArea:Q4}=se,{Option:ta}=la,K4=()=>{Se();const[i]=X.useForm(),[r,s]=G.useState(!1),u=async g=>{s(!0);try{console.log("提交的数据:",g)}catch(v){console.error("提交失败:",v)}finally{s(!1)}},c=()=>{i.resetFields()},m=()=>!1;return o.jsx("div",{className:"min-h-screen bg-[#0d0d0d] relative",children:o.jsx("div",{className:"pl-8 pr-8 pt-6",children:o.jsxs(X,{form:i,layout:"vertical",onFinish:u,className:"max-w-none",children:[o.jsxs("div",{className:"mb-10",children:[o.jsx("h2",{className:"text-white text-[22px] font-bold mb-6 font-arial",children:"Artist Info"}),o.jsxs("div",{className:"mb-6",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Artist (Stage Name):"}),o.jsx("div",{className:"w-[854px]",children:o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4",placeholder:"value25",style:{borderRadius:0}})})]}),o.jsxs("div",{className:"mb-6",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Artist Bio (0 to 400 characters):"}),o.jsx(Q4,{className:"bg-[#282828] border-none text-[#999999] text-[14px] leading-[27px] px-4 py-3",style:{borderRadius:0,height:"139px",resize:"none",width:"1462px"},placeholder:'Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.',maxLength:400})]})]}),o.jsxs("div",{className:"mb-10",children:[o.jsx("h2",{className:"text-white text-[22px] font-bold mb-8 font-arial",children:"Track Info"}),o.jsxs("div",{className:"flex gap-8 mb-8",children:[o.jsx("div",{className:"w-[340px] h-[340px]",children:o.jsx("div",{className:"!w-[340px] !h-[340px] flex flex-col items-center justify-center cursor-pointer relative",style:{background:'linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzQwIiBoZWlnaHQ9IjM0MCIgdmlld0JveD0iMCAwIDM0MCAzNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzNDAiIGhlaWdodD0iMzQwIiBmaWxsPSIjMjgyODI4Ii8+Cjwvc3ZnPgo=")',borderRadius:"6px"},children:o.jsx(wu,{name:"coverArt",listType:"picture-card",className:"cover-art-uploader",beforeUpload:m,showUploadList:!1,children:o.jsxs("div",{className:"flex flex-col items-center justify-center",children:[o.jsx(Eu,{className:"text-[64px] text-[#656565] mb-4"}),o.jsx("div",{className:"text-[#656565] text-[14px] font-medium text-center w-[175px]",children:"CoverArt+Upload"})]})})})}),o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex gap-4 mb-4",children:[o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"text-[#656565] text-[14px] mb-2",children:"Primary Artist (Stage Name):"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4",placeholder:"Super Freaky Girl",style:{borderRadius:0}})]}),o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"text-[#656565] text-[14px] mb-2",children:"Label Name:"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] text-[14px] px-4",placeholder:"Super Freaky Girl",style:{borderRadius:0}})]})]}),o.jsxs("div",{className:"mb-4",children:[o.jsx("div",{className:"text-[#656565] text-[14px] mb-2",children:"Artist Bio (0 to 400 characters):"}),o.jsx("div",{className:"bg-[#999999] p-4 text-[#000000] text-[14px] leading-normal",style:{height:"139px"},children:'Koji Takanashi, born on April 13, 1963 in Tokyo, Japan, is a Japanese composer, arranger, keyboard player, and a member of the Japanese hard rock band "Musashi". He is currently mainly engaged in the production of animation music. The classic tracks in a series of well-known animations and TV works such as "Naruto", "Pretty Cure", "Fairy Tail", "Hell Girl", "Super Star God", and "Log Horizon" are all from his hand.'})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx(Eu,{className:"text-[24px] text-[#656565] mr-2"}),o.jsx("span",{className:"text-[#656565] text-[14px] font-medium",children:"Upload"})]})]})]}),o.jsxs("div",{className:"flex gap-8 mb-6",children:[o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Artist (Stage Name):"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] px-5",style:{borderRadius:0}})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Language:"}),o.jsxs(la,{className:"w-full h-[42px] custom-select",placeholder:"Select language",style:{borderRadius:0},suffixIcon:o.jsx("div",{className:"w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"}),children:[o.jsx(ta,{value:"en",children:"English"}),o.jsx(ta,{value:"zh",children:"中文"}),o.jsx(ta,{value:"ja",children:"日本語"})]})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Artist (Stage Name):"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] px-5",style:{borderRadius:0}})]})]}),o.jsxs("div",{className:"flex gap-8 mb-8",children:[o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Language:"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] px-5",style:{borderRadius:0}})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Language:"}),o.jsxs(la,{className:"w-full h-[42px] custom-select",placeholder:"Select language",style:{borderRadius:0},suffixIcon:o.jsx("div",{className:"w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"}),children:[o.jsx(ta,{value:"en",children:"English"}),o.jsx(ta,{value:"zh",children:"中文"}),o.jsx(ta,{value:"ja",children:"日本語"})]})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Primary Language:"}),o.jsxs(la,{className:"w-full h-[42px] custom-select",placeholder:"Select language",style:{borderRadius:0},suffixIcon:o.jsx("div",{className:"w-[9.78px] h-[9.78px] bg-[#656565] rotate-45 transform border border-[#656565]"}),children:[o.jsx(ta,{value:"en",children:"English"}),o.jsx(ta,{value:"zh",children:"中文"}),o.jsx(ta,{value:"ja",children:"日本語"})]})]})]}),o.jsxs("div",{className:"mb-8",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Bids Between:"}),o.jsxs("div",{className:"flex items-center gap-8",children:[o.jsxs("div",{className:"relative w-[454px]",children:[o.jsx(Il,{className:"w-full h-[42px] bg-[#282828] border-none custom-datepicker",placeholder:"25/11/2022",style:{borderRadius:"8px"},suffixIcon:o.jsx(ru,{className:"text-[18px] text-[#656565]"})}),o.jsx(Rr,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]"})]}),o.jsxs("div",{className:"relative w-[454px]",children:[o.jsx(Il,{className:"w-full h-[42px] bg-[#282828] border-none custom-datepicker",placeholder:"25/11/2022",style:{borderRadius:"8px"}}),o.jsx(Rr,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]"})]})]})]})]}),o.jsxs("div",{className:"mb-10",children:[o.jsx("h2",{className:"text-white text-[22px] font-bold mb-8 font-arial",children:"Copyright Info"}),o.jsxs("div",{className:"flex gap-8 mb-6",children:[o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Year:"}),o.jsxs("div",{className:"relative",children:[o.jsx(Il,{className:"w-full h-[42px] bg-[#282828] border-none custom-datepicker",placeholder:"25/11/2022",style:{borderRadius:"8px"},suffixIcon:o.jsx(ru,{className:"text-[18px] text-[#656565]"})}),o.jsx(Rr,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]"})]})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Name"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] px-5",style:{borderRadius:0}})]})]}),o.jsxs("div",{className:"flex gap-8 mb-8",children:[o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Year:"}),o.jsxs("div",{className:"relative",children:[o.jsx(Il,{className:"w-full h-[42px] bg-[#282828] border-none custom-datepicker",placeholder:"25/11/2022",style:{borderRadius:"8px"},suffixIcon:o.jsx(ru,{className:"text-[18px] text-[#656565]"})}),o.jsx(Rr,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-[16px] text-[#656565]"})]})]}),o.jsxs("div",{className:"w-[454px]",children:[o.jsx("div",{className:"text-[#656565] text-[18px] font-bold mb-5 font-arial",children:"Name"}),o.jsx(se,{className:"h-[42px] bg-[#282828] border-none text-[#999999] px-5",style:{borderRadius:0}})]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center",children:o.jsx("div",{className:"w-[7px] h-[7px] bg-transparent"})}),o.jsx("span",{className:"text-[#656565] text-[18px] font-bold font-arial",children:"Supports Dolby Atmos"})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center",children:o.jsx("div",{className:"w-[7px] h-[7px] bg-transparent"})}),o.jsx("span",{className:"text-[#656565] text-[18px] font-bold font-arial",children:"Supports Dolby Atmos"})]})]})]}),o.jsxs("div",{className:"flex gap-16 mb-10",children:[o.jsx("div",{className:"flex-1",children:o.jsx("h2",{className:"text-white text-[22px] font-bold mb-8 font-arial",children:"Copyright Info"})}),o.jsxs("div",{className:"flex-1",children:[o.jsx("h2",{className:"text-white text-[22px] font-bold mb-8 font-arial",children:"Release Options (optional):"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center",children:o.jsx("div",{className:"w-[7px] h-[7px] bg-transparent"})}),o.jsx("span",{className:"text-[#656565] text-[18px] font-bold font-arial",children:"Enable iTunes Pre-Order"})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center",children:o.jsx("div",{className:"w-[7px] h-[7px] bg-transparent"})}),o.jsx("span",{className:"text-[#656565] text-[18px] font-bold font-arial",children:"Live Concert Recording"})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-[15px] h-[15px] border border-[#656565] mr-4 flex items-center justify-center",children:o.jsx("div",{className:"w-[7px] h-[7px] bg-transparent"})}),o.jsx("span",{className:"text-[#656565] text-[18px] font-bold font-arial",children:"Remastered Recording"})]})]})]})]}),o.jsxs("div",{className:"mb-16",children:[o.jsx("h2",{className:"text-white text-[22px] font-bold mb-8 font-arial",children:"Additional Media Files (optional)"}),o.jsx(wu,{name:"additionalFiles",multiple:!0,beforeUpload:m,className:"additional-files-uploader",children:o.jsxs("div",{className:"w-[218px] h-[218px] bg-[#282828] flex flex-col items-center justify-center cursor-pointer",children:[o.jsxs("div",{className:"w-16 h-16 relative mb-4",children:[o.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:o.jsx("div",{className:"w-16 h-1 bg-[#999999] rounded-[11px]"})}),o.jsx("div",{className:"absolute inset-0 flex items-center justify-center rotate-90",children:o.jsx("div",{className:"w-16 h-1 bg-[#999999] rounded-[11px]"})})]}),o.jsx("div",{className:"text-[#656565] text-[14px] font-medium",children:"Upload"})]})})]}),o.jsxs("div",{className:"flex justify-center gap-6 mb-60px",children:[o.jsx(ht,{size:"large",className:"w-[496px] h-[63px] bg-transparent text-[#ff5e13] text-[16px] font-bold border border-[#ff5e13] hover:bg-[#ff5e13] hover:text-black transition-colors",style:{borderRadius:"6px"},onClick:c,children:"Cancel"}),o.jsx(ht,{type:"primary",size:"large",htmlType:"submit",loading:r,className:"w-[496px] h-[63px] bg-[#ff5e13] text-black text-[16px] font-bold border-none hover:bg-[#e5541a] transition-colors",style:{borderRadius:"6px"},children:"Confirm"})]})]})})})},{Option:d8}=la,{RangePicker:J4}=Il,W4=[{id:1,title:"Summer Vibes",artist:"DJ Alex",genre:"Pop",revenue:"22,345.60",streams:"1.2M",cover:wt,releaseDate:"2024-01-15",sharesAvailable:!0},{id:2,title:"Jazz Night",artist:"Sarah Johnson",genre:"Jazz",revenue:"9,345.30",streams:"856K",cover:wt,releaseDate:"2024-02-20",sharesAvailable:!1},{id:3,title:"Peaceful Mind",artist:"Meditation Masters",genre:"Instrumental Music",revenue:"8,345.61",streams:"743K",cover:wt,releaseDate:"2024-01-10",sharesAvailable:!0},{id:4,title:"City Lights",artist:"Urban Beats",genre:"Pop",revenue:"8,041.70",streams:"692K",cover:wt,releaseDate:"2024-03-05",sharesAvailable:!0}],e9=()=>{const{t:i}=Se(),[r,s]=G.useState(""),[u,c]=G.useState(""),[m,g]=G.useState(null),[v,x]=G.useState(!1),y=W4.filter($=>{const j=$.title.toLowerCase().includes(r.toLowerCase())||$.artist.toLowerCase().includes(r.toLowerCase()),A=!u||$.genre===u,O=!v||$.sharesAvailable;return j&&A&&O});return o.jsxs("div",{className:"p-8",children:[o.jsxs("div",{className:"mb-8 space-y-4",children:[o.jsx("div",{className:"flex items-center space-x-4",children:o.jsx("div",{className:"relative flex-1 max-w-md",children:o.jsx(se,{placeholder:i("musicMarket.searchPlaceholder"),value:r,onChange:$=>s($.target.value),className:"h-12 pl-4 pr-12 bg-[#1a1a1a] border-[#333] text-white placeholder:text-[#666] rounded-lg",prefix:o.jsx(d2,{className:"text-[#666] mr-2"})})})}),o.jsxs("div",{className:"flex items-center space-x-6",children:[o.jsx("div",{className:"flex items-center space-x-2"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:"text-[#999] text-14px",children:"Released Between:"}),o.jsx(J4,{value:m,onChange:g,className:"bg-[#1a1a1a] border-[#333]"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:"text-[#999] text-14px",children:"Shares Available Only:"}),o.jsx(u2,{checked:v,onChange:x,size:"small"})]})]})]}),o.jsx("div",{className:"grid grid-cols-4 gap-4 mb-12",children:y.map($=>o.jsxs("div",{className:"bg-transparent rounded-lg overflow-hidden hover:opacity-80 transition-opacity cursor-pointer",children:[o.jsx("div",{className:"aspect-square bg-[#333] relative rounded-lg overflow-hidden",children:o.jsx("img",{src:$.cover,alt:$.title,className:"w-full h-full object-cover",onError:j=>{j.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgZmlsbD0iIzY2NiIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIj5NdXNpYyBDb3ZlcjwvdGV4dD4KPHN2Zz4="}})}),o.jsxs("div",{className:"pt-3",children:[o.jsx("h3",{className:"text-white font-medium text-14px mb-1 truncate",children:$.title}),o.jsx("p",{className:"text-[#999] text-12px truncate",children:$.artist})]})]},$.id))}),o.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg overflow-hidden",children:[o.jsxs("div",{className:"grid grid-cols-4 gap-4 p-4 border-b border-[#333] bg-[#222]",children:[o.jsx("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:o.jsx("span",{children:i("musicMarket.table.title")})}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.genre")}),o.jsx(Xl,{className:"ml-1 text-10px"})]}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.revenue")}),o.jsx(Xl,{className:"ml-1 text-10px"})]}),o.jsxs("div",{className:"flex items-center text-[#999] text-12px font-semibold",children:[o.jsx("span",{children:i("musicMarket.table.streams")}),o.jsx(Xl,{className:"ml-1 text-10px"})]})]}),o.jsx("div",{className:"divide-y divide-[#333]",children:y.map($=>o.jsxs("div",{className:"grid grid-cols-4 gap-4 p-4 hover:bg-[#222] transition-colors",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-12 h-12 bg-[#333] rounded mr-3 flex-shrink-0",children:o.jsx("img",{src:$.cover,alt:$.title,className:"w-full h-full object-cover rounded",onError:j=>{j.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4="}})}),o.jsxs("div",{children:[o.jsx("div",{className:"text-white text-14px font-medium",children:$.title}),o.jsx("div",{className:"text-[#999] text-12px",children:$.artist})]})]}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.genre}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.revenue}),o.jsx("div",{className:"flex items-center text-[#656565] text-14px",children:$.streams})]},$.id))})]})]})},Ef={"/music-market":()=>o.jsx(X4,{}),"/music-market/browse":()=>o.jsx("div",{children:"浏览音乐"}),"/music-market/purchase":()=>o.jsx("div",{children:"购买音乐"}),"/my-assets":()=>o.jsx(e9,{}),"/my-assets/library":()=>o.jsx("div",{children:"我的音乐库"}),"/my-assets/licenses":()=>o.jsx("div",{children:"我的许可证"}),"/my-balance":()=>o.jsx("div",{children:"我的余额"}),"/my-balance/overview":()=>o.jsx("div",{children:"余额概览"}),"/my-balance/transactions":()=>o.jsx("div",{children:"交易记录"}),"/my-orders":()=>o.jsx("div",{children:"我的订单"}),"/my-orders/list":()=>o.jsx("div",{children:"订单列表"}),"/my-orders/history":()=>o.jsx("div",{children:"订单历史"}),"/submit-music":()=>o.jsx(K4,{}),"/submit-music/upload":()=>o.jsx("div",{children:"上传音乐"}),"/submit-music/manage":()=>o.jsx("div",{children:"管理我的音乐"})},t9=({className:i})=>{const{language:r,setLanguage:s}=qu(),u=({key:g})=>{s(g)},c=Tu.find(g=>g.code===r),m=Tu.map(g=>({key:g.code,label:o.jsx("div",{className:"flex items-center",children:o.jsx("span",{children:g.name})})}));return o.jsx("div",{className:`${i} `,children:o.jsx(fh,{menu:{items:m,onClick:u},children:o.jsx("a",{className:"cursor-pointer",onClick:g=>g.preventDefault(),children:o.jsxs(mh,{style:{color:"var(--color-label)"},children:[c?.name,o.jsx(hh,{})]})})})})},Gu="/assets/logo-Bu4mna3N.svg",{Header:u8}=zr,Pu=({fixed:i=!0,onClick:r})=>{const s=ia(),{t:u}=Se(),{user:c,isAuthenticated:m}=Ht(),g=()=>{s("/login")};console.log("user",c);const v=()=>{s("/register")};return o.jsxs("header",{className:`flex items-center px-60px bg-page-bg justify-between shadow-sm h-120px  ${i?"fixed top-0 left-0 right-0 z-50":""} font-700 text-label text-16px `,children:[o.jsx("div",{className:"flex items-center cursor-pointer",onClick:()=>s("/"),children:o.jsx("img",{src:Gu,alt:"logo",className:"w-54px h-54px"})}),o.jsxs("div",{className:"flex items-center space-x-4",style:{fontSize:"16px",fontWeight:"700",color:"var(--color-label)"},children:[o.jsx("div",{className:"cursor-pointer hover:text-primary w-120px text-center",children:u("common.navigation.help")}),o.jsx("div",{className:"cursor-pointer hover:text-primary w-120px text-center",children:u("common.navigation.aboutUs")}),o.jsx(t9,{className:"w-120px  text-center"}),m?o.jsx(ju,{size:44,src:c?.avatarUrl,onClick:r,className:"cursor-pointer  select-none",children:c?.alias?.slice(0,1)}):o.jsxs("div",{className:"space-x-2 flex items-center border-l-2 border-l-label border-l-solid",children:[o.jsx("div",{className:"w-120px h-44px flex items-center justify-center cursor-pointer hover:text-primary",onClick:g,children:u("common.navigation.login")}),o.jsx(ht,{type:"primary",onClick:v,className:"w-120px h-44px text-16px font-700",children:u("common.navigation.register")})]})]})]})},a9=[{key:"music-market",name:Ce.t("common.menu.musicMarket"),url:"/music-market",icon:"music"},{key:"my-assets",name:Ce.t("common.menu.myAssets"),url:"/my-assets",icon:"folder"},{key:"my-balance",name:Ce.t("common.menu.myBalance"),url:"/my-balance",icon:"wallet"},{key:"my-orders",name:Ce.t("common.menu.myOrders"),url:"/my-orders",icon:"shopping"},{key:"submit-music",name:Ce.t("common.menu.submitMusic"),url:"/submit-music",icon:"upload"}],l9=()=>{const i=[],r=s=>{s.forEach(u=>{i.push(u.url),u.children&&r(u.children)})};return r(a9),i},n9="data:image/svg+xml,%3csvg%20width='13'%20height='9'%20viewBox='0%200%2013%209'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cmask%20id='mask0_135_1658'%20style='mask-type:luminance'%20maskUnits='userSpaceOnUse'%20x='0'%20y='0'%20width='14'%20height='9'%3e%3cpath%20d='M13.0001%200H0.00219727V9H13.0001V0Z'%20fill='white'/%3e%3c/mask%3e%3cg%20mask='url(%23mask0_135_1658)'%3e%3cpath%20d='M12.3344%200.00244141H12.3493C12.3676%200.00244141%2012.3861%200.00396704%2012.4044%200.00549267L12.3344%200.00244141C12.5708%200.0028991%2012.7889%200.116101%2012.9063%200.299329L12.8599%200.236778C12.9268%200.316569%2012.9697%200.41009%2012.9851%200.508646L12.9887%200.530462C12.9899%200.54221%2012.9908%200.553957%2012.9913%200.565552V4.49375C12.9913%204.80177%2012.7237%205.05716%2012.3792%205.07806C12.0347%205.09912%2011.7307%204.87851%2011.6837%204.57338L11.6775%204.49375V1.75997L6.89019%204.96257C6.68492%205.10018%206.40848%205.11849%206.18251%205.00941L6.10194%204.96257L1.31375%201.76073V7.81367H6.49607C6.82433%207.81367%207.10214%208.02985%207.14678%208.31987L7.15294%208.39951C7.15294%208.69259%206.91003%208.9405%206.5845%208.97986L6.49607%208.98536H0.656877C0.328609%208.98536%200.0508053%208.76917%200.00615822%208.47915L0%208.39951V0.575011C0%200.559297%200.00171062%200.545413%200.00342123%200.529699L0%200.588284C0.000855308%200.362643%200.146087%200.157293%200.373941%200.0595C0.43501%200.0332591%200.500355%200.015867%200.567411%200.00793368L0.586741%200.00549267C0.605387%200.00381447%200.624033%200.00274653%200.64285%200.00244141H12.3344ZM11.2659%205.32506L11.3395%205.38136L12.7995%206.68348C12.8214%206.70377%2012.8433%206.72483%2012.8616%206.74832L12.7995%206.68348C13.005%206.86763%2013.0505%207.15033%2012.9116%207.37871C12.8948%207.40572%2012.8758%207.43165%2012.8546%207.45606L12.7995%207.51236L11.3395%208.81372C11.0961%209.02914%2010.7079%209.04135%2010.4481%208.84179C10.1883%208.64224%2010.1443%208.29806%2010.3472%208.05152L10.4103%207.98591L10.7485%207.68353H8.24791C7.90253%207.68353%207.61618%207.44493%207.59274%207.13766C7.56913%206.8304%207.81649%206.5593%208.15861%206.51734L8.24791%206.51185H10.7493L10.4103%206.21038C10.1802%206.00503%2010.1534%205.68038%2010.3472%205.44726L10.4103%205.38166C10.6405%205.17647%2011.0046%205.15251%2011.2659%205.32537V5.32506ZM10.3629%201.17413H2.62665L6.49521%203.76114L10.3629%201.17413Z'%20fill='white'/%3e%3c/g%3e%3c/svg%3e",j2=({onCancel:i,onSave:r,htmlType:s="submit",className:u,loading:c=!1})=>{const{t:m}=Se();return o.jsxs("div",{className:`flex gap-12px ${u||""}`,children:[o.jsx(ht,{size:"small",type:"primary",ghost:!0,onClick:()=>{i()},className:"leading-24px rounded-2px !text-label !border-label text-12px hover:!text-label",children:m("common.cancel")}),o.jsx(ht,{size:"small",type:"primary",htmlType:s,loading:c,className:"text-white hover:!text-white leading-24px rounded-2px text-12px",onClick:()=>{r&&r()},children:m("common.save")})]})};var i9=Object.defineProperty,r9=(i,r,s)=>r in i?i9(i,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[r]=s,Ee=(i,r,s)=>r9(i,typeof r!="symbol"?r+"":r,s);const Br={x:0,y:0,width:0,height:0,unit:"px"},Zl=(i,r,s)=>Math.min(Math.max(i,r),s),s9=(...i)=>i.filter(r=>r&&typeof r=="string").join(" "),jf=(i,r)=>i===r||i.width===r.width&&i.height===r.height&&i.x===r.x&&i.y===r.y&&i.unit===r.unit;function kl(i,r,s){return i.unit==="%"?{...Br,...i,unit:"%"}:{unit:"%",x:i.x?i.x/r*100:0,y:i.y?i.y/s*100:0,width:i.width?i.width/r*100:0,height:i.height?i.height/s*100:0}}function _a(i,r,s){return i.unit?i.unit==="px"?{...Br,...i,unit:"px"}:{unit:"px",x:i.x?i.x*r/100:0,y:i.y?i.y*s/100:0,width:i.width?i.width*r/100:0,height:i.height?i.height*s/100:0}:{...Br,...i,unit:"px"}}function Af(i,r,s,u,c,m=0,g=0,v=u,x=c){const y={...i};let $=Math.min(m,u),j=Math.min(g,c),A=Math.min(v,u),O=Math.min(x,c);r&&(r>1?($=g?g*r:$,j=$/r,A=v*r):(j=m?m/r:j,$=j*r,O=x/r)),y.y<0&&(y.height=Math.max(y.height+y.y,j),y.y=0),y.x<0&&(y.width=Math.max(y.width+y.x,$),y.x=0);const B=u-(y.x+y.width);B<0&&(y.x=Math.min(y.x,u-$),y.width+=B);const z=c-(y.y+y.height);if(z<0&&(y.y=Math.min(y.y,c-j),y.height+=z),y.width<$&&((s==="sw"||s=="nw")&&(y.x-=$-y.width),y.width=$),y.height<j&&((s==="nw"||s=="ne")&&(y.y-=j-y.height),y.height=j),y.width>A&&((s==="sw"||s=="nw")&&(y.x-=A-y.width),y.width=A),y.height>O&&((s==="nw"||s=="ne")&&(y.y-=O-y.height),y.height=O),r){const P=y.width/y.height;if(P<r){const L=Math.max(y.width/r,j);(s==="nw"||s=="ne")&&(y.y-=L-y.height),y.height=L}else if(P>r){const L=Math.max(y.height*r,$);(s==="sw"||s=="nw")&&(y.x-=L-y.width),y.width=L}}return y}function d9(i,r,s,u){const c={...i};return r==="ArrowLeft"?u==="nw"?(c.x-=s,c.y-=s,c.width+=s,c.height+=s):u==="w"?(c.x-=s,c.width+=s):u==="sw"?(c.x-=s,c.width+=s,c.height+=s):u==="ne"?(c.y+=s,c.width-=s,c.height-=s):u==="e"?c.width-=s:u==="se"&&(c.width-=s,c.height-=s):r==="ArrowRight"&&(u==="nw"?(c.x+=s,c.y+=s,c.width-=s,c.height-=s):u==="w"?(c.x+=s,c.width-=s):u==="sw"?(c.x+=s,c.width-=s,c.height-=s):u==="ne"?(c.y-=s,c.width+=s,c.height+=s):u==="e"?c.width+=s:u==="se"&&(c.width+=s,c.height+=s)),r==="ArrowUp"?u==="nw"?(c.x-=s,c.y-=s,c.width+=s,c.height+=s):u==="n"?(c.y-=s,c.height+=s):u==="ne"?(c.y-=s,c.width+=s,c.height+=s):u==="sw"?(c.x+=s,c.width-=s,c.height-=s):u==="s"?c.height-=s:u==="se"&&(c.width-=s,c.height-=s):r==="ArrowDown"&&(u==="nw"?(c.x+=s,c.y+=s,c.width-=s,c.height-=s):u==="n"?(c.y+=s,c.height-=s):u==="ne"?(c.y+=s,c.width-=s,c.height-=s):u==="sw"?(c.x-=s,c.width+=s,c.height+=s):u==="s"?c.height+=s:u==="se"&&(c.width+=s,c.height+=s)),c}const Fl={capture:!0,passive:!1};let u9=0;const Ma=class Rt extends G.PureComponent{constructor(){super(...arguments),Ee(this,"docMoveBound",!1),Ee(this,"mouseDownOnCrop",!1),Ee(this,"dragStarted",!1),Ee(this,"evData",{startClientX:0,startClientY:0,startCropX:0,startCropY:0,clientX:0,clientY:0,isResize:!0}),Ee(this,"componentRef",G.createRef()),Ee(this,"mediaRef",G.createRef()),Ee(this,"resizeObserver"),Ee(this,"initChangeCalled",!1),Ee(this,"instanceId",`rc-${u9++}`),Ee(this,"state",{cropIsActive:!1,newCropIsBeingDrawn:!1}),Ee(this,"onCropPointerDown",r=>{const{crop:s,disabled:u}=this.props,c=this.getBox();if(!s)return;const m=_a(s,c.width,c.height);if(u)return;r.cancelable&&r.preventDefault(),this.bindDocMove(),this.componentRef.current.focus({preventScroll:!0});const g=r.target.dataset.ord,v=!!g;let x=r.clientX,y=r.clientY,$=m.x,j=m.y;if(g){const A=r.clientX-c.x,O=r.clientY-c.y;let B=0,z=0;g==="ne"||g=="e"?(B=A-(m.x+m.width),z=O-m.y,$=m.x,j=m.y+m.height):g==="se"||g==="s"?(B=A-(m.x+m.width),z=O-(m.y+m.height),$=m.x,j=m.y):g==="sw"||g=="w"?(B=A-m.x,z=O-(m.y+m.height),$=m.x+m.width,j=m.y):(g==="nw"||g=="n")&&(B=A-m.x,z=O-m.y,$=m.x+m.width,j=m.y+m.height),x=$+c.x+B,y=j+c.y+z}this.evData={startClientX:x,startClientY:y,startCropX:$,startCropY:j,clientX:r.clientX,clientY:r.clientY,isResize:v,ord:g},this.mouseDownOnCrop=!0,this.setState({cropIsActive:!0})}),Ee(this,"onComponentPointerDown",r=>{const{crop:s,disabled:u,locked:c,keepSelection:m,onChange:g}=this.props,v=this.getBox();if(u||c||m&&s)return;r.cancelable&&r.preventDefault(),this.bindDocMove(),this.componentRef.current.focus({preventScroll:!0});const x=r.clientX-v.x,y=r.clientY-v.y,$={unit:"px",x,y,width:0,height:0};this.evData={startClientX:r.clientX,startClientY:r.clientY,startCropX:x,startCropY:y,clientX:r.clientX,clientY:r.clientY,isResize:!0},this.mouseDownOnCrop=!0,g(_a($,v.width,v.height),kl($,v.width,v.height)),this.setState({cropIsActive:!0,newCropIsBeingDrawn:!0})}),Ee(this,"onDocPointerMove",r=>{const{crop:s,disabled:u,onChange:c,onDragStart:m}=this.props,g=this.getBox();if(u||!s||!this.mouseDownOnCrop)return;r.cancelable&&r.preventDefault(),this.dragStarted||(this.dragStarted=!0,m&&m(r));const{evData:v}=this;v.clientX=r.clientX,v.clientY=r.clientY;let x;v.isResize?x=this.resizeCrop():x=this.dragCrop(),jf(s,x)||c(_a(x,g.width,g.height),kl(x,g.width,g.height))}),Ee(this,"onComponentKeyDown",r=>{const{crop:s,disabled:u,onChange:c,onComplete:m}=this.props;if(u)return;const g=r.key;let v=!1;if(!s)return;const x=this.getBox(),y=this.makePixelCrop(x),$=(navigator.platform.match("Mac")?r.metaKey:r.ctrlKey)?Rt.nudgeStepLarge:r.shiftKey?Rt.nudgeStepMedium:Rt.nudgeStep;if(g==="ArrowLeft"?(y.x-=$,v=!0):g==="ArrowRight"?(y.x+=$,v=!0):g==="ArrowUp"?(y.y-=$,v=!0):g==="ArrowDown"&&(y.y+=$,v=!0),v){r.cancelable&&r.preventDefault(),y.x=Zl(y.x,0,x.width-y.width),y.y=Zl(y.y,0,x.height-y.height);const j=_a(y,x.width,x.height),A=kl(y,x.width,x.height);c(j,A),m&&m(j,A)}}),Ee(this,"onHandlerKeyDown",(r,s)=>{const{aspect:u=0,crop:c,disabled:m,minWidth:g=0,minHeight:v=0,maxWidth:x,maxHeight:y,onChange:$,onComplete:j}=this.props,A=this.getBox();if(m||!c)return;if(r.key==="ArrowUp"||r.key==="ArrowDown"||r.key==="ArrowLeft"||r.key==="ArrowRight")r.stopPropagation(),r.preventDefault();else return;const O=(navigator.platform.match("Mac")?r.metaKey:r.ctrlKey)?Rt.nudgeStepLarge:r.shiftKey?Rt.nudgeStepMedium:Rt.nudgeStep,B=_a(c,A.width,A.height),z=d9(B,r.key,O,s),P=Af(z,u,s,A.width,A.height,g,v,x,y);if(!jf(c,P)){const L=kl(P,A.width,A.height);$(P,L),j&&j(P,L)}}),Ee(this,"onDocPointerDone",r=>{const{crop:s,disabled:u,onComplete:c,onDragEnd:m}=this.props,g=this.getBox();this.unbindDocMove(),!(u||!s)&&this.mouseDownOnCrop&&(this.mouseDownOnCrop=!1,this.dragStarted=!1,m&&m(r),c&&c(_a(s,g.width,g.height),kl(s,g.width,g.height)),this.setState({cropIsActive:!1,newCropIsBeingDrawn:!1}))}),Ee(this,"onDragFocus",()=>{var r;(r=this.componentRef.current)==null||r.scrollTo(0,0)})}get document(){return document}getBox(){const r=this.mediaRef.current;if(!r)return{x:0,y:0,width:0,height:0};const{x:s,y:u,width:c,height:m}=r.getBoundingClientRect();return{x:s,y:u,width:c,height:m}}componentDidUpdate(r){const{crop:s,onComplete:u}=this.props;if(u&&!r.crop&&s){const{width:c,height:m}=this.getBox();c&&m&&u(_a(s,c,m),kl(s,c,m))}}componentWillUnmount(){this.resizeObserver&&this.resizeObserver.disconnect(),this.unbindDocMove()}bindDocMove(){this.docMoveBound||(this.document.addEventListener("pointermove",this.onDocPointerMove,Fl),this.document.addEventListener("pointerup",this.onDocPointerDone,Fl),this.document.addEventListener("pointercancel",this.onDocPointerDone,Fl),this.docMoveBound=!0)}unbindDocMove(){this.docMoveBound&&(this.document.removeEventListener("pointermove",this.onDocPointerMove,Fl),this.document.removeEventListener("pointerup",this.onDocPointerDone,Fl),this.document.removeEventListener("pointercancel",this.onDocPointerDone,Fl),this.docMoveBound=!1)}getCropStyle(){const{crop:r}=this.props;if(r)return{top:`${r.y}${r.unit}`,left:`${r.x}${r.unit}`,width:`${r.width}${r.unit}`,height:`${r.height}${r.unit}`}}dragCrop(){const{evData:r}=this,s=this.getBox(),u=this.makePixelCrop(s),c=r.clientX-r.startClientX,m=r.clientY-r.startClientY;return u.x=Zl(r.startCropX+c,0,s.width-u.width),u.y=Zl(r.startCropY+m,0,s.height-u.height),u}getPointRegion(r,s,u,c){const{evData:m}=this,g=m.clientX-r.x,v=m.clientY-r.y;let x;c&&s?x=s==="nw"||s==="n"||s==="ne":x=v<m.startCropY;let y;return u&&s?y=s==="nw"||s==="w"||s==="sw":y=g<m.startCropX,y?x?"nw":"sw":x?"ne":"se"}resolveMinDimensions(r,s,u=0,c=0){const m=Math.min(u,r.width),g=Math.min(c,r.height);return!s||!m&&!g?[m,g]:s>1?m?[m,m/s]:[g*s,g]:g?[g*s,g]:[m,m/s]}resizeCrop(){const{evData:r}=this,{aspect:s=0,maxWidth:u,maxHeight:c}=this.props,m=this.getBox(),[g,v]=this.resolveMinDimensions(m,s,this.props.minWidth,this.props.minHeight);let x=this.makePixelCrop(m);const y=this.getPointRegion(m,r.ord,g,v),$=r.ord||y;let j=r.clientX-r.startClientX,A=r.clientY-r.startClientY;(g&&$==="nw"||$==="w"||$==="sw")&&(j=Math.min(j,-g)),(v&&$==="nw"||$==="n"||$==="ne")&&(A=Math.min(A,-v));const O={unit:"px",x:0,y:0,width:0,height:0};y==="ne"?(O.x=r.startCropX,O.width=j,s?(O.height=O.width/s,O.y=r.startCropY-O.height):(O.height=Math.abs(A),O.y=r.startCropY-O.height)):y==="se"?(O.x=r.startCropX,O.y=r.startCropY,O.width=j,s?O.height=O.width/s:O.height=A):y==="sw"?(O.x=r.startCropX+j,O.y=r.startCropY,O.width=Math.abs(j),s?O.height=O.width/s:O.height=A):y==="nw"&&(O.x=r.startCropX+j,O.width=Math.abs(j),s?(O.height=O.width/s,O.y=r.startCropY-O.height):(O.height=Math.abs(A),O.y=r.startCropY+A));const B=Af(O,s,y,m.width,m.height,g,v,u,c);return s||Rt.xyOrds.indexOf($)>-1?x=B:Rt.xOrds.indexOf($)>-1?(x.x=B.x,x.width=B.width):Rt.yOrds.indexOf($)>-1&&(x.y=B.y,x.height=B.height),x.x=Zl(x.x,0,m.width-x.width),x.y=Zl(x.y,0,m.height-x.height),x}renderCropSelection(){const{ariaLabels:r=Rt.defaultProps.ariaLabels,disabled:s,locked:u,renderSelectionAddon:c,ruleOfThirds:m,crop:g}=this.props,v=this.getCropStyle();if(g)return ee.createElement("div",{style:v,className:"ReactCrop__crop-selection",onPointerDown:this.onCropPointerDown,"aria-label":r.cropArea,tabIndex:0,onKeyDown:this.onComponentKeyDown,role:"group"},!s&&!u&&ee.createElement("div",{className:"ReactCrop__drag-elements",onFocus:this.onDragFocus},ee.createElement("div",{className:"ReactCrop__drag-bar ord-n","data-ord":"n"}),ee.createElement("div",{className:"ReactCrop__drag-bar ord-e","data-ord":"e"}),ee.createElement("div",{className:"ReactCrop__drag-bar ord-s","data-ord":"s"}),ee.createElement("div",{className:"ReactCrop__drag-bar ord-w","data-ord":"w"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-nw","data-ord":"nw",tabIndex:0,"aria-label":r.nwDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"nw"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-n","data-ord":"n",tabIndex:0,"aria-label":r.nDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"n"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-ne","data-ord":"ne",tabIndex:0,"aria-label":r.neDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"ne"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-e","data-ord":"e",tabIndex:0,"aria-label":r.eDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"e"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-se","data-ord":"se",tabIndex:0,"aria-label":r.seDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"se"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-s","data-ord":"s",tabIndex:0,"aria-label":r.sDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"s"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-sw","data-ord":"sw",tabIndex:0,"aria-label":r.swDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"sw"),role:"button"}),ee.createElement("div",{className:"ReactCrop__drag-handle ord-w","data-ord":"w",tabIndex:0,"aria-label":r.wDragHandle,onKeyDown:x=>this.onHandlerKeyDown(x,"w"),role:"button"})),c&&ee.createElement("div",{className:"ReactCrop__selection-addon",onPointerDown:x=>x.stopPropagation()},c(this.state)),m&&ee.createElement(ee.Fragment,null,ee.createElement("div",{className:"ReactCrop__rule-of-thirds-hz"}),ee.createElement("div",{className:"ReactCrop__rule-of-thirds-vt"})))}makePixelCrop(r){const s={...Br,...this.props.crop||{}};return _a(s,r.width,r.height)}render(){const{aspect:r,children:s,circularCrop:u,className:c,crop:m,disabled:g,locked:v,style:x,ruleOfThirds:y}=this.props,{cropIsActive:$,newCropIsBeingDrawn:j}=this.state,A=m?this.renderCropSelection():null,O=s9("ReactCrop",c,$&&"ReactCrop--active",g&&"ReactCrop--disabled",v&&"ReactCrop--locked",j&&"ReactCrop--new-crop",m&&r&&"ReactCrop--fixed-aspect",m&&u&&"ReactCrop--circular-crop",m&&y&&"ReactCrop--rule-of-thirds",!this.dragStarted&&m&&!m.width&&!m.height&&"ReactCrop--invisible-crop",u&&"ReactCrop--no-animate");return ee.createElement("div",{ref:this.componentRef,className:O,style:x},ee.createElement("div",{ref:this.mediaRef,className:"ReactCrop__child-wrapper",onPointerDown:this.onComponentPointerDown},s),m?ee.createElement("svg",{className:"ReactCrop__crop-mask",width:"100%",height:"100%"},ee.createElement("defs",null,ee.createElement("mask",{id:`hole-${this.instanceId}`},ee.createElement("rect",{width:"100%",height:"100%",fill:"white"}),u?ee.createElement("ellipse",{cx:`${m.x+m.width/2}${m.unit}`,cy:`${m.y+m.height/2}${m.unit}`,rx:`${m.width/2}${m.unit}`,ry:`${m.height/2}${m.unit}`,fill:"black"}):ee.createElement("rect",{x:`${m.x}${m.unit}`,y:`${m.y}${m.unit}`,width:`${m.width}${m.unit}`,height:`${m.height}${m.unit}`,fill:"black"}))),ee.createElement("rect",{fill:"black",fillOpacity:.5,width:"100%",height:"100%",mask:`url(#hole-${this.instanceId})`})):void 0,A)}};Ee(Ma,"xOrds",["e","w"]),Ee(Ma,"yOrds",["n","s"]),Ee(Ma,"xyOrds",["nw","ne","se","sw"]),Ee(Ma,"nudgeStep",1),Ee(Ma,"nudgeStepMedium",10),Ee(Ma,"nudgeStepLarge",100),Ee(Ma,"defaultProps",{ariaLabels:{cropArea:"Use the arrow keys to move the crop selection area",nwDragHandle:"Use the arrow keys to move the north west drag handle to change the crop selection area",nDragHandle:"Use the up and down arrow keys to move the north drag handle to change the crop selection area",neDragHandle:"Use the arrow keys to move the north east drag handle to change the crop selection area",eDragHandle:"Use the up and down arrow keys to move the east drag handle to change the crop selection area",seDragHandle:"Use the arrow keys to move the south east drag handle to change the crop selection area",sDragHandle:"Use the up and down arrow keys to move the south drag handle to change the crop selection area",swDragHandle:"Use the arrow keys to move the south west drag handle to change the crop selection area",wDragHandle:"Use the up and down arrow keys to move the west drag handle to change the crop selection area"}});let o9=Ma;const c9=({avatarUrl:i,avatarSize:r=100,userName:s,onUploadSuccess:u,customRequest:c})=>{const{t:m}=Se(),[g,v]=G.useState(!1),[x,y]=G.useState(i||""),[$,j]=G.useState(!1),[A,O]=G.useState(""),[B,z]=G.useState({unit:"%",width:80,height:80,x:10,y:10}),[P,L]=G.useState(),U=G.useRef(null),q=G.useRef(null),fe=re=>{if(!re.type.startsWith("image/"))return Z.error(m("messages.uploadImageFailed")),!1;if(!(re.size/1024/1024<5))return Z.error(m("messages.uploadImageSizeFailed")),!1;q.current=re;const Te=new FileReader;return Te.onload=()=>{O(Te.result),j(!0)},Te.readAsDataURL(re),!1},ie=G.useCallback((re,K)=>{const oe=document.createElement("canvas"),Te=oe.getContext("2d"),Et=re.naturalWidth/re.width,Oe=re.naturalHeight/re.height;return oe.width=K.width,oe.height=K.height,Te.drawImage(re,K.x*Et,K.y*Oe,K.width*Et,K.height*Oe,0,0,K.width,K.height),new Promise(ra=>{oe.toBlob(We=>ra(We),"image/jpeg",.9)})},[]),$e=async()=>{if(!(!P||!U.current))try{v(!0);const re=await ie(U.current,P),K=new File([re],q.current?.name||"avatar.jpg",{type:"image/jpeg"});if(c)c({file:K,onSuccess:oe=>{const Te=oe?.url||URL.createObjectURL(re);y(Te),u?.(Te),v(!1),j(!1),Z.success(m("messages.uploadSuccess"))},onError:()=>{v(!1),Z.error(m("messages.uploadFailed"))}});else{const oe=URL.createObjectURL(re);y(oe),u?.(oe),v(!1),j(!1),Z.success(m("messages.uploadSuccess"))}}catch{Z.error(m("messages.uploadFailed")),v(!1)}};return o.jsxs(o.Fragment,{children:[o.jsx(wu,{name:"avatar",listType:"picture-card",showUploadList:!1,beforeUpload:fe,accept:"image/*",disabled:g,style:{width:r,height:r,border:"none",background:"transparent"},children:o.jsxs("div",{className:"relative inline-block",children:[x?o.jsx(ju,{size:r,src:x,className:"cursor-pointer hover:opacity-80"}):o.jsx(ju,{size:r,className:"cursor-pointer hover:opacity-80 ",children:s?s.charAt(0).toUpperCase():o.jsx(P1,{})}),o.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer",children:g?o.jsx(o2,{className:"text-white text-2xl"}):o.jsx(P1,{className:"text-white text-2xl"})})]})}),o.jsx(c2,{title:m("common.cropAvatar"),open:$,onOk:$e,onCancel:()=>j(!1),centered:!0,okText:m("common.confirm"),cancelText:m("common.cancel"),confirmLoading:g,children:A&&o.jsx(o9,{crop:B,onChange:(re,K)=>z(K),onComplete:L,aspect:1,circularCrop:!0,children:o.jsx("img",{ref:U,src:A,style:{maxHeight:"400px",maxWidth:"100%"},onLoad:()=>{if(U.current){const{width:re,height:K}=U.current,oe=Math.min(re,K)*.8,Te={unit:"px",width:oe,height:oe,x:(re-oe)/2,y:(K-oe)/2};z(Te),L(Te)}}})})})]})},Da=({text:i,children:r,className:s="",type:u="primary",htmlType:c="submit",size:m="large",block:g=!0,...v})=>{const{t:x}=Se(),y=`[&.ant-btn-variant-solid:disabled]:opacity-45  ${s}`;return o.jsx(ht,{type:u,htmlType:c,size:m,block:!0,className:y,...v,children:r||i||x("auth.register.step4.form.signUp")})},{Text:f9}=gh,m9=({password:i=""})=>{const{t:r}=Se(),s=[{key:"length",text:r("auth.register.step4.form.passwordRequirements.length"),check:u=>u.length>=8},{key:"uppercase",text:r("auth.register.step4.form.passwordRequirements.uppercase"),check:u=>/[A-Z]/.test(u)},{key:"lowercase",text:r("auth.register.step4.form.passwordRequirements.lowercase"),check:u=>/[a-z]/.test(u)},{key:"number",text:r("auth.register.step4.form.passwordRequirements.number"),check:u=>/[0-9]/.test(u)},{key:"special",text:r("auth.register.step4.form.passwordRequirements.special"),check:u=>/[!@#$%^&*()_+={}[\];':"\\|,.<>/?-]/.test(u)}];return o.jsx("div",{className:"mb-6",children:s.map(u=>o.jsxs("div",{className:"mb-2 flex items-center",children:[o.jsx("div",{className:`mr-3 h-2 w-2 flex-shrink-0 rounded-full ${i?u.check(i)?"bg-[#00CA47]":"bg-#F80000":"bg-label"}`}),o.jsx(f9,{className:`text-sm ${i?u.check(i)?"text-[#00CA47]":"text-#F80000":"text-label"}`,children:u.text})]},u.key))})},A2=({form:i,password:r="",onPasswordChange:s,showTermsCheckbox:u=!0,agreeTerms:c=!1,onAgreeTermsChange:m,showPasswordRequirements:g=!0,className:v="",passwordRules:x=[],confirmPasswordRules:y=[],inputClassName:$,labelClassName:j,passwordRequirementsClassName:A,showLabelColon:O=!0})=>{const{t:B}=Se(),z=q=>{const fe=q.target.value;s?.(fe)},P=q=>{m?.(q.target.checked)},L=[{required:!0,message:B("auth.register.step4.form.passwordRequired")},{min:8,message:B("auth.register.step4.form.passwordRequirements.length")},...x],U=[{required:!0,message:B("auth.register.step4.form.confirmPasswordRequired")},({getFieldValue:q})=>({validator(fe,ie){return!ie||q("password")===ie?Promise.resolve():Promise.reject(new Error(B("auth.register.step4.form.passwordMismatch")))}}),...y];return o.jsxs("div",{className:v,children:[o.jsx(X.Item,{label:o.jsxs("span",{className:`text-base text-gray-700 font-medium ${j}`,children:[B("auth.register.step4.form.password"),O&&":"]}),name:"password",rules:L,className:"mb-4",children:o.jsx(se.Password,{placeholder:B("auth.register.step4.form.passwordPlaceholder"),onChange:z,iconRender:q=>q?o.jsx(Y1,{}):o.jsx(Z1,{}),className:$||"s-form-input"})}),g&&o.jsx("div",{className:`${A}`,children:o.jsx(m9,{password:r})}),o.jsx(X.Item,{name:"confirm",label:o.jsxs("span",{className:`text-base text-gray-700 font-medium ${j}`,children:[B("auth.register.step4.form.confirmPassword"),O&&":"]}),dependencies:["password"],rules:U,className:"mb-6",children:o.jsx(se.Password,{placeholder:B("auth.register.step4.form.confirmPasswordPlaceholder"),iconRender:q=>q?o.jsx(Y1,{}):o.jsx(Z1,{}),className:$||"s-form-input"})}),u&&o.jsx("div",{className:"mb-6",children:o.jsxs(ph,{checked:c,onChange:P,className:"text-sm text-label",children:[B("auth.register.step4.form.agreeTerms")," ",o.jsx("a",{href:"#",className:"text-primary hover:text-primary",children:B("auth.register.step4.form.termsOfService")})," ",B("auth.register.step4.form.and")," ",o.jsx("a",{href:"#",className:"text-primary hover:text-primary",children:B("auth.register.step4.form.privacyPolicy")})]})})]})},h9=({visible:i,onClose:r})=>{const{t:s}=Se(),[u,c]=ee.useState(""),[m,g]=ee.useState(!1),[v]=X.useForm();if(!i)return null;const x=$=>{c($)},y=async $=>{try{g(!0);const j=await Ae.user.changePassword({password:$.password});j.code===200&&j.body.trueOrFalse?(Z.success(s("common.saveSuccess")),r(),v.resetFields()):Z.error(j.message||s("common.saveFailed"))}catch(j){console.error("密码修改失败:",j),Z.error(s("common.saveFailed"))}finally{g(!1)}};return o.jsx("div",{className:"my-20px -ml-148px",children:o.jsxs(X,{form:v,requiredMark:!1,onFinish:y,autoComplete:"off",children:[o.jsx(A2,{password:u,onPasswordChange:x,inputClassName:"s-profile-input",labelClassName:"text-label  !text-12px !w-135px",passwordRequirementsClassName:"pl-148px",showLabelColon:!1,showTermsCheckbox:!1,className:"border"}),o.jsx(j2,{className:"pl-148px",loading:m,onCancel:()=>{r(),v.resetFields()}})]})})},T2=({placeholder:i,size:r="large",value:s,onChange:u,className:c})=>{const{t:m}=Se(),[g,v]=G.useState([]),[x,y]=G.useState(!1);G.useEffect(()=>{(async()=>{try{y(!0);const A=await Ae.meta.getCountries();A.code===200?v(A.body||[]):Z.error(m("common.messages.loadCountriesFailed"))}catch(A){console.error("获取国家列表失败:",A),Z.error(A?.message||m("common.messages.loadCountriesFailed"))}finally{y(!1)}})()},[m]);const $=g.map(j=>({label:j.name,value:j.code}));return o.jsx(la,{placeholder:i||m("common.form.selectCountry"),size:r,className:c||"s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px ",options:$,value:s,onChange:u,loading:x,showSearch:!0,filterOption:(j,A)=>(A?.label??"").toLowerCase().includes(j.toLowerCase())})},O2=({placeholder:i,size:r="large",form:s,countryFieldName:u="country",stateFieldName:c="state",value:m,onChange:g,selectClassName:v,inputClassName:x})=>{const{t:y}=Se(),$=X.useWatch(u,s),[j,A]=G.useState([]),[O,B]=G.useState(!1),[z,P]=G.useState(!1);G.useEffect(()=>{(async()=>{if(!$){A([]),P(!1);return}if(!P4.isSupportedCountryForSubdivisions($)){A([]),P(!0);return}try{B(!0),P(!1);const q=await Ae.meta.getSubdivisions($);q.code===200?A(q.body||[]):(Z.error(y("common.messages.loadSubdivisionsFailed")),A([]))}catch(q){console.error("获取州/省列表失败:",q),Z.error(q?.message||y("common.messages.loadSubdivisionsFailed")),A([])}finally{B(!1)}})()},[$,y]);const L=G.useMemo(()=>j.map(U=>({label:U.name,value:U.code})),[j]);return z?o.jsx(se,{type:"text",placeholder:i||y("common.form.statePlaceholder"),className:x||"s-form-input",value:m,onChange:U=>g?.(U.target.value),disabled:!$}):o.jsx(la,{placeholder:i||y("common.form.selectState"),size:r,className:v||"s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px ",options:L,value:m,onChange:g,loading:O,disabled:!$,showSearch:!0,filterOption:(U,q)=>(q?.label??"").toLowerCase().includes(U.toLowerCase())})},{TextArea:g9}=se,p9=({visible:i,onClose:r})=>{const{t:s,isEnUS:u}=Se(),{logout:c,isArtist:m,profile:g,profileLoading:v,fetchUserProfile:x,updateProfileData:y}=Ht(),[$,j]=G.useState({}),[A,O]=G.useState(!1),[B,z]=G.useState(0),[P,L]=G.useState(!1),U=G.useRef(null),[q]=X.useForm(),fe=D=>{j(H=>({...H,[D]:!0}))},ie=D=>{j(H=>({...H,[D]:!1})),q.resetFields()},$e=async({fieldName:D,requestData:H})=>{try{if(D==="alias"&&H.alias){const J=await Ae.auth.checkAlias(H.alias);if(H.alias===g?.alias){j(W=>({...W,[D]:!1}));return}if(J.code===200&&J.body?.trueOrFalse===!1){const W=J.body.suggestions||[],he=W.length>0?s("auth.register.step2.messages.aliasSuggestions",{suggestions:W.join(", ")}):"";q.setFields([{name:"alias",errors:[he||s("auth.register.step2.messages.aliasUnavailable")]}]);return}}const V=await Ae.user.updateProfile(H);V.code===200?(j(J=>({...J,[D]:!1})),Z.success(s("common.saveSuccess")),await x()):Z.error(V.message||s("common.saveFailed"))}catch(V){console.error("保存失败:",V),Z.error(s("common.saveFailed"))}};G.useEffect(()=>{i&&x()},[i,x]);const re=()=>{P||(z(60),U.current=setInterval(()=>{z(D=>D<=1?(clearInterval(U.current),U.current=null,L(!1),0):D-1)},1e3))},K=()=>{j({}),q.resetFields(),O(!1),z(0),L(!1),U.current&&(clearInterval(U.current),U.current=null)};G.useEffect(()=>()=>{U.current&&clearInterval(U.current)},[]);const oe=G.useMemo(()=>g?D4(g):"",[g]),Te=async D=>{try{const H=await Ae.user.updateProfile({avatarUrl:D});H.code===200?(Z.success(s("common.saveSuccess")),await x()):Z.error(H.message||s("common.saveFailed"))}catch(H){console.error("头像上传失败:",H),Z.error(s("common.saveFailed"))}},Et=async D=>{const{file:H,onSuccess:V,onError:J}=D;try{const W=await Ae.user.uploadAvatar(H);V({url:W.url})}catch(W){console.error("头像上传失败:",W),J(W)}},Oe=D=>o.jsxs("div",{className:"flex items-start",children:[o.jsxs("span",{className:"text-label mr-6px w-100px text-right -ml-100px",children:[D.label,":"]}),o.jsxs("div",{className:"flex-1",children:[o.jsx("span",{className:"text-white",children:D.value}),o.jsx($h,{className:"ml-10px cursor-pointer hover:text-primary",onClick:()=>fe(D.fieldName)})]})]},D.fieldName),ra=(D,H)=>{if(D==="input")return o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:H.label}),name:H.fieldName,children:o.jsx(se,{className:"s-profile-input",size:"large"})});if(D==="textarea")return o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:H.label}),name:H.fieldName,children:o.jsx(g9,{rows:4,className:"s-profile-input"})});if(D==="name"){const V=[{min:2,message:s("auth.register.step3.validation.nameLength",{min:2,max:12})},{max:12,message:s("auth.register.step3.validation.nameLength",{min:2,max:12})},{pattern:/^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,message:s("auth.register.step3.validation.namePattern")}],J=o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.firstName")}),name:"firstName",rules:V,children:o.jsx(se,{placeholder:s("auth.register.step3.form.firstNamePlaceholder"),className:"s-profile-input",size:"large"})},"firstName"),W=o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.lastName")}),name:"lastName",rules:V,children:o.jsx(se,{placeholder:s("auth.register.step3.form.lastNamePlaceholder"),className:"s-profile-input",size:"large"})},"lastName");return u?[J,W]:[W,J]}return D==="address"?o.jsxs(o.Fragment,{children:[o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.address")}),name:"address",children:o.jsx(se,{placeholder:s("auth.register.step3.form.addressPlaceholder"),className:"s-profile-input",size:"large"})}),o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.city")}),name:"city",children:o.jsx(se,{placeholder:s("auth.register.step3.form.cityPlaceholder"),className:"s-profile-input",size:"large"})}),o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.country")}),name:"country",children:o.jsx(T2,{placeholder:s("common.form.selectCountry"),size:"small",className:"s-profile-selector !h-35px"})}),o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("common.state")}),name:"state",children:o.jsx(O2,{form:q,placeholder:s("common.form.selectState"),size:"small",selectClassName:"s-profile-selector !h-35px",inputClassName:"s-profile-input !h-35px"})}),o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("auth.register.step3.form.postalZipCode")}),name:"postalZipCode",children:o.jsx(se,{placeholder:s("auth.register.step3.form.postalZipCodePlaceholder"),className:"s-profile-input",size:"large"})})]}):D==="email"?o.jsxs(o.Fragment,{children:[o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("common.email")}),name:"email",rules:[{required:!0,message:s("common.form.emailRequired")}],children:o.jsx(se,{placeholder:s("common.form.emailRequired"),className:"s-profile-input",size:"large"})}),o.jsx(X.Item,{label:o.jsx("span",{className:"text-label text-12px w-100px",children:s("common.emailCode")}),name:"verificationCode",rules:[{required:!0,message:s("common.emailCodeRequired")}],children:o.jsx(se,{placeholder:s("auth.login.form.emailCodeLabel"),className:"s-profile-input",size:"large"})}),o.jsxs("div",{className:"pl-114px flex flex-col gap-12px",children:[o.jsx("div",{children:o.jsxs(ht,{htmlType:"button",size:"small",type:"primary",loading:P,disabled:P||B>0,onClick:async()=>{try{const V=q.getFieldValue("email");if(!V){Z.error(s("common.form.emailRequired"));return}if(V===g?.email){q.setFields([{name:"email",errors:[s("common.messages.emailSameAsOld")]}]);return}const J=await Ae.auth.checkUsername(V);if(J.code===200&&J.body.trueOrFalse){L(!0);const W=await Ae.user.sendChangeEmailOtp({recipient:V});W.code===200?(Z.success(s("common.messages.verificationCodeSent")),re()):Z.error(W.message||s("messages.sendFailed"))}else J.body.trueOrFalse||q.setFields([{name:"email",errors:[s("common.messages.emailUnavailable")]}])}catch(V){console.error("发送验证码失败:",V),Z.error(s("messages.sendFailed"))}finally{L(!1)}},className:"leading-24px rounded-2px text-12px !text-white mb-15px hover:!text-white",children:[o.jsx("img",{src:n9,className:"w-13px",alt:"sendCode"}),B>0?`${B}s`:s(P?"common.sending":"common.verificationCode")]})}),o.jsxs("div",{className:"flex gap-12px",children:[o.jsx(ht,{htmlType:"button",size:"small",type:"primary",ghost:!0,onClick:()=>{ie(H.fieldName)},className:"leading-24px !text-label !border-label rounded-2px text-12px  mb-15px hover:!text-label",children:s("common.cancel")}),o.jsx(ht,{size:"small",type:"primary",htmlType:"submit",className:"leading-24px rounded-2px text-12px text-white mb-15px hover:!text-white",icon:o.jsx(vh,{}),children:s("common.verify")})]})]})]}):null},We=(D,H)=>{const V=async W=>{let he={};if(D==="name")he={firstName:W.firstName||"",lastName:W.lastName||""};else if(D==="address")he={addressLine1:W.address||"",addressLine2:W.city||"",countryCode:W.country||"",stateProvince:W.state||"",postalZipCode:W.postalZipCode||""};else if(D==="email"){try{console.log("values---->",W);const de=await Ae.auth.verifyOtp({username:W.email,verificationCode:W.verificationCode});if(de.code===200&&de.body.trueOrFalse){const Me=await Ae.user.updateProfile({email:W.email});Me.code===200?(j(gt=>({...gt,[H.fieldName]:!1})),Z.success(s("common.saveSuccess")),await x()):Z.error(Me.message||s("common.saveFailed"))}else Z.error(de.message||s("common.saveFailed"))}catch(de){console.error("邮箱修改失败:",de),Z.error(s("common.saveFailed"))}return}else he={[H.fieldName]:W[H.fieldName]||""};$e({fieldName:H.fieldName,requestData:he})},J=()=>D==="name"?{firstName:g?.firstName||"",lastName:g?.lastName||""}:D==="address"?{address:g?.addressLine1||"",city:g?.addressLine2||"",country:g?.countryCode||"",state:g?.stateProvince||"",postalZipCode:g?.postalZipCode||""}:D==="email"?{email:g?.email||"",verificationCode:""}:{[H.fieldName]:H.value};return o.jsx("div",{className:"flex items-start -ml-106px",children:o.jsx("div",{className:"flex-1",children:o.jsxs(X,{form:q,requiredMark:!1,onFinish:V,autoComplete:"off",initialValues:J(),children:[ra(D,H),D!=="email"&&o.jsx(j2,{className:"pl-114px",htmlType:"submit",onCancel:()=>ie(H.fieldName)})]})})},H.fieldName)},jt=()=>{let D=[{name:"alias",editable:!!$.alias,showElement:Oe({label:s("common.alias"),value:g?.alias||"",fieldName:"alias"}),editElement:We("input",{label:s("common.alias"),value:g?.alias||"",fieldName:"alias"})},{name:"name",editable:!!$.name,showElement:Oe({label:s("common.name"),value:g?.displayName||"",fieldName:"name"}),editElement:We("name",{label:s("common.name"),value:g?.displayName||"",fieldName:"name"})},{name:"email",editable:!!$.email,showElement:Oe({label:s("common.email"),value:g?.email||"",fieldName:"email"}),editElement:We("email",{label:s("common.email"),value:g?.email||"",fieldName:"email"})},{name:"address",editable:!!$.address,showElement:Oe({label:s("common.address"),value:oe,fieldName:"address"}),editElement:We("address",{label:s("common.address"),value:oe,fieldName:"address"})}];const H=[{name:"stageName",editable:!!$.stageName,showElement:Oe({label:s("common.stageName"),value:g?.stageName||"",fieldName:"stageName"}),editElement:We("input",{label:s("common.stageName"),value:g?.stageName||"",fieldName:"stageName"})},{name:"bio",editable:!!$.bio,showElement:Oe({label:s("common.artistBio"),value:g?.bio||"",fieldName:"bio"}),editElement:We("textarea",{label:s("common.artistBio"),value:g?.bio||"",fieldName:"bio"})}];return m&&(D=D.concat(H)),D.forEach((V,J)=>{V._id=J}),o.jsx("div",{className:"flex flex-col gap-15px",children:D.map(V=>V.editable?V.editElement:V.showElement)})};if(!i)return null;const R=()=>o.jsxs("div",{className:"flex flex-col items-center pt-55px w-500px mx-auto",children:[o.jsx("div",{className:"flex flex-col items-center mb-30px",children:o.jsx("div",{className:"w-230px h-230px rounded-full bg-gray-700 animate-pulse mb-4"})}),o.jsxs("div",{className:"w-full text-12px",children:[o.jsx("div",{className:"text-center text-white text-22px font-medium mb-30px",children:o.jsx("div",{className:"h-6 bg-gray-700 rounded animate-pulse w-32 mx-auto"})}),o.jsx("div",{className:"flex flex-col gap-15px",children:[{label:s("common.alias")},{label:s("common.name")},{label:s("common.email")},{label:s("common.address")},...m?[{label:s("common.stageName")},{label:s("common.artistBio")}]:[]].map((D,H)=>o.jsxs("div",{className:"flex items-start",children:[o.jsxs("span",{className:"text-label mr-6px w-100px text-right -ml-100px",children:[D.label,":"]}),o.jsx("div",{className:"flex-1",children:o.jsx("div",{className:"h-4 bg-gray-700 rounded animate-pulse w-full max-w-xs"})})]},H))}),o.jsxs("div",{className:"mt-80px flex justify-center gap-20px",children:[o.jsx("div",{className:"h-10 bg-gray-700 rounded animate-pulse flex-1"}),o.jsx("div",{className:"h-10 bg-gray-700 rounded animate-pulse flex-1"})]})]})]});return o.jsx(mi,{theme:{components:{Select:{optionLineHeight:"35px",optionHeight:35},Form:{fontSize:12}}},children:o.jsx(c2,{open:i,onCancel:()=>{K(),r()},width:1e3,className:`
      [&.ant-modal_.ant-modal-close]:inset-ie-auto [&.ant-modal_.ant-modal-close]:ml-12px [&.ant-modal_.ant-modal-close]:top-40px
      [&.ant-modal_.ant-modal-content]:px-50px [&.ant-modal_.ant-modal-content]:pt-35px [&.ant-modal_.ant-modal-content]:pb-112px `,footer:null,keyboard:!1,maskClosable:!1,centered:!0,zIndex:800,closeIcon:o.jsxs("div",{className:"flex items-center gap-2 hover:opacity-45",children:[o.jsx(f2,{style:{fontSize:"16px",color:"var(--color-label)"}}),o.jsx("span",{className:"text-#B5B5B5 font-400 min-w-max",children:s("common.back")})]}),title:o.jsx("div",{className:" text-center text-white text-32px font-700",children:s("common.profile")}),children:v||!g?R():o.jsxs("div",{className:"flex flex-col items-center pt-55px w-500px mx-auto ",children:[o.jsx("div",{className:"flex flex-col items-center mb-30px",children:o.jsx(c9,{avatarUrl:g?.avatarUrl||"",avatarSize:230,userName:g?.alias||"",onUploadSuccess:Te,customRequest:Et})}),o.jsxs("div",{className:"w-full text-12px",children:[o.jsx("div",{className:"text-center text-white text-22px font-medium mb-30px",children:g?.displayName||""}),o.jsx("div",{children:jt()}),o.jsx(h9,{visible:A,onClose:()=>O(!1)}),o.jsxs("div",{className:"mt-80px flex justify-center gap-20px",children:[!A&&o.jsx(Da,{ghost:!0,variant:"outlined",className:"flex-1",onClick:()=>O(!0),children:s("common.changePassword")}),o.jsx(Da,{type:"primary",className:"flex-1",onClick:c,children:s("common.navigation.logout")})]})]})]})})})},{Content:$9,Sider:v9,Header:y9}=zr,x9=i=>[{key:"music-market",label:Ce.t("common.navigation.musicMarket"),url:"/music-market",icon:yh},{key:"my-assets",label:Ce.t("common.navigation.myAssets"),url:"/my-assets",icon:xh},{key:"my-balance",label:Ce.t("common.navigation.myBalance"),url:"/my-balance",icon:bh},{key:"my-orders",label:Ce.t("common.navigation.myOrders"),url:"/my-orders",icon:Sh},{key:"submit-music",label:Ce.t("common.navigation.submitMusic"),url:"/submit-music",icon:Eu}],b9=()=>{const i=ia(),r=fi();Se();const{isAuthenticated:s}=Ht();G.useEffect(()=>{Ht.getState().initializeAuth()},[]);const u=x9(),[c,m]=G.useState(!1),g=()=>{m(!0)},v=x=>{i(x)};return o.jsxs("div",{className:"bg-black min-h-screen",children:[o.jsxs(zr,{className:"min-h-screen bg-black",children:[o.jsx(y9,{className:"bg-black border-b-0 p-0 h-auto",children:o.jsx(Pu,{fixed:!1,onClick:g})}),o.jsxs(zr,{className:"bg-black",children:[s&&o.jsx(v9,{width:307,className:"bg-[#0d0d0d] pt-8 px-4",children:o.jsx("div",{className:"space-y-1",children:u.map(x=>{const y=r.pathname===x.url;return o.jsx("div",{className:`
                        relative px-6 py-3 rounded-md cursor-pointer transition-colors
                        ${y?"text-primary border-2 border-primary border-solid bg-[#1a1a1a]":"text-[#999999] hover:text-white hover:bg-[#1a1a1a]"}
                      `,onClick:()=>v(x.url),children:o.jsxs("div",{className:"flex items-center",children:[o.jsx(x.icon,{className:"w-6 h-6 mr-3"}),o.jsx("span",{className:"text-18px font-bold",children:x.label})]})},x.key)})})}),o.jsx($9,{className:"flex-1 bg-[#0d0d0d]",children:o.jsx(i2,{})})]})]}),o.jsx(p9,{visible:c,onClose:()=>m(!1)})]})},Yu=({onSubmit:i,buttonText:r=Ce.t("auth.register.step1.buttons.next")})=>{const s=Ce.t;return o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"mb-64px",children:[o.jsx("span",{className:"font-inter text-[12px] text-[#656565] font-medium",children:s("auth.login.form.noAccount")}),o.jsx(r2,{to:"/register",className:"font-inter ml-1 text-[12px] font-medium !text-[#ff5e13] hover:underline",children:s("auth.login.form.register")})]}),o.jsx("button",{onClick:i,className:"font-arial h-[63px] w-full cursor-pointer rounded-md border-none bg-[#ff5e13] text-[16px] text-black font-bold transition-colors hover:bg-[#e5541a]",children:r})]})},S9=({form:i,onSubmit:r,initialEmail:s=""})=>{const{t:u}=Se();G.useEffect(()=>{s&&i.setFieldsValue({email:s})},[s,i]);const c=()=>{i.validateFields(["email"]).then(g=>{r(g)}).catch(g=>{console.log("Validation failed:",g)})},m=[{required:!0},{type:"email",message:u("common.form.emailInvalid")}];return o.jsxs(o.Fragment,{children:[o.jsx(X.Item,{name:"email",rules:m,messageVariables:{label:u("auth.register.step1.form.email")},label:u("auth.register.step1.form.email"),className:"mb-6",children:o.jsx(se,{placeholder:u("common.form.emailRequired"),className:"s-form-input",size:"large"})}),o.jsx(Yu,{onSubmit:c}),o.jsx("div",{className:"h-10 text-center leading-10"})]})},N9=({value:i,onChange:r,placeholder:s=Ce.t("auth.login.form.emailCodeLabel"),onSendCode:u,disabled:c=!1,countdownTime:m=60,className:g,size:v="large"})=>{const{t:x}=Se(),[y,$]=G.useState(!1),[j,A]=G.useState(0),O=j>0||y||c,B=async()=>{u&&($(!0),await u(),$(!1)),P()},z=G.useRef(null),P=()=>{y||(A(m),z.current=setInterval(()=>{A(L=>(console.log("设置countDown"),L<=1?(clearInterval(z.current),z.current=null,$(!1),0):L-1))},1e3))};return o.jsx(se,{type:"text",size:v,placeholder:s||x("auth.login.form.emailCodeLabel"),className:g||"s-form-input",disabled:c,value:i,onChange:L=>r?.(L.target.value),suffix:o.jsxs("div",{className:"flex items-center",children:[j>0&&o.jsxs("span",{className:"mr-2 text-[12px] text-#999",children:[j,"s"]}),o.jsx(ht,{variant:"link",color:"default",loading:y,onClick:B,disabled:O,className:"text-[12px] !text-[#ff5e13] !disabled:text-#999",children:j>0?x("auth.login.form.resend"):x(y?"auth.login.form.sending":"auth.login.form.sendCode")})]})})},C9=({form:i,onSubmit:r,onSwitchToPassword:s,userEmail:u})=>{const{t:c}=s2(),m=()=>{i&&r&&i.validateFields(["emailCode"]).then(v=>{r(v)}).catch(v=>{console.log("Email code validation failed:",v)})},g=async()=>{try{const v=i.getFieldValue("email");if(!v)throw new Error("邮箱地址不能为空");console.log("重新发送验证码到邮箱:",v);const x=await Ae.auth.sendLoginOtp({recipient:v});if(x.code===200&&x.body.trueOrFalse)console.log("验证码发送成功");else{let y="验证码发送失败";switch(x.code){case 400:y="邮箱格式不正确";break;case 404:y="该邮箱未注册";break;case 429:y="发送频率过快，请稍后重试";break;case 500:y="服务器错误，请稍后重试";break;default:y=x.message||"验证码发送失败，请重试"}throw new Error(y)}}catch(v){throw console.error("发送验证码失败:",v),v}};return o.jsxs(o.Fragment,{children:[o.jsx(X.Item,{label:c("auth.login.form.emailCodeLabel"),name:"emailCode",rules:[{required:!0,message:c("common.form.required")}],validateTrigger:["onChange","onBlur"],className:"mb-6",children:o.jsx(N9,{onSendCode:g})}),o.jsx(Yu,{buttonText:c("auth.login.form.submit"),onSubmit:m}),o.jsx("div",{className:"mt-6 text-center",children:o.jsx("span",{className:"cursor-pointer text-[12px] text-[#ff5e13] hover:underline",onClick:s,children:c("auth.login.form.loginWithPassword")})})]})},w9=({form:i,onSubmit:r,onSwitchToEmailCode:s,initialPassword:u=""})=>{G.useEffect(()=>{u&&i.setFieldsValue({password:u})},[u,i]);const{t:c}=Se(),m=()=>{i.validateFields(["password"]).then(g=>{r(g)}).catch(g=>{console.log("Password validation failed:",g)})};return o.jsxs(o.Fragment,{children:[o.jsx(X.Item,{label:c("auth.login.form.password"),name:"password",messageVariables:{label:c("auth.login.form.password")},rules:[{required:!0}],className:"mb-6",children:o.jsx(se.Password,{placeholder:c("auth.register.step4.form.passwordPlaceholder"),className:"s-form-input",size:"large"})}),o.jsx(Yu,{buttonText:c("auth.login.form.submit"),onSubmit:m}),o.jsx("div",{className:"mt-6 text-center",children:o.jsx("span",{className:"cursor-pointer text-[12px] text-[#ff5e13] hover:underline",onClick:s,children:c("auth.login.form.loginWithEmailCode")})})]})},E9=()=>{const{t:i}=Se(),r=ia(),s=fi(),[u,c]=G.useState("userName"),[m,g]=G.useState("password"),[v]=X.useForm();G.useEffect(()=>{v.setFieldsValue({email:"<EMAIL>",password:"031420"})},[]);const x=L=>{v.setFieldsValue({email:L.email}),c("auth")},y=L=>{v.setFieldsValue({password:L.password}),A()},$=L=>{v.setFieldsValue({emailCode:L.emailCode}),A()},{login:j}=Ht(),A=async()=>{const L=v.getFieldsValue(!0);console.log("loginData----",L);const U={username:L.email};m==="password"?U.password=L.password:U.verificationCode=L.emailCode;try{await j(U),Z.success(i("auth.login.success"));const q=s.state?.from?.pathname||"/";r(q,{replace:!0})}catch(q){console.error("登录失败:",q);const fe=q.message||i("auth.login.error");Z.error(fe)}},O=async()=>{g("email"),v.setFieldsValue({password:"",emailCode:""}),console.log("清空密码和邮箱验证码");try{const L=z();if(L){const U=await Ae.auth.sendLoginOtp({recipient:L});if(U.code===200&&U.body.trueOrFalse)Z.success(i("auth.login.otpSent"));else{let q=i("auth.login.otpSendFailed");switch(U.code){case 400:q="邮箱格式不正确";break;case 404:q="该邮箱未注册";break;case 429:q="发送频率过快，请稍后重试";break;case 500:q="服务器错误，请稍后重试";break;default:q=U.message||i("auth.login.otpSendFailed")}Z.error(q)}}}catch(L){console.error("发送验证码失败:",L),Z.error(i("auth.login.otpSendFailed"))}},B=()=>{g("password"),v.setFieldsValue({password:"",emailCode:""}),console.log("清空密码和邮箱验证码")},z=()=>{const{email:L}=v.getFieldsValue(!0);return L},P=()=>{switch(u){case"userName":return o.jsx(S9,{form:v,onSubmit:x});case"auth":return m==="email"?o.jsx(C9,{form:v,onSubmit:$,onSwitchToPassword:B,userEmail:z()}):o.jsx(w9,{form:v,onSubmit:y,onSwitchToEmailCode:O});default:return null}};return o.jsx(mi,{theme:{components:{Form:{labelFontSize:12}}},children:o.jsxs("div",{className:"relative pt-[120px] min-h-screen flex items-center justify-center bg-black text-[#656565]",children:[o.jsx(Pu,{}),o.jsx("div",{className:"absolute inset-0 bg-page-bg"}),o.jsxs("div",{className:"relative z-10 h-520px flex flex-col items-center",children:[o.jsx("div",{className:"mb-8",children:o.jsx("img",{src:Gu,alt:"Yuequ Logo",className:"h-[60px] w-[69px]"})}),o.jsx("h1",{className:"font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold",children:i("auth.login.title")}),o.jsx("div",{className:"w-[496px]",children:o.jsx(X,{form:v,layout:"vertical",onValuesChange:(L,U)=>{},children:P()})})]})]})})},j9=()=>{const i=ia(),{t:r}=Se(),s=()=>{i("/",{replace:!0})},u=()=>{i(-1)};return o.jsx("div",{className:"min-h-screen flex items-center justify-center from-blue-50 to-indigo-100 bg-gradient-to-br p-4",children:o.jsxs("div",{className:"max-w-2xl w-full",children:[o.jsx(Nh,{status:"404",title:o.jsx("div",{className:"mb-4 text-6xl text-blue-600 font-bold",children:r("error.404.title")}),subTitle:o.jsx("div",{className:"mb-8 text-xl text-gray-600",children:r("error.404.subtitle")}),extra:o.jsxs("div",{className:"flex flex-col justify-center gap-4 sm:flex-row",children:[o.jsx(ht,{type:"primary",size:"large",icon:o.jsx(Ch,{}),onClick:s,className:"h-auto border-blue-500 bg-blue-500 px-8 py-2 hover:border-blue-600 hover:bg-blue-600",children:r("error.404.backHome")}),o.jsx(ht,{size:"large",icon:o.jsx(f2,{}),onClick:u,className:"h-auto border-blue-500 px-8 py-2 text-blue-500 hover:border-blue-600 hover:text-blue-600",children:r("error.404.goBack")})]}),className:"mx-4 rounded-2xl bg-white p-8 shadow-lg"}),o.jsx("div",{className:"mt-12 text-center",children:o.jsxs("div",{className:"inline-flex items-center gap-2 text-sm text-gray-500",children:[o.jsx("div",{className:"h-2 w-2 animate-pulse rounded-full bg-blue-400"}),o.jsxs("span",{children:[r("common.appName")," - ",r("common.slogan")]}),o.jsx("div",{className:"h-2 w-2 animate-pulse rounded-full bg-blue-400"})]})})]})})},R2=({children:i})=>{const r=fi(),{isAuthenticated:s}=Ht();return s?o.jsx(o.Fragment,{children:i}):o.jsx(zu,{to:"/login",replace:!0,state:{from:r}})},Tf=({children:i,redirectTo:r="/"})=>{const s=fi(),{isAuthenticated:u}=Ht();if(u){const c=s.state?.from?.pathname||r;return o.jsx(zu,{to:c,replace:!0})}return o.jsx(o.Fragment,{children:i})},A9=()=>{const i=ia(),r=fi();return ee.useEffect(()=>{r.pathname==="/register"&&i("/register/email",{replace:!0})},[r.pathname,i]),o.jsx(mi,{theme:{cssVar:!0,token:{colorPrimary:"#ff5e13"},components:{Form:{labelFontSize:12},Select:{colorBgContainerDisabled:"#888888",colorTextDisabled:"#ffffff",colorBgContainer:"var(--color-form-item)",activeBorderColor:"var(--color-form-item)",optionActiveBg:"var(--color-form-item)",optionLineHeight:"54px",optionHeight:54},Button:{colorPrimaryHover:"var(--color-primary-active)",colorPrimaryActive:"var(--color-primary-active)",primaryColor:"var(--color-primary-text)",colorBgContainerDisabled:"var(--color-primary)",borderColorDisabled:"transparent",colorTextDisabled:"var(--color-primary-text)",contentFontSizeLG:18,controlHeightLG:63}}},children:o.jsxs("div",{className:"min-h-screen pt-[120px] flex  items-center justify-center bg-page-bg text-[#656565]",children:[o.jsx(Pu,{}),o.jsx("div",{className:"w-496px",children:o.jsx(i2,{})})]})})},Gr=Lu()(Bu(Hu((i,r)=>({formData:{},updateFormData:s=>{i({formData:{...r().formData,...s}})},clearFormData:()=>{i({formData:{}})}}),{name:"register-store"}),{name:"RegisterStore"})),T9=60,O9=()=>{const i=ia(),{t:r}=Se(),{formData:s,updateFormData:u}=Gr(),[c]=X.useForm(),m=X.useWatch("email",c),[g,v]=G.useState(!0),[x,y]=G.useState(0),[$,j]=G.useState(!1),A=G.useRef(null),O=async()=>{try{await c.validateFields(["email"]);const U=c.getFieldValue("email");j(!0);const q=await Ae.auth.checkUsername(U);if(q.code===200&&q.body.trueOrFalse){const fe=await Ae.auth.sendSignupOtp({recipient:U});fe.code===200&&fe.body.trueOrFalse?(Z.success(r("common.messages.verificationCodeSent")),v(!1),z()):Z.error(r("common.messages.sendVerificationCodeFailed"))}else c.setFields([{name:"email",errors:[r("auth.register.step1.messages.emailAlreadyExists")]}])}catch(U){console.error("发送验证码失败:",U),Z.error(U?.message||r("common.messages.sendVerificationCodeFailed"))}finally{j(!1)}},B=async()=>{try{await c.validateFields(["verificationCode"]);const U=c.getFieldValue("email"),q=c.getFieldValue("verificationCode"),fe=await Ae.auth.verifyOtp({username:U,verificationCode:q});fe.code===200&&fe.body.trueOrFalse?(Z.success(r("common.messages.verificationSuccess")),u({email:U}),i("/register/alias")):Z.error(r("common.messages.verificationCodeInvalid"))}catch(U){console.error("验证码验证失败:",U),Z.error(U?.message||r("common.messages.verificationCodeInvalid"))}},z=()=>{$||(y(T9),A.current=setInterval(()=>{y(U=>U<=1?(clearInterval(A.current),A.current=null,j(!1),0):U-1)},1e3))},P=async()=>{try{await c.validateFields(["email"]);const U=c.getFieldValue("email");j(!0);const q=await Ae.auth.sendSignupOtp({recipient:U});q.code===200&&q.body.trueOrFalse?(Z.success(r("common.messages.verificationCodeSent")),z()):Z.error(r("common.messages.sendVerificationCodeFailed"))}catch(U){console.error("重新发送验证码失败:",U),Z.error(U?.message||r("common.messages.sendVerificationCodeFailed"))}finally{j(!1)}};G.useEffect(()=>{s.email&&c.setFieldsValue({email:s.email})},[s.email,c]),G.useEffect(()=>()=>{A.current&&clearInterval(A.current)},[]);const L=[{required:!0},{type:"email",message:r("common.form.emailInvalid")}];return o.jsxs("div",{className:"h-520px",children:[o.jsx("div",{className:"mb-8 text-center",children:o.jsx("img",{src:Gu,alt:"Yuequ Logo",className:"h-[60px] w-[69px]"})}),o.jsx("h1",{className:"font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold",children:r("auth.register.title")}),o.jsxs(X,{form:c,layout:"vertical",autoComplete:"off",children:[o.jsx(X.Item,{name:"email",label:r("auth.register.step1.form.email"),rules:L,children:o.jsx(se,{placeholder:r("common.form.emailRequired"),className:"s-form-input",size:"large"})}),!g&&o.jsx(X.Item,{name:"verificationCode",label:r("common.verificationCode"),rules:[{required:!0,message:r("common.form.enterVerificationCode")}],children:o.jsx(se,{placeholder:r("common.form.enterVerificationCode"),className:"s-form-input",size:"large"})}),o.jsxs(X.Item,{children:[o.jsxs("div",{className:"mb-64px",children:[o.jsx("span",{className:"font-inter text-[12px] text-[#656565] font-medium",children:r("auth.register.step1.hasAccount")}),o.jsx(r2,{to:"/login",className:"font-inter ml-1 text-[12px] font-medium !text-[#ff5e13] hover:underline",children:r("auth.register.step1.loginHere")})]}),g&&o.jsx(Da,{htmlType:"button",disabled:!m?.trim(),onClick:O,children:r("common.buttons.sendVerificationCode")}),!g&&o.jsxs("div",{className:"flex justify-between gap-20px",children:[o.jsx(Da,{ghost:!0,htmlType:"button",onClick:B,children:r("common.verify")}),o.jsx(Da,{htmlType:"button",onClick:P,disabled:$||x>0,children:x>0?`${x}s`:r($?"common.sending":"common.resendCode")})]})]})]}),o.jsx("div",{className:"w-[496px]"})]})},R9=()=>{const i=ia(),{t:r}=Se(),{formData:s,updateFormData:u}=Gr(),[c]=X.useForm(),m=X.useWatch("alias",c);ee.useEffect(()=>{s.alias&&c.setFieldsValue({alias:s.alias})},[s.alias,c]),ee.useEffect(()=>{s.email||(Z.warning(r("auth.register.step4.messages.emailVerificationRequired")),i("/register/email"))},[s.email,i]);const g=async()=>{try{const y=c.getFieldValue("alias"),$=await Ae.auth.checkAlias(y);if($.code===200&&$.body.trueOrFalse)u({alias:y}),i("/register/personal-info");else{const j=$.body.suggestions||[],A=j.length>0?r("auth.register.step2.messages.aliasSuggestions",{suggestions:j.join(", ")}):"";c.setFields([{name:"alias",errors:[A||r("auth.register.step2.messages.aliasUnavailable")]}])}}catch(y){console.error("检查别名失败:",y),Z.error(y?.message||r("auth.register.step2.messages.aliasCheckFailed"))}},v=(y,$)=>$?/^\s|\s$/.test($)?Promise.reject(new Error("首尾不能包含空格")):/[^a-zA-Z0-9 ]/.test($)?Promise.reject(new Error(r("auth.register.step2.form.aliasPattern"))):Promise.resolve():Promise.resolve(),x=[{required:!0,message:r("auth.register.step2.form.aliasRequired")},{min:6,message:r("auth.register.step2.form.aliasMinLength",{min:6})},{max:12,message:r("auth.register.step2.form.aliasMaxLength",{max:12})},{validator:v}];return o.jsxs("div",{className:" h-520px  ",children:[o.jsx("h1",{className:"font-arial mb-2 text-center text-[32px] text-[#ff5e13] font-bold",children:r("auth.register.title")}),o.jsx("div",{className:"mb-13 text-center text-12px text-label",children:r("auth.register.step2.subtitle")}),o.jsxs(X,{form:c,layout:"vertical",onFinish:g,autoComplete:"off",children:[o.jsx(X.Item,{label:r("auth.register.step2.form.alias"),name:"alias",rules:x,normalize:y=>y.replace(/ {2,}/g," "),children:o.jsx(se,{placeholder:r("auth.register.step2.form.aliasPlaceholder"),className:"s-form-input",size:"large"})}),o.jsxs(X.Item,{children:[o.jsx("div",{className:"mb-64px",children:o.jsx("span",{className:"font-inter text-[12px] text-[#656565] font-medium",children:r("auth.register.step2.form.aliasMinLength",{min:6})})}),o.jsx(Da,{disabled:!m?.trim(),children:r("auth.register.step2.buttons.next")})]})]})]})},_9={version:4,country_calling_codes:{1:["US","AG","AI","AS","BB","BM","BS","CA","DM","DO","GD","GU","JM","KN","KY","LC","MP","MS","PR","SX","TC","TT","VC","VG","VI"],7:["RU","KZ"],20:["EG"],27:["ZA"],30:["GR"],31:["NL"],32:["BE"],33:["FR"],34:["ES"],36:["HU"],39:["IT","VA"],40:["RO"],41:["CH"],43:["AT"],44:["GB","GG","IM","JE"],45:["DK"],46:["SE"],47:["NO","SJ"],48:["PL"],49:["DE"],51:["PE"],52:["MX"],53:["CU"],54:["AR"],55:["BR"],56:["CL"],57:["CO"],58:["VE"],60:["MY"],61:["AU","CC","CX"],62:["ID"],63:["PH"],64:["NZ"],65:["SG"],66:["TH"],81:["JP"],82:["KR"],84:["VN"],86:["CN"],90:["TR"],91:["IN"],92:["PK"],93:["AF"],94:["LK"],95:["MM"],98:["IR"],211:["SS"],212:["MA","EH"],213:["DZ"],216:["TN"],218:["LY"],220:["GM"],221:["SN"],222:["MR"],223:["ML"],224:["GN"],225:["CI"],226:["BF"],227:["NE"],228:["TG"],229:["BJ"],230:["MU"],231:["LR"],232:["SL"],233:["GH"],234:["NG"],235:["TD"],236:["CF"],237:["CM"],238:["CV"],239:["ST"],240:["GQ"],241:["GA"],242:["CG"],243:["CD"],244:["AO"],245:["GW"],246:["IO"],247:["AC"],248:["SC"],249:["SD"],250:["RW"],251:["ET"],252:["SO"],253:["DJ"],254:["KE"],255:["TZ"],256:["UG"],257:["BI"],258:["MZ"],260:["ZM"],261:["MG"],262:["RE","YT"],263:["ZW"],264:["NA"],265:["MW"],266:["LS"],267:["BW"],268:["SZ"],269:["KM"],290:["SH","TA"],291:["ER"],297:["AW"],298:["FO"],299:["GL"],350:["GI"],351:["PT"],352:["LU"],353:["IE"],354:["IS"],355:["AL"],356:["MT"],357:["CY"],358:["FI","AX"],359:["BG"],370:["LT"],371:["LV"],372:["EE"],373:["MD"],374:["AM"],375:["BY"],376:["AD"],377:["MC"],378:["SM"],380:["UA"],381:["RS"],382:["ME"],383:["XK"],385:["HR"],386:["SI"],387:["BA"],389:["MK"],420:["CZ"],421:["SK"],423:["LI"],500:["FK"],501:["BZ"],502:["GT"],503:["SV"],504:["HN"],505:["NI"],506:["CR"],507:["PA"],508:["PM"],509:["HT"],590:["GP","BL","MF"],591:["BO"],592:["GY"],593:["EC"],594:["GF"],595:["PY"],596:["MQ"],597:["SR"],598:["UY"],599:["CW","BQ"],670:["TL"],672:["NF"],673:["BN"],674:["NR"],675:["PG"],676:["TO"],677:["SB"],678:["VU"],679:["FJ"],680:["PW"],681:["WF"],682:["CK"],683:["NU"],685:["WS"],686:["KI"],687:["NC"],688:["TV"],689:["PF"],690:["TK"],691:["FM"],692:["MH"],850:["KP"],852:["HK"],853:["MO"],855:["KH"],856:["LA"],880:["BD"],886:["TW"],960:["MV"],961:["LB"],962:["JO"],963:["SY"],964:["IQ"],965:["KW"],966:["SA"],967:["YE"],968:["OM"],970:["PS"],971:["AE"],972:["IL"],973:["BH"],974:["QA"],975:["BT"],976:["MN"],977:["NP"],992:["TJ"],993:["TM"],994:["AZ"],995:["GE"],996:["KG"],998:["UZ"]},countries:{AC:["247","00","(?:[01589]\\d|[46])\\d{4}",[5,6],0,0,0,0,0,0,0,[0,["4\\d{4}",[5]]]],AD:["376","00","(?:1|6\\d)\\d{7}|[135-9]\\d{5}",[6,8,9],[["(\\d{3})(\\d{3})","$1 $2",["[135-9]"]],["(\\d{4})(\\d{4})","$1 $2",["1"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]]],0,0,0,0,0,0,[0,["690\\d{6}|[356]\\d{5}",[6,9]]]],AE:["971","00","(?:[4-7]\\d|9[0-689])\\d{7}|800\\d{2,9}|[2-4679]\\d{7}",[5,6,7,8,9,10,11,12],[["(\\d{3})(\\d{2,9})","$1 $2",["60|8"]],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[236]|[479][2-8]"],"0$1"],["(\\d{3})(\\d)(\\d{5})","$1 $2 $3",["[479]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["5"],"0$1"]],"0",0,0,0,0,0,[0,["5[024-68]\\d{7}",[9]]]],AF:["93","00","[2-7]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[2-7]"],"0$1"]],"0",0,0,0,0,0,[0,["7\\d{8}"]]],AG:["1","011","(?:268|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([457]\\d{6})$|1","268$1",0,"268",[0,["268(?:464|7(?:1[3-9]|[28]\\d|3[0246]|64|7[0-689]))\\d{4}"]]],AI:["1","011","(?:264|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2457]\\d{6})$|1","264$1",0,"264",[0,["264(?:235|4(?:69|76)|5(?:3[6-9]|8[1-4])|7(?:29|72))\\d{4}"]]],AL:["355","00","(?:700\\d\\d|900)\\d{3}|8\\d{5,7}|(?:[2-5]|6\\d)\\d{7}",[6,7,8,9],[["(\\d{3})(\\d{3,4})","$1 $2",["80|9"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["4[2-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2358][2-5]|4"],"0$1"],["(\\d{3})(\\d{5})","$1 $2",["[23578]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["6"],"0$1"]],"0",0,0,0,0,0,[0,["6(?:[78][2-9]|9\\d)\\d{6}",[9]]]],AM:["374","00","(?:[1-489]\\d|55|60|77)\\d{6}",[8],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[89]0"],"0 $1"],["(\\d{3})(\\d{5})","$1 $2",["2|3[12]"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["1|47"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["[3-9]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:33|4[1349]|55|77|88|9[13-9])\\d{6}"]]],AO:["244","00","[29]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[29]"]]],0,0,0,0,0,0,[0,["9[1-579]\\d{7}"]]],AR:["54","00","(?:11|[89]\\d\\d)\\d{8}|[2368]\\d{9}",[10,11],[["(\\d{4})(\\d{2})(\\d{4})","$1 $2-$3",["2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])","2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)","2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]","2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]"],"0$1",1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2-$3",["1"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["[68]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2-$3",["[23]"],"0$1",1],["(\\d)(\\d{4})(\\d{2})(\\d{4})","$2 15-$3-$4",["9(?:2[2-469]|3[3-578])","9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))","9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)","9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]","9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]"],"0$1",0,"$1 $2 $3-$4"],["(\\d)(\\d{2})(\\d{4})(\\d{4})","$2 15-$3-$4",["91"],"0$1",0,"$1 $2 $3-$4"],["(\\d{3})(\\d{3})(\\d{5})","$1-$2-$3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{4})","$2 15-$3-$4",["9"],"0$1",0,"$1 $2 $3-$4"]],"0",0,"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?","9$1",0,0,[0,["93(?:7(?:1[15]|81)|8(?:21|4[16]|69|9[12]))[46]\\d{5}|9(?:2(?:657|9(?:54|66))|3(?:7(?:55|77)|865))[2-8]\\d{5}|9(?:2(?:2(?:2[59]|44|52)|3(?:26|44)|473|9(?:[07]2|2[26]|34|46))|3327)[45]\\d{5}|9(?:2(?:284|3(?:02|23)|920)|3(?:4(?:46|8[27]|92)|541|878))[2-7]\\d{5}|9(?:2(?:(?:26|62)2|320|477|9(?:42|83))|3(?:329|4(?:62|76|89)|564))[2-6]\\d{5}|(?:675\\d|9(?:11[1-8]\\d|2(?:2(?:0[45]|1[2-6]|3[3-6])|3(?:[06]4|7[45])|494|6(?:04|1[2-8]|[36][45]|4[3-6])|80[45]|9(?:[17][4-6]|[48][45]|9[3-6]))|3(?:364|4(?:1[2-8]|[235][4-6]|84)|5(?:1[2-9]|[38][4-6])|6(?:2[45]|44)|7[069][45]|8(?:0[45]|[17][2-6]|3[4-6]|5[3-6]|8[3-68]))))\\d{6}|92(?:2(?:21|4[23]|6[145]|7[1-4]|8[356]|9[267])|3(?:16|3[13-8]|43|5[346-8]|9[3-5])|475|6(?:2[46]|4[78]|5[1568])|9(?:03|2[1457-9]|3[1356]|4[08]|[56][23]|82))4\\d{5}|9(?:2(?:2(?:57|81)|3(?:24|46|92)|9(?:01|23|64))|3(?:4(?:42|71)|5(?:25|37|4[347]|71)|7(?:18|5[17])))[3-6]\\d{5}|9(?:2(?:2(?:02|2[3467]|4[156]|5[45]|6[6-8]|91)|3(?:1[47]|25|[45][25]|96)|47[48]|625|932)|3(?:38[2578]|4(?:0[0-24-9]|3[78]|4[457]|58|6[03-9]|72|83|9[136-8])|5(?:2[124]|[368][23]|4[2689]|7[2-6])|7(?:16|2[15]|3[145]|4[13]|5[468]|7[2-5]|8[26])|8(?:2[5-7]|3[278]|4[3-5]|5[78]|6[1-378]|[78]7|94)))[4-6]\\d{5}"]]],AS:["1","011","(?:[58]\\d\\d|684|900)\\d{7}",[10],0,"1",0,"([267]\\d{6})$|1","684$1",0,"684",[0,["684(?:2(?:48|5[2468]|7[26])|7(?:3[13]|70|82))\\d{4}"]]],AT:["43","00","1\\d{3,12}|2\\d{6,12}|43(?:(?:0\\d|5[02-9])\\d{3,9}|2\\d{4,5}|[3467]\\d{4}|8\\d{4,6}|9\\d{4,7})|5\\d{4,12}|8\\d{7,12}|9\\d{8,12}|(?:[367]\\d|4[0-24-9])\\d{4,11}",[4,5,6,7,8,9,10,11,12,13],[["(\\d)(\\d{3,12})","$1 $2",["1(?:11|[2-9])"],"0$1"],["(\\d{3})(\\d{2})","$1 $2",["517"],"0$1"],["(\\d{2})(\\d{3,5})","$1 $2",["5[079]"],"0$1"],["(\\d{3})(\\d{3,10})","$1 $2",["(?:31|4)6|51|6(?:5[0-3579]|[6-9])|7(?:20|32|8)|[89]"],"0$1"],["(\\d{4})(\\d{3,9})","$1 $2",["[2-467]|5[2-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["5"],"0$1"],["(\\d{2})(\\d{4})(\\d{4,7})","$1 $2 $3",["5"],"0$1"]],"0",0,0,0,0,0,[0,["6(?:5[0-3579]|6[013-9]|[7-9]\\d)\\d{4,10}",[7,8,9,10,11,12,13]]]],AU:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{7}(?:\\d(?:\\d{2})?)?|8[0-24-9]\\d{7})|[2-478]\\d{8}|1\\d{4,7}",[5,6,7,8,9,10,12],[["(\\d{2})(\\d{3,4})","$1 $2",["16"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,4})","$1 $2 $3",["16"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["14|4"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["[2378]"],"(0$1)"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1(?:30|[89])"]]],"0",0,"(183[12])|0",0,0,0,[0,["4(?:79[01]|83[0-389]|94[0-4])\\d{5}|4(?:[0-36]\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]]],"0011"],AW:["297","00","(?:[25-79]\\d\\d|800)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[25-9]"]]],0,0,0,0,0,0,[0,["(?:290|5[69]\\d|6(?:[03]0|22|4[0-2]|[69]\\d)|7(?:[34]\\d|7[07])|9(?:6[45]|9[4-8]))\\d{4}"]]],AX:["358","00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))","2\\d{4,9}|35\\d{4,5}|(?:60\\d\\d|800)\\d{4,6}|7\\d{5,11}|(?:[14]\\d|3[0-46-9]|50)\\d{4,8}",[5,6,7,8,9,10,11,12],0,"0",0,0,0,0,"18",[0,["4946\\d{2,6}|(?:4[0-8]|50)\\d{4,8}",[6,7,8,9,10]]],"00"],AZ:["994","00","365\\d{6}|(?:[124579]\\d|60|88)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["90"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[28]|2|365|46","1[28]|2|365[45]|46","1[28]|2|365(?:4|5[02])|46"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[13-9]"],"0$1"]],"0",0,0,0,0,0,[0,["36554\\d{4}|(?:[16]0|4[04]|5[015]|7[07]|99)\\d{7}"]]],BA:["387","00","6\\d{8}|(?:[35689]\\d|49|70)\\d{6}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["6[1-3]|[7-9]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2-$3",["[3-5]|6[56]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["6"],"0$1"]],"0",0,0,0,0,0,[0,["6040\\d{5}|6(?:03|[1-356]|44|7\\d)\\d{6}"]]],BB:["1","011","(?:246|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","246$1",0,"246",[0,["246(?:(?:2(?:[3568]\\d|4[0-57-9])|3(?:5[2-9]|6[0-6])|4(?:46|5\\d)|69[5-7]|8(?:[2-5]\\d|83))\\d|52(?:1[147]|20))\\d{3}"]]],BD:["880","00","[1-469]\\d{9}|8[0-79]\\d{7,8}|[2-79]\\d{8}|[2-9]\\d{7}|[3-9]\\d{6}|[57-9]\\d{5}",[6,7,8,9,10],[["(\\d{2})(\\d{4,6})","$1-$2",["31[5-8]|[459]1"],"0$1"],["(\\d{3})(\\d{3,7})","$1-$2",["3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]"],"0$1"],["(\\d{4})(\\d{3,6})","$1-$2",["[13-9]|22"],"0$1"],["(\\d)(\\d{7,8})","$1-$2",["2"],"0$1"]],"0",0,0,0,0,0,[0,["(?:1[13-9]\\d|644)\\d{7}|(?:3[78]|44|66)[02-9]\\d{7}",[10]]]],BE:["32","00","4\\d{8}|[1-9]\\d{7}",[8,9],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["(?:80|9)0"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[239]|4[23]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[15-8]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["4"],"0$1"]],"0",0,0,0,0,0,[0,["4[5-9]\\d{7}",[9]]]],BF:["226","00","[025-7]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[025-7]"]]],0,0,0,0,0,0,[0,["(?:0[1-35-7]|5[0-8]|[67]\\d)\\d{6}"]]],BG:["359","00","00800\\d{7}|[2-7]\\d{6,7}|[89]\\d{6,8}|2\\d{5}",[6,7,8,9,12],[["(\\d)(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["2"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["43[1-6]|70[1-9]"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["(?:70|8)0"],"0$1"],["(\\d{3})(\\d{3})(\\d{2})","$1 $2 $3",["43[1-7]|7"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[48]|9[08]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[0,["(?:43[07-9]|99[69]\\d)\\d{5}|(?:8[7-9]|98)\\d{7}",[8,9]]]],BH:["973","00","[136-9]\\d{7}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[13679]|8[02-4679]"]]],0,0,0,0,0,0,[0,["(?:3(?:[0-79]\\d|8[0-57-9])\\d|6(?:3(?:00|33|6[16])|441|6(?:3[03-9]|[69]\\d|7[0-689])))\\d{4}"]]],BI:["257","00","(?:[267]\\d|31)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2367]"]]],0,0,0,0,0,0,[0,["(?:29|[67][125-9])\\d{6}"]]],BJ:["229","00","[24-689]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24-689]"]]],0,0,0,0,0,0,[0,["(?:4[0-7]|[56]\\d|9[013-9])\\d{6}"]]],BL:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],0,"0",0,0,0,0,0,[0,["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"]]],BM:["1","011","(?:441|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","441$1",0,"441",[0,["441(?:[2378]\\d|5[0-39]|9[02])\\d{5}"]]],BN:["673","00","[2-578]\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-578]"]]],0,0,0,0,0,0,[0,["(?:22[89]|[78]\\d\\d)\\d{4}"]]],BO:["591","00(?:1\\d)?","(?:[2-467]\\d\\d|8001)\\d{5}",[8,9],[["(\\d)(\\d{7})","$1 $2",["[23]|4[46]"]],["(\\d{8})","$1",["[67]"]],["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["8"]]],"0",0,"0(1\\d)?",0,0,0,[0,["[67]\\d{7}",[8]]]],BQ:["599","00","(?:[34]1|7\\d)\\d{5}",[7],0,0,0,0,0,0,"[347]",[0,["(?:31(?:8[14-8]|9[14578])|416[14-9]|7(?:0[01]|7[07]|8\\d|9[056])\\d)\\d{3}"]]],BR:["55","00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)","(?:[1-46-9]\\d\\d|5(?:[0-46-9]\\d|5[0-46-9]))\\d{8}|[1-9]\\d{9}|[3589]\\d{8}|[34]\\d{7}",[8,9,10,11],[["(\\d{4})(\\d{4})","$1-$2",["300|4(?:0[02]|37)","4(?:02|37)0|[34]00"]],["(\\d{3})(\\d{2,3})(\\d{4})","$1 $2 $3",["(?:[358]|90)0"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2-$3",["(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]"],"($1)"],["(\\d{2})(\\d{5})(\\d{4})","$1 $2-$3",["[16][1-9]|[2-57-9]"],"($1)"]],"0",0,"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\d{10,11}))?","$2",0,0,[0,["(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])(?:7|9\\d)\\d{7}",[10,11]]]],BS:["1","011","(?:242|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([3-8]\\d{6})$|1","242$1",0,"242",[0,["242(?:3(?:5[79]|7[56]|95)|4(?:[23][1-9]|4[1-35-9]|5[1-8]|6[2-8]|7\\d|81)|5(?:2[45]|3[35]|44|5[1-46-9]|65|77)|6[34]6|7(?:27|38)|8(?:0[1-9]|1[02-9]|2\\d|3[0-4]|[89]9))\\d{4}"]]],BT:["975","00","[17]\\d{7}|[2-8]\\d{6}",[7,8],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[2-68]|7[246]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[67]|7"]]],0,0,0,0,0,0,[0,["(?:1[67]|77)\\d{6}",[8]]]],BW:["267","00","(?:0800|(?:[37]|800)\\d)\\d{6}|(?:[2-6]\\d|90)\\d{5}",[7,8,10],[["(\\d{2})(\\d{5})","$1 $2",["90"]],["(\\d{3})(\\d{4})","$1 $2",["[24-6]|3[15-9]"]],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[37]"]],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["0"]],["(\\d{3})(\\d{4})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["(?:321|7[1-8]\\d)\\d{5}",[8]]]],BY:["375","810","(?:[12]\\d|33|44|902)\\d{7}|8(?:0[0-79]\\d{5,7}|[1-7]\\d{9})|8(?:1[0-489]|[5-79]\\d)\\d{7}|8[1-79]\\d{6,7}|8[0-79]\\d{5}|8\\d{5}",[6,7,8,9,10,11],[["(\\d{3})(\\d{3})","$1 $2",["800"],"8 $1"],["(\\d{3})(\\d{2})(\\d{2,4})","$1 $2 $3",["800"],"8 $1"],["(\\d{4})(\\d{2})(\\d{3})","$1 $2-$3",["1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])","1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])"],"8 0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2-$3-$4",["1(?:[56]|7[467])|2[1-3]"],"8 0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2-$3-$4",["[1-4]"],"8 0$1"],["(\\d{3})(\\d{3,4})(\\d{4})","$1 $2 $3",["[89]"],"8 $1"]],"8",0,"0|80?",0,0,0,[0,["(?:2(?:5[5-79]|9[1-9])|(?:33|44)\\d)\\d{6}",[9]]],"8~10"],BZ:["501","00","(?:0800\\d|[2-8])\\d{6}",[7,11],[["(\\d{3})(\\d{4})","$1-$2",["[2-8]"]],["(\\d)(\\d{3})(\\d{4})(\\d{3})","$1-$2-$3-$4",["0"]]],0,0,0,0,0,0,[0,["6[0-35-7]\\d{5}",[7]]]],CA:["1","011","(?:[2-8]\\d|90)\\d{8}|3\\d{6}",[7,10],0,"1",0,0,0,0,0,[0,["(?:2(?:04|[23]6|[48]9|50|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|7[39])|90[25])[2-9]\\d{6}",[10]]]],CC:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{8}(?:\\d{2})?|8[0-24-9]\\d{7})|[148]\\d{8}|1\\d{5,7}",[6,7,8,9,10,12],0,"0",0,"([59]\\d{7})$|0","8$1",0,0,[0,["4(?:79[01]|83[0-389]|94[0-4])\\d{5}|4(?:[0-36]\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]]],"0011"],CD:["243","00","[189]\\d{8}|[1-68]\\d{6}",[7,9],[["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["88"],"0$1"],["(\\d{2})(\\d{5})","$1 $2",["[1-6]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[0,["88\\d{5}|(?:8[0-69]|9[017-9])\\d{7}"]]],CF:["236","00","(?:[27]\\d{3}|8776)\\d{4}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[278]"]]],0,0,0,0,0,0,[0,["7[024-7]\\d{6}"]]],CG:["242","00","222\\d{6}|(?:0\\d|80)\\d{7}",[9],[["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["8"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[02]"]]],0,0,0,0,0,0,[0,["026(?:1[0-5]|6[6-9])\\d{4}|0(?:[14-6]\\d\\d|2(?:40|5[5-8]|6[07-9]))\\d{5}"]]],CH:["41","00","8\\d{11}|[2-9]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8[047]|90"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-79]|81"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["8"],"0$1"]],"0",0,0,0,0,0,[0,["(?:6[89]|7[235-9])\\d{7}"]]],CI:["225","00","[02]\\d{9}",[10],[["(\\d{2})(\\d{2})(\\d)(\\d{5})","$1 $2 $3 $4",["2"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3 $4",["0"]]],0,0,0,0,0,0,[0,["0[157]\\d{8}"]]],CK:["682","00","[2-578]\\d{4}",[5],[["(\\d{2})(\\d{3})","$1 $2",["[2-578]"]]],0,0,0,0,0,0,[0,["[578]\\d{4}"]]],CL:["56","(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0","12300\\d{6}|6\\d{9,10}|[2-9]\\d{8}",[9,10,11],[["(\\d{5})(\\d{4})","$1 $2",["219","2196"],"($1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["44"]],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2[1-36]"],"($1)"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["9[2-9]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])"],"($1)"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["60|8"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]],["(\\d{3})(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3 $4",["60"]]],0,0,0,0,0,0,[0,["2(?:1982[0-6]|3314[05-9])\\d{3}|(?:2(?:1(?:160|962)|3(?:2\\d\\d|3(?:[03467]\\d|1[0-35-9]|2[1-9]|5[0-24-9]|8[0-3])|600)|646[59])|80[1-9]\\d\\d|9(?:3(?:[0-57-9]\\d\\d|6(?:0[02-9]|[1-9]\\d))|6(?:[0-8]\\d\\d|9(?:[02-79]\\d|1[05-9]))|7[1-9]\\d\\d|9(?:[03-9]\\d\\d|1(?:[0235-9]\\d|4[0-24-9])|2(?:[0-79]\\d|8[0-46-9]))))\\d{4}|(?:22|3[2-5]|[47][1-35]|5[1-3578]|6[13-57]|8[1-9]|9[2458])\\d{7}",[9]]]],CM:["237","00","[26]\\d{8}|88\\d{6,7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["88"]],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["[26]|88"]]],0,0,0,0,0,0,[0,["(?:24[23]|6[25-9]\\d)\\d{6}",[9]]]],CN:["86","00|1(?:[12]\\d|79)\\d\\d00","1[127]\\d{8,9}|2\\d{9}(?:\\d{2})?|[12]\\d{6,7}|86\\d{6}|(?:1[03-689]\\d|6)\\d{7,9}|(?:[3-579]\\d|8[0-57-9])\\d{6,9}",[7,8,9,10,11,12],[["(\\d{2})(\\d{5,6})","$1 $2",["(?:10|2[0-57-9])[19]","(?:10|2[0-57-9])(?:10|9[56])","10(?:10|9[56])|2[0-57-9](?:100|9[56])"],"0$1"],["(\\d{3})(\\d{5,6})","$1 $2",["3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]","(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))[19]","85[23](?:10|95)|(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))(?:10|9[56])","85[23](?:100|95)|(?:3(?:[157]\\d|35|49|9[1-68])|4(?:[17]\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\d|4[13]|5[1-5]))(?:100|9[56])"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["(?:4|80)0"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["10|2(?:[02-57-9]|1[1-9])","10|2(?:[02-57-9]|1[1-9])","10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]"],"0$1",1],["(\\d{3})(\\d{7,8})","$1 $2",["9"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["80"],"0$1",1],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["[3-578]"],"0$1",1],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["1[3-9]"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3 $4",["[12]"],"0$1",1]],"0",0,"(1(?:[12]\\d|79)\\d\\d)|0",0,0,0,[0,["1740[0-5]\\d{6}|1(?:[38]\\d|4[57]|[59][0-35-9]|6[25-7]|7[0-35-8])\\d{8}",[11]]],"00"],CO:["57","00(?:4(?:[14]4|56)|[579])","(?:60\\d\\d|9101)\\d{6}|(?:1\\d|3)\\d{9}",[10,11],[["(\\d{3})(\\d{7})","$1 $2",["6"],"($1)"],["(\\d{3})(\\d{7})","$1 $2",["3[0-357]|91"]],["(\\d)(\\d{3})(\\d{7})","$1-$2-$3",["1"],"0$1",0,"$1 $2 $3"]],"0",0,"0([3579]|4(?:[14]4|56))?",0,0,0,[0,["333301[0-5]\\d{3}|3333(?:00|2[5-9]|[3-9]\\d)\\d{4}|(?:3(?:24[1-9]|3(?:00|3[0-24-9]))|9101)\\d{6}|3(?:0[0-5]|1\\d|2[0-3]|5[01]|70)\\d{7}",[10]]]],CR:["506","00","(?:8\\d|90)\\d{8}|(?:[24-8]\\d{3}|3005)\\d{4}",[8,10],[["(\\d{4})(\\d{4})","$1 $2",["[2-7]|8[3-9]"]],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["[89]"]]],0,0,"(19(?:0[0-2468]|1[09]|20|66|77|99))",0,0,0,[0,["(?:3005\\d|6500[01])\\d{3}|(?:5[07]|6[0-4]|7[0-3]|8[3-9])\\d{6}",[8]]]],CU:["53","119","(?:[2-7]|8\\d\\d)\\d{7}|[2-47]\\d{6}|[34]\\d{5}",[6,7,8,10],[["(\\d{2})(\\d{4,6})","$1 $2",["2[1-4]|[34]"],"(0$1)"],["(\\d)(\\d{6,7})","$1 $2",["7"],"(0$1)"],["(\\d)(\\d{7})","$1 $2",["[56]"],"0$1"],["(\\d{3})(\\d{7})","$1 $2",["8"],"0$1"]],"0",0,0,0,0,0,[0,["(?:5\\d|6[2-4])\\d{6}",[8]]]],CV:["238","0","(?:[2-59]\\d\\d|800)\\d{4}",[7],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[2-589]"]]],0,0,0,0,0,0,[0,["(?:36|5[1-389]|9\\d)\\d{5}"]]],CW:["599","00","(?:[34]1|60|(?:7|9\\d)\\d)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["[3467]"]],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["9[4-8]"]]],0,0,0,0,0,"[69]",[0,["953[01]\\d{4}|9(?:5[12467]|6[5-9])\\d{5}"]]],CX:["61","001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011","1(?:[0-79]\\d{8}(?:\\d{2})?|8[0-24-9]\\d{7})|[148]\\d{8}|1\\d{5,7}",[6,7,8,9,10,12],0,"0",0,"([59]\\d{7})$|0","8$1",0,0,[0,["4(?:79[01]|83[0-389]|94[0-4])\\d{5}|4(?:[0-36]\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\d{6}",[9]]],"0011"],CY:["357","00","(?:[279]\\d|[58]0)\\d{6}",[8],[["(\\d{2})(\\d{6})","$1 $2",["[257-9]"]]],0,0,0,0,0,0,[0,["9(?:10|[4-79]\\d)\\d{5}"]]],CZ:["420","00","(?:[2-578]\\d|60)\\d{7}|9\\d{8,11}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[2-8]|9[015-7]"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{2})","$1 $2 $3 $4",["96"]],["(\\d{2})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]]],0,0,0,0,0,0,[0,["(?:60[1-8]\\d|7(?:0(?:[2-5]\\d|60)|190|[2379]\\d\\d))\\d{5}"]]],DE:["49","00","[2579]\\d{5,14}|49(?:[34]0|69|8\\d)\\d\\d?|49(?:37|49|60|7[089]|9\\d)\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\d{1,8}|(?:1|[368]\\d|4[0-8])\\d{3,13}|49(?:[015]\\d|2[13]|31|[46][1-8])\\d{1,9}",[4,5,6,7,8,9,10,11,12,13,14,15],[["(\\d{2})(\\d{3,13})","$1 $2",["3[02]|40|[68]9"],"0$1"],["(\\d{3})(\\d{3,12})","$1 $2",["2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1","2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1"],"0$1"],["(\\d{4})(\\d{2,11})","$1 $2",["[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]","[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["138"],"0$1"],["(\\d{5})(\\d{2,10})","$1 $2",["3"],"0$1"],["(\\d{3})(\\d{5,11})","$1 $2",["181"],"0$1"],["(\\d{3})(\\d)(\\d{4,10})","$1 $2 $3",["1(?:3|80)|9"],"0$1"],["(\\d{3})(\\d{7,8})","$1 $2",["1[67]"],"0$1"],["(\\d{3})(\\d{7,12})","$1 $2",["8"],"0$1"],["(\\d{5})(\\d{6})","$1 $2",["185","1850","18500"],"0$1"],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{4})(\\d{7})","$1 $2",["18[68]"],"0$1"],["(\\d{4})(\\d{7})","$1 $2",["15[1279]"],"0$1"],["(\\d{5})(\\d{6})","$1 $2",["15[03568]","15(?:[0568]|31)"],"0$1"],["(\\d{3})(\\d{8})","$1 $2",["18"],"0$1"],["(\\d{3})(\\d{2})(\\d{7,8})","$1 $2 $3",["1(?:6[023]|7)"],"0$1"],["(\\d{4})(\\d{2})(\\d{7})","$1 $2 $3",["15[279]"],"0$1"],["(\\d{3})(\\d{2})(\\d{8})","$1 $2 $3",["15"],"0$1"]],"0",0,0,0,0,0,[0,["15(?:[0-25-9]\\d\\d|310)\\d{6}|1(?:6[023]|7\\d)\\d{7,8}",[10,11]]]],DJ:["253","00","(?:2\\d|77)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[27]"]]],0,0,0,0,0,0,[0,["77\\d{6}"]]],DK:["45","00","[2-9]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-9]"]]],0,0,0,0,0,0,[0,["(?:[2-7]\\d|8[126-9]|9[1-46-9])\\d{6}"]]],DM:["1","011","(?:[58]\\d\\d|767|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","767$1",0,"767",[0,["767(?:2(?:[2-4689]5|7[5-7])|31[5-7]|61[1-8]|70[1-6])\\d{4}"]]],DO:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,0,0,0,"8001|8[024]9",[0,["8[024]9[2-9]\\d{6}"]]],DZ:["213","00","(?:[1-4]|[5-79]\\d|80)\\d{7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[1-4]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["9"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-8]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:5(?:4[0-29]|5\\d|6[0-2])|6(?:[569]\\d|7[0-6])|7[7-9]\\d)\\d{6}",[9]]]],EC:["593","00","1\\d{9,10}|(?:[2-7]|9\\d)\\d{7}",[8,9,10,11],[["(\\d)(\\d{3})(\\d{4})","$1 $2-$3",["[2-7]"],"(0$1)",0,"$1-$2-$3"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["9"],"0$1"],["(\\d{4})(\\d{3})(\\d{3,4})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[0,["964[0-2]\\d{5}|9(?:39|[57][89]|6[0-36-9]|[89]\\d)\\d{6}",[9]]]],EE:["372","00","8\\d{9}|[4578]\\d{7}|(?:[3-8]\\d|90)\\d{5}",[7,8,10],[["(\\d{3})(\\d{4})","$1 $2",["[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88","[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88"]],["(\\d{4})(\\d{3,4})","$1 $2",["[45]|8(?:00|[1-49])","[45]|8(?:00[1-9]|[1-49])"]],["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["7"]],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["(?:5\\d{5}|8(?:1(?:0(?:0(?:00|[178]\\d)|[3-9]\\d\\d)|(?:1(?:0[236]|1\\d)|(?:2[0-59]|[3-79]\\d)\\d)\\d)|2(?:0(?:0(?:00|4\\d)|(?:19|[2-7]\\d)\\d)|(?:(?:[124-6]\\d|3[5-9])\\d|7(?:[0-79]\\d|8[13-9])|8(?:[2-6]\\d|7[01]))\\d)|[349]\\d{4}))\\d\\d|5(?:(?:[02]\\d|5[0-478])\\d|1(?:[0-8]\\d|95)|6(?:4[0-4]|5[1-589]))\\d{3}",[7,8]]]],EG:["20","00","[189]\\d{8,9}|[24-6]\\d{8}|[135]\\d{7}",[8,9,10],[["(\\d)(\\d{7,8})","$1 $2",["[23]"],"0$1"],["(\\d{2})(\\d{6,7})","$1 $2",["1[35]|[4-6]|8[2468]|9[235-7]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{2})(\\d{8})","$1 $2",["1"],"0$1"]],"0",0,0,0,0,0,[0,["1[0-25]\\d{8}",[10]]]],EH:["212","00","[5-8]\\d{8}",[9],0,"0",0,0,0,0,"528[89]",[0,["(?:6(?:[0-79]\\d|8[0-247-9])|7(?:[0167]\\d|2[0-4]|5[01]|8[0-3]))\\d{6}"]]],ER:["291","00","[178]\\d{6}",[7],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[178]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:17[1-3]|7\\d\\d)\\d{4}"]]],ES:["34","00","[5-9]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[89]00"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-9]"]]],0,0,0,0,0,0,[0,["(?:590[16]00\\d|9(?:6906(?:09|10)|7390\\d\\d))\\d\\d|(?:6\\d|7[1-48])\\d{7}"]]],ET:["251","00","(?:11|[2-579]\\d)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-579]"],"0$1"]],"0",0,0,0,0,0,[0,["700[1-9]\\d{5}|(?:7(?:0[1-9]|1[0-8]|22|77|86|99)|9\\d\\d)\\d{6}"]]],FI:["358","00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))","[1-35689]\\d{4}|7\\d{10,11}|(?:[124-7]\\d|3[0-46-9])\\d{8}|[1-9]\\d{5,8}",[5,6,7,8,9,10,11,12],[["(\\d{5})","$1",["20[2-59]"],"0$1"],["(\\d{3})(\\d{3,7})","$1 $2",["(?:[1-3]0|[68])0|70[07-9]"],"0$1"],["(\\d{2})(\\d{4,8})","$1 $2",["[14]|2[09]|50|7[135]"],"0$1"],["(\\d{2})(\\d{6,10})","$1 $2",["7"],"0$1"],["(\\d)(\\d{4,9})","$1 $2",["(?:1[3-79]|[2568])[1-8]|3(?:0[1-9]|[1-9])|9"],"0$1"]],"0",0,0,0,0,"1[03-79]|[2-9]",[0,["4946\\d{2,6}|(?:4[0-8]|50)\\d{4,8}",[6,7,8,9,10]]],"00"],FJ:["679","0(?:0|52)","45\\d{5}|(?:0800\\d|[235-9])\\d{6}",[7,11],[["(\\d{3})(\\d{4})","$1 $2",["[235-9]|45"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["0"]]],0,0,0,0,0,0,[0,["(?:[279]\\d|45|5[01568]|8[034679])\\d{5}",[7]]],"00"],FK:["500","00","[2-7]\\d{4}",[5],0,0,0,0,0,0,0,[0,["[56]\\d{4}"]]],FM:["691","00","(?:[39]\\d\\d|820)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[389]"]]],0,0,0,0,0,0,[0,["31(?:00[67]|208|309)\\d\\d|(?:3(?:[2357]0[1-9]|602|804|905)|(?:820|9[2-7]\\d)\\d)\\d{3}"]]],FO:["298","00","[2-9]\\d{5}",[6],[["(\\d{6})","$1",["[2-9]"]]],0,0,"(10(?:01|[12]0|88))",0,0,0,[0,["(?:[27][1-9]|5\\d|9[16])\\d{4}"]]],FR:["33","00","[1-9]\\d{8}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0 $1"],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["[1-79]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:6(?:[0-24-8]\\d|3[0-8]|9[589])|7[3-9]\\d)\\d{6}"]]],GA:["241","00","(?:[067]\\d|11)\\d{6}|[2-7]\\d{6}",[7,8],[["(\\d)(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-7]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["0"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["11|[67]"],"0$1"]],0,0,"0(11\\d{6}|60\\d{6}|61\\d{6}|6[256]\\d{6}|7[467]\\d{6})","$1",0,0,[0,["(?:(?:0[2-7]|7[467])\\d|6(?:0[0-4]|10|[256]\\d))\\d{5}|[2-7]\\d{6}"]]],GB:["44","00","[1-357-9]\\d{9}|[18]\\d{8}|8\\d{6}",[7,9,10],[["(\\d{3})(\\d{4})","$1 $2",["800","8001","80011","800111","8001111"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["845","8454","84546","845464"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["800"],"0$1"],["(\\d{5})(\\d{4,5})","$1 $2",["1(?:38|5[23]|69|76|94)","1(?:(?:38|69)7|5(?:24|39)|768|946)","1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)"],"0$1"],["(\\d{4})(\\d{5,6})","$1 $2",["1(?:[2-69][02-9]|[78])"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["[25]|7(?:0|6[02-9])","[25]|7(?:0|6(?:[03-9]|2[356]))"],"0$1"],["(\\d{4})(\\d{6})","$1 $2",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[1389]"],"0$1"]],"0",0,0,0,0,0,[0,["7(?:457[0-57-9]|700[01]|911[028])\\d{5}|7(?:[1-3]\\d\\d|4(?:[0-46-9]\\d|5[0-689])|5(?:0[0-8]|[13-9]\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\d|8[02-9]|9[0-689])|8(?:[014-9]\\d|[23][0-8])|9(?:[024-9]\\d|1[02-9]|3[0-689]))\\d{6}",[10]]],0," x"],GD:["1","011","(?:473|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","473$1",0,"473",[0,["473(?:4(?:0[2-79]|1[04-9]|2[0-5]|49|5[68])|5(?:2[01]|3[3-8])|901)\\d{4}"]]],GE:["995","00","(?:[3-57]\\d\\d|800)\\d{6}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["70"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["32"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[57]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[348]"],"0$1"]],"0",0,0,0,0,0,[0,["5(?:(?:(?:0555|1(?:[17]77|555))[5-9]|757(?:7[7-9]|8[01]))\\d|22252[0-4])\\d\\d|(?:5(?:0(?:0(?:0\\d|11|22|3[0-6]|44|5[05]|77|88|9[09])|111|77\\d)|1(?:1(?:[03][01]|[124]\\d)|4\\d\\d)|[23]555|4(?:4\\d\\d|555)|5(?:[0157-9]\\d\\d|200)|6[89]\\d\\d|7(?:[0147-9]\\d\\d|5(?:00|[57]5))|8(?:0(?:[018]\\d|2[0-4])|5(?:55|8[89])|8(?:55|88))|9(?:090|[1-35-9]\\d\\d))|790\\d\\d)\\d{4}|5(?:0(?:0[17]0|505)|1(?:0[01]0|1(?:07|33|51))|2(?:0[02]0|2[25]2)|3(?:0[03]0|3[35]3)|(?:40[04]|900)0|5222)[0-4]\\d{3}"]]],GF:["594","00","[56]94\\d{6}|(?:80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[56]|9[47]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[89]"],"0$1"]],"0",0,0,0,0,0,[0,["694(?:[0-249]\\d|3[0-8])\\d{4}"]]],GG:["44","00","(?:1481|[357-9]\\d{3})\\d{6}|8\\d{6}(?:\\d{2})?",[7,9,10],0,"0",0,"([25-9]\\d{5})$|0","1481$1",0,0,[0,["7(?:(?:781|839)\\d|911[17])\\d{5}",[10]]]],GH:["233","00","(?:[235]\\d{3}|800)\\d{5}",[8,9],[["(\\d{3})(\\d{5})","$1 $2",["8"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[235]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:2(?:[0346-9]\\d|5[67])|5(?:[03-7]\\d|9[1-9]))\\d{6}",[9]]]],GI:["350","00","(?:[25]\\d|60)\\d{6}",[8],[["(\\d{3})(\\d{5})","$1 $2",["2"]]],0,0,0,0,0,0,[0,["5251[0-4]\\d{3}|(?:5(?:[146-8]\\d\\d|250)|60(?:1[01]|6\\d))\\d{4}"]]],GL:["299","00","(?:19|[2-689]\\d|70)\\d{4}",[6],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["19|[2-9]"]]],0,0,0,0,0,0,[0,["[245]\\d{5}"]]],GM:["220","00","[2-9]\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[0,["(?:[23679]\\d|5[0-489])\\d{5}"]]],GN:["224","00","722\\d{6}|(?:3|6\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["3"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[67]"]]],0,0,0,0,0,0,[0,["6[0-356]\\d{7}",[9]]]],GP:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[569]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[0,["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"]]],GQ:["240","00","222\\d{6}|(?:3\\d|55|[89]0)\\d{7}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[235]"]],["(\\d{3})(\\d{6})","$1 $2",["[89]"]]],0,0,0,0,0,0,[0,["(?:222|55\\d)\\d{6}"]]],GR:["30","00","5005000\\d{3}|8\\d{9,11}|(?:[269]\\d|70)\\d{8}",[10,11,12],[["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["21|7"]],["(\\d{4})(\\d{6})","$1 $2",["2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[2689]"]],["(\\d{3})(\\d{3,4})(\\d{5})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["68[57-9]\\d{7}|(?:69|94)\\d{8}",[10]]]],GT:["502","00","80\\d{6}|(?:1\\d{3}|[2-7])\\d{7}",[8,11],[["(\\d{4})(\\d{4})","$1 $2",["[2-8]"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[0,["(?:[3-5]\\d\\d|80[0-4])\\d{5}",[8]]]],GU:["1","011","(?:[58]\\d\\d|671|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","671$1",0,"671",[0,["671(?:2\\d\\d|3(?:00|3[39]|4[349]|55|6[26])|4(?:00|56|7[1-9]|8[02-46-9])|5(?:55|6[2-5]|88)|6(?:3[2-578]|4[24-9]|5[34]|78|8[235-9])|7(?:[0479]7|2[0167]|3[45]|8[7-9])|8(?:[2-57-9]8|6[48])|9(?:2[29]|6[79]|7[1279]|8[7-9]|9[78]))\\d{4}"]]],GW:["245","00","[49]\\d{8}|4\\d{6}",[7,9],[["(\\d{3})(\\d{4})","$1 $2",["40"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[49]"]]],0,0,0,0,0,0,[0,["9(?:5\\d|6[569]|77)\\d{6}",[9]]]],GY:["592","001","(?:[2-8]\\d{3}|9008)\\d{3}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[0,["(?:510|6\\d\\d|7(?:[013]\\d|2[0-25-8]|4[0-29]|5[1-9]))\\d{4}"]]],HK:["852","00(?:30|5[09]|[126-9]?)","8[0-46-9]\\d{6,7}|9\\d{4,7}|(?:[2-7]|9\\d{3})\\d{7}",[5,6,7,8,9,11],[["(\\d{3})(\\d{2,5})","$1 $2",["900","9003"]],["(\\d{4})(\\d{4})","$1 $2",["[2-7]|8[1-4]|9(?:0[1-9]|[1-8])"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]],["(\\d{3})(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3 $4",["9"]]],0,0,0,0,0,0,[0,["(?:4(?:44[0-25-9]|6(?:1[0-7]|4[0-57-9]|6[0-4]))|5(?:73[0-6]|95[0-8])|6(?:26[013-8]|66[0-3])|70(?:7[1-8]|8[0-4])|848[0-35-9]|9(?:29[013-9]|39[01]|59[0-4]|899))\\d{4}|(?:4(?:4[0-35-8]|6[02357-9])|5(?:[1-59][0-46-9]|6[0-4689]|7[0-246-9])|6(?:0[1-9]|[13-59]\\d|[268][0-57-9]|7[0-79])|70[129]|84[0-29]|9(?:0[1-9]|1[02-9]|[2358][0-8]|[467]\\d))\\d{5}",[8]]],"00"],HN:["504","00","8\\d{10}|[237-9]\\d{7}",[8,11],[["(\\d{4})(\\d{4})","$1-$2",["[237-9]"]]],0,0,0,0,0,0,[0,["[37-9]\\d{7}",[8]]]],HR:["385","00","(?:[24-69]\\d|3[0-79])\\d{7}|80\\d{5,7}|[1-79]\\d{7}|6\\d{5,6}",[6,7,8,9],[["(\\d{2})(\\d{2})(\\d{2,3})","$1 $2 $3",["6[01]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{4})(\\d{3})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["6|7[245]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["9"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-57]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[0,["9(?:(?:0[1-9]|[12589]\\d)\\d\\d|7(?:[0679]\\d\\d|5(?:[01]\\d|44|55|77|9[5-7])))\\d{4}|98\\d{6}",[8,9]]]],HT:["509","00","(?:[2-489]\\d|55)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["[2-589]"]]],0,0,0,0,0,0,[0,["(?:[34]\\d|55)\\d{6}"]]],HU:["36","00","[235-7]\\d{8}|[1-9]\\d{7}",[8,9],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["1"],"(06 $1)"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]"],"(06 $1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-9]"],"06 $1"]],"06",0,0,0,0,0,[0,["(?:[257]0|3[01])\\d{7}",[9]]]],ID:["62","00[89]","(?:(?:00[1-9]|8\\d)\\d{4}|[1-36])\\d{6}|00\\d{10}|[1-9]\\d{8,10}|[2-9]\\d{7}",[7,8,9,10,11,12,13],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["15"]],["(\\d{2})(\\d{5,9})","$1 $2",["2[124]|[36]1"],"(0$1)"],["(\\d{3})(\\d{5,7})","$1 $2",["800"],"0$1"],["(\\d{3})(\\d{5,8})","$1 $2",["[2-79]"],"(0$1)"],["(\\d{3})(\\d{3,4})(\\d{3})","$1-$2-$3",["8[1-35-9]"],"0$1"],["(\\d{3})(\\d{6,8})","$1 $2",["1"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["804"],"0$1"],["(\\d{3})(\\d)(\\d{3})(\\d{3})","$1 $2 $3 $4",["80"],"0$1"],["(\\d{3})(\\d{4})(\\d{4,5})","$1-$2-$3",["8"],"0$1"]],"0",0,0,0,0,0,[0,["8[1-35-9]\\d{7,10}",[9,10,11,12]]]],IE:["353","00","(?:1\\d|[2569])\\d{6,8}|4\\d{6,9}|7\\d{8}|8\\d{8,9}",[7,8,9,10],[["(\\d{2})(\\d{5})","$1 $2",["2[24-9]|47|58|6[237-9]|9[35-9]"],"(0$1)"],["(\\d{3})(\\d{5})","$1 $2",["[45]0"],"(0$1)"],["(\\d)(\\d{3,4})(\\d{4})","$1 $2 $3",["1"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2569]|4[1-69]|7[14]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["70"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["81"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[78]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["4"],"(0$1)"],["(\\d{2})(\\d)(\\d{3})(\\d{4})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[0,["8(?:22|[35-9]\\d)\\d{6}",[9]]]],IL:["972","0(?:0|1[2-9])","1\\d{6}(?:\\d{3,5})?|[57]\\d{8}|[1-489]\\d{7}",[7,8,9,10,11,12],[["(\\d{4})(\\d{3})","$1-$2",["125"]],["(\\d{4})(\\d{2})(\\d{2})","$1-$2-$3",["121"]],["(\\d)(\\d{3})(\\d{4})","$1-$2-$3",["[2-489]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["[57]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1-$2-$3",["12"]],["(\\d{4})(\\d{6})","$1-$2",["159"]],["(\\d)(\\d{3})(\\d{3})(\\d{3})","$1-$2-$3-$4",["1[7-9]"]],["(\\d{3})(\\d{1,2})(\\d{3})(\\d{4})","$1-$2 $3-$4",["15"]]],"0",0,0,0,0,0,[0,["55410\\d{4}|5(?:(?:[02][02-9]|[149][2-9]|[36]\\d|8[3-7])\\d|5(?:01|2\\d|3[0-3]|4[34]|5[0-25689]|6[6-8]|7[0-267]|8[7-9]|9[1-9]))\\d{5}",[9]]]],IM:["44","00","1624\\d{6}|(?:[3578]\\d|90)\\d{8}",[10],0,"0",0,"([25-8]\\d{5})$|0","1624$1",0,"74576|(?:16|7[56])24",[0,["76245[06]\\d{4}|7(?:4576|[59]24\\d|624[0-4689])\\d{5}"]]],IN:["91","00","(?:000800|[2-9]\\d\\d)\\d{7}|1\\d{7,12}",[8,9,10,11,12,13],[["(\\d{8})","$1",["5(?:0|2[23]|3[03]|[67]1|88)","5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)","5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)"],0,1],["(\\d{4})(\\d{4,5})","$1 $2",["180","1800"],0,1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["140"],0,1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["11|2[02]|33|4[04]|79[1-7]|80[2-46]","11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])","11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])"],"0$1",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]","1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]","1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]"],"0$1",1],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807","1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]","1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\d|7(?:1(?:[013-8]\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\d|5[0-367])|70[13-7]))[2-7]"],"0$1",1],["(\\d{5})(\\d{5})","$1 $2",["[6-9]"],"0$1",1],["(\\d{4})(\\d{2,4})(\\d{4})","$1 $2 $3",["1(?:6|8[06])","1(?:6|8[06]0)"],0,1],["(\\d{4})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["18"],0,1]],"0",0,0,0,0,0,[0,["(?:61279|7(?:887[02-9]|9(?:313|79[07-9]))|8(?:079[04-9]|(?:84|91)7[02-8]))\\d{5}|(?:6(?:12|[2-47]1|5[17]|6[13]|80)[0189]|7(?:1(?:2[0189]|9[0-5])|2(?:[14][017-9]|8[0-59])|3(?:2[5-8]|[34][017-9]|9[016-9])|4(?:1[015-9]|[29][89]|39|8[389])|5(?:[15][017-9]|2[04-9]|9[7-9])|6(?:0[0-47]|1[0-257-9]|2[0-4]|3[19]|5[4589])|70[0289]|88[089]|97[02-8])|8(?:0(?:6[67]|7[02-8])|70[017-9]|84[01489]|91[0-289]))\\d{6}|(?:7(?:31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[0189]\\d|7[02-8])\\d{5}|(?:6(?:[09]\\d|1[04679]|2[03689]|3[05-9]|4[0489]|50|6[069]|7[07]|8[7-9])|7(?:0\\d|2[0235-79]|3[05-8]|40|5[0346-8]|6[6-9]|7[1-9]|8[0-79]|9[089])|8(?:0[01589]|1[0-57-9]|2[235-9]|3[03-57-9]|[45]\\d|6[02457-9]|7[1-69]|8[0-25-9]|9[02-9])|9\\d\\d)\\d{7}|(?:6(?:(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|8[124-6])\\d|7(?:[235689]\\d|4[0189]))|7(?:1(?:[013-8]\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-5])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\d|5[0-367])|70[13-7]|881))[0189]\\d{5}",[10]]]],IO:["246","00","3\\d{6}",[7],[["(\\d{3})(\\d{4})","$1 $2",["3"]]],0,0,0,0,0,0,[0,["38\\d{5}"]]],IQ:["964","00","(?:1|7\\d\\d)\\d{7}|[2-6]\\d{7,8}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-6]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[0,["7[3-9]\\d{8}",[10]]]],IR:["98","00","[1-9]\\d{9}|(?:[1-8]\\d\\d|9)\\d{3,4}",[4,5,6,7,10],[["(\\d{4,5})","$1",["96"],"0$1"],["(\\d{2})(\\d{4,5})","$1 $2",["(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["9"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["[1-8]"],"0$1"]],"0",0,0,0,0,0,[0,["9(?:(?:0(?:[0-35]\\d|4[4-6])|(?:[13]\\d|2[0-3])\\d)\\d|9(?:[0-46]\\d\\d|5[15]0|8(?:[12]\\d|88)|9(?:0[0-3]|[19]\\d|21|69|77|8[7-9])))\\d{5}",[10]]]],IS:["354","00|1(?:0(?:01|[12]0)|100)","(?:38\\d|[4-9])\\d{6}",[7,9],[["(\\d{3})(\\d{4})","$1 $2",["[4-9]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["3"]]],0,0,0,0,0,0,[0,["(?:38[589]\\d\\d|6(?:1[1-8]|2[0-6]|3[026-9]|4[014679]|5[0159]|6[0-69]|70|8[06-8]|9\\d)|7(?:5[057]|[6-9]\\d)|8(?:2[0-59]|[3-69]\\d|8[238]))\\d{4}"]],"00"],IT:["39","00","0\\d{5,10}|1\\d{8,10}|3(?:[0-8]\\d{7,10}|9\\d{7,8})|(?:43|55|70)\\d{8}|8\\d{5}(?:\\d{2,4})?",[6,7,8,9,10,11],[["(\\d{2})(\\d{4,6})","$1 $2",["0[26]"]],["(\\d{3})(\\d{3,6})","$1 $2",["0[13-57-9][0159]|8(?:03|4[17]|9[2-5])","0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))"]],["(\\d{4})(\\d{2,6})","$1 $2",["0(?:[13-579][2-46-8]|8[236-8])"]],["(\\d{4})(\\d{4})","$1 $2",["894"]],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["0[26]|5"]],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["1(?:44|[679])|[378]|43"]],["(\\d{3})(\\d{3,4})(\\d{4})","$1 $2 $3",["0[13-57-9][0159]|14"]],["(\\d{2})(\\d{4})(\\d{5})","$1 $2 $3",["0[26]"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["0"]],["(\\d{3})(\\d{4})(\\d{4,5})","$1 $2 $3",["3"]]],0,0,0,0,0,0,[0,["3[2-9]\\d{7,8}|(?:31|43)\\d{8}",[9,10]]]],JE:["44","00","1534\\d{6}|(?:[3578]\\d|90)\\d{8}",[10],0,"0",0,"([0-24-8]\\d{5})$|0","1534$1",0,0,[0,["7(?:(?:(?:50|82)9|937)\\d|7(?:00[378]|97\\d))\\d{5}"]]],JM:["1","011","(?:[58]\\d\\d|658|900)\\d{7}",[10],0,"1",0,0,0,0,"658|876",[0,["(?:658295|876(?:2(?:0[1-9]|[13-9]\\d|2[013-9])|[348]\\d\\d|5(?:0[1-9]|[1-9]\\d)|6(?:4[89]|6[67])|7(?:0[07]|7\\d|8[1-47-9]|9[0-36-9])|9(?:[01]9|9[0579])))\\d{4}"]]],JO:["962","00","(?:(?:[2689]|7\\d)\\d|32|53)\\d{6}",[8,9],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2356]|87"],"(0$1)"],["(\\d{3})(\\d{5,6})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["70"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[0,["7(?:[78][0-25-9]|9\\d)\\d{6}",[9]]]],JP:["81","010","00[1-9]\\d{6,14}|[257-9]\\d{9}|(?:00|[1-9]\\d\\d)\\d{6}",[8,9,10,11,12,13,14,15,16,17],[["(\\d{3})(\\d{3})(\\d{3})","$1-$2-$3",["(?:12|57|99)0"],"0$1"],["(\\d{4})(\\d)(\\d{4})","$1-$2-$3",["1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])","1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]","1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["60"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1-$2-$3",["[36]|4(?:2[09]|7[01])","[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])","1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]","1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]"],"0$1"],["(\\d{3})(\\d{2})(\\d{4})","$1-$2-$3",["[14]|[289][2-9]|5[3-9]|7[2-4679]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1-$2-$3",["800"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2-$3",["[257-9]"],"0$1"]],"0",0,"(000[259]\\d{6})$|(?:(?:003768)0?)|0","$1",0,0,[0,["[7-9]0[1-9]\\d{7}",[10]]]],KE:["254","000","(?:[17]\\d\\d|900)\\d{6}|(?:2|80)0\\d{6,7}|[4-6]\\d{6,8}",[7,8,9,10],[["(\\d{2})(\\d{5,7})","$1 $2",["[24-6]"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["[17]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:1(?:0[0-8]|1[0-5]|2[014]|30)|7\\d\\d)\\d{6}",[9]]]],KG:["996","00","8\\d{9}|[235-9]\\d{8}",[9,10],[["(\\d{4})(\\d{5})","$1 $2",["3(?:1[346]|[24-79])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[235-79]|88"],"0$1"],["(\\d{3})(\\d{3})(\\d)(\\d{2,3})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[0,["312(?:58\\d|973)\\d{3}|(?:2(?:0[0-35]|2\\d)|5[0-24-7]\\d|600|7(?:[07]\\d|55)|88[08]|9(?:12|9[05-9]))\\d{6}",[9]]]],KH:["855","00[14-9]","1\\d{9}|[1-9]\\d{7,8}",[8,9,10],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-9]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[0,["(?:(?:1[28]|3[18]|9[67])\\d|6[016-9]|7(?:[07-9]|[16]\\d)|8(?:[013-79]|8\\d))\\d{6}|(?:1\\d|9[0-57-9])\\d{6}|(?:2[3-6]|3[2-6]|4[2-4]|[5-7][2-5])48\\d{5}",[8,9]]]],KI:["686","00","(?:[37]\\d|6[0-79])\\d{6}|(?:[2-48]\\d|50)\\d{3}",[5,8],0,"0",0,0,0,0,0,[0,["(?:6200[01]|7(?:310[1-9]|5(?:02[03-9]|12[0-47-9]|22[0-7]|[34](?:0[1-9]|8[02-9])|50[1-9])))\\d{3}|(?:63\\d\\d|7(?:(?:[0146-9]\\d|2[0-689])\\d|3(?:[02-9]\\d|1[1-9])|5(?:[0-2][013-9]|[34][1-79]|5[1-9]|[6-9]\\d)))\\d{4}",[8]]]],KM:["269","00","[3478]\\d{6}",[7],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[3478]"]]],0,0,0,0,0,0,[0,["[34]\\d{6}"]]],KN:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","869$1",0,"869",[0,["869(?:48[89]|55[6-8]|66\\d|76[02-7])\\d{4}"]]],KP:["850","00|99","85\\d{6}|(?:19\\d|[2-7])\\d{7}",[8,10],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2-7]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"]],"0",0,0,0,0,0,[0,["19[1-3]\\d{7}",[10]]]],KR:["82","00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))","00[1-9]\\d{8,11}|(?:[12]|5\\d{3})\\d{7}|[13-6]\\d{9}|(?:[1-6]\\d|80)\\d{7}|[3-6]\\d{4,5}|(?:00|7)0\\d{8}",[5,6,8,9,10,11,12,13,14],[["(\\d{2})(\\d{3,4})","$1-$2",["(?:3[1-3]|[46][1-4]|5[1-5])1"],"0$1"],["(\\d{4})(\\d{4})","$1-$2",["1"]],["(\\d)(\\d{3,4})(\\d{4})","$1-$2-$3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1-$2-$3",["60|8"],"0$1"],["(\\d{2})(\\d{3,4})(\\d{4})","$1-$2-$3",["[1346]|5[1-5]"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2-$3",["[57]"],"0$1"],["(\\d{2})(\\d{5})(\\d{4})","$1-$2-$3",["5"],"0$1"]],"0",0,"0(8(?:[1-46-8]|5\\d\\d))?",0,0,0,[0,["1(?:05(?:[0-8]\\d|9[0-6])|22[13]\\d)\\d{4,5}|1(?:0[0-46-9]|[16-9]\\d|2[013-9])\\d{6,7}",[9,10]]]],KW:["965","00","18\\d{5}|(?:[2569]\\d|41)\\d{6}",[7,8],[["(\\d{4})(\\d{3,4})","$1 $2",["[169]|2(?:[235]|4[1-35-9])|52"]],["(\\d{3})(\\d{5})","$1 $2",["[245]"]]],0,0,0,0,0,0,[0,["(?:41\\d\\d|5(?:(?:[05]\\d|1[0-7]|6[56])\\d|2(?:22|5[25])|7(?:55|77)|88[58])|6(?:(?:0[034679]|5[015-9]|6\\d)\\d|1(?:00|11|6[16])|2[26]2|3[36]3|4[46]4|7(?:0[013-9]|[67]\\d)|8[68]8|9(?:[069]\\d|3[039]))|9(?:(?:[04679]\\d|8[057-9])\\d|1(?:1[01]|99)|2(?:00|2\\d)|3(?:00|3[03])|5(?:00|5\\d)))\\d{4}",[8]]]],KY:["1","011","(?:345|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","345$1",0,"345",[0,["345(?:32[1-9]|42[0-4]|5(?:1[67]|2[5-79]|4[6-9]|50|76)|649|82[56]|9(?:1[679]|2[2-9]|3[06-9]|90))\\d{4}"]]],KZ:["7","810","(?:33622|8\\d{8})\\d{5}|[78]\\d{9}",[10,14],0,"8",0,0,0,0,"33|7",[0,["7(?:0[0-25-8]|47|6[0-4]|7[15-8]|85)\\d{7}",[10]]],"8~10"],LA:["856","00","[23]\\d{9}|3\\d{8}|(?:[235-8]\\d|41)\\d{6}",[8,9,10],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["2[13]|3[14]|[4-8]"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["30[013-9]"],"0$1"],["(\\d{2})(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[23]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:20(?:[23579]\\d|88)|30(?:2\\d|4))\\d{6}",[9,10]]]],LB:["961","00","[27-9]\\d{7}|[13-9]\\d{6}",[7,8],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[13-69]|7(?:[2-57]|62|8[0-7]|9[04-9])|8[02-9]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[27-9]"]]],"0",0,0,0,0,0,[0,["793(?:[01]\\d|2[0-4])\\d{3}|(?:(?:3|81)\\d|7(?:[01]\\d|6[013-9]|8[89]|9[12]))\\d{5}"]]],LC:["1","011","(?:[58]\\d\\d|758|900)\\d{7}",[10],0,"1",0,"([2-8]\\d{6})$|1","758$1",0,"758",[0,["758(?:28[4-7]|384|4(?:6[01]|8[4-9])|5(?:1[89]|20|84)|7(?:1[2-9]|2\\d|3[0-3])|812)\\d{4}"]]],LI:["423","00","[68]\\d{8}|(?:[2378]\\d|90)\\d{5}",[7,9],[["(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3",["[2379]|8(?:0[09]|7)","[2379]|8(?:0(?:02|9)|7)"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["69"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]]],"0",0,"(1001)|0",0,0,0,[0,["(?:6(?:(?:4[5-9]|5[0-469])\\d|6(?:[024-6]\\d|[17]0|3[7-9]))\\d|7(?:[37-9]\\d|42|56))\\d{4}"]]],LK:["94","00","[1-9]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[1-689]"],"0$1"]],"0",0,0,0,0,0,[0,["7(?:[0-25-8]\\d|4[0-4])\\d{6}"]]],LR:["231","00","(?:[245]\\d|33|77|88)\\d{7}|(?:2\\d|[4-6])\\d{6}",[7,8,9],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["4[67]|[56]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[2-578]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:(?:(?:22|33)0|555|(?:77|88)\\d)\\d|4(?:240|[67]))\\d{5}|[56]\\d{6}",[7,9]]]],LS:["266","00","(?:[256]\\d\\d|800)\\d{5}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[2568]"]]],0,0,0,0,0,0,[0,["[56]\\d{7}"]]],LT:["370","00","(?:[3469]\\d|52|[78]0)\\d{6}",[8],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["52[0-7]"],"(0-$1)",1],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[7-9]"],"0 $1",1],["(\\d{2})(\\d{6})","$1 $2",["37|4(?:[15]|6[1-8])"],"(0-$1)",1],["(\\d{3})(\\d{5})","$1 $2",["[3-6]"],"(0-$1)",1]],"0",0,"[08]",0,0,0,[0,["6\\d{7}"]]],LU:["352","00","35[013-9]\\d{4,8}|6\\d{8}|35\\d{2,4}|(?:[2457-9]\\d|3[0-46-9])\\d{2,9}",[4,5,6,7,8,9,10,11],[["(\\d{2})(\\d{3})","$1 $2",["2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])"]],["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])"]],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["20[2-689]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{1,2})","$1 $2 $3 $4",["2(?:[0367]|4[3-8])"]],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["80[01]|90[015]"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3 $4",["20"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{1,2})","$1 $2 $3 $4 $5",["2(?:[0367]|4[3-8])"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{1,5})","$1 $2 $3 $4",["[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]"]]],0,0,"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\d)",0,0,0,[0,["6(?:[269][18]|5[1568]|7[189]|81)\\d{6}",[9]]]],LV:["371","00","(?:[268]\\d|90)\\d{6}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[269]|8[01]"]]],0,0,0,0,0,0,[0,["23(?:23[0-57-9]|33[0238])\\d{3}|2(?:[0-24-9]\\d\\d|3(?:0[07]|[14-9]\\d|2[024-9]|3[0-24-9]))\\d{4}"]]],LY:["218","00","[2-9]\\d{8}",[9],[["(\\d{2})(\\d{7})","$1-$2",["[2-9]"],"0$1"]],"0",0,0,0,0,0,[0,["9[1-6]\\d{7}"]]],MA:["212","00","[5-8]\\d{8}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["5[45]"],"0$1"],["(\\d{4})(\\d{5})","$1-$2",["5(?:2[2-46-9]|3[3-9]|9)|8(?:0[89]|92)"],"0$1"],["(\\d{2})(\\d{7})","$1-$2",["8"],"0$1"],["(\\d{3})(\\d{6})","$1-$2",["[5-7]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:6(?:[0-79]\\d|8[0-247-9])|7(?:[0167]\\d|2[0-4]|5[01]|8[0-3]))\\d{6}"]]],MC:["377","00","(?:[3489]|6\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["4"],"0$1"],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[389]"]],["(\\d)(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4 $5",["6"],"0$1"]],"0",0,0,0,0,0,[0,["4(?:[469]\\d|5[1-9])\\d{5}|(?:3|6\\d)\\d{7}"]]],MD:["373","00","(?:[235-7]\\d|[89]0)\\d{6}",[8],[["(\\d{3})(\\d{5})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["22|3"],"0$1"],["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["[25-7]"],"0$1"]],"0",0,0,0,0,0,[0,["562\\d{5}|(?:6\\d|7[16-9])\\d{6}"]]],ME:["382","00","(?:20|[3-79]\\d)\\d{6}|80\\d{6,7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[2-9]"],"0$1"]],"0",0,0,0,0,0,[0,["6(?:[07-9]\\d|3[024]|6[0-25])\\d{5}",[8]]]],MF:["590","00","590\\d{6}|(?:69|80|9\\d)\\d{7}",[9],0,"0",0,0,0,0,0,[0,["69(?:0\\d\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\d)|6(?:1[016-9]|5[0-4]|[67]\\d))\\d{4}"]]],MG:["261","00","[23]\\d{8}",[9],[["(\\d{2})(\\d{2})(\\d{3})(\\d{2})","$1 $2 $3 $4",["[23]"],"0$1"]],"0",0,"([24-9]\\d{6})$|0","20$1",0,0,[0,["3[2-47-9]\\d{7}"]]],MH:["692","011","329\\d{4}|(?:[256]\\d|45)\\d{5}",[7],[["(\\d{3})(\\d{4})","$1-$2",["[2-6]"]]],"1",0,0,0,0,0,[0,["(?:(?:23|54)5|329|45[35-8])\\d{4}"]]],MK:["389","00","[2-578]\\d{7}",[8],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["2|34[47]|4(?:[37]7|5[47]|64)"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[347]"],"0$1"],["(\\d{3})(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["[58]"],"0$1"]],"0",0,0,0,0,0,[0,["7(?:3555|(?:474|9[019]7)7)\\d{3}|7(?:[0-25-8]\\d\\d|3(?:[1-48]\\d|6[01]|7[01578])|4(?:2\\d|60|7[01578])|9(?:[2-4]\\d|5[01]|7[015]))\\d{4}"]]],ML:["223","00","[24-9]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24-9]"]]],0,0,0,0,0,0,[0,["2(?:0(?:01|79)|17\\d)\\d{4}|(?:5[01]|[679]\\d|8[2-49])\\d{6}"]]],MM:["95","00","1\\d{5,7}|95\\d{6}|(?:[4-7]|9[0-46-9])\\d{6,8}|(?:2|8\\d)\\d{5,8}",[6,7,8,9,10],[["(\\d)(\\d{2})(\\d{3})","$1 $2 $3",["16|2"],"0$1"],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["[45]|6(?:0[23]|[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-6]"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[12]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[4-7]|8[1-35]"],"0$1"],["(\\d)(\\d{3})(\\d{4,6})","$1 $2 $3",["9(?:2[0-4]|[35-9]|4[137-9])"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["92"],"0$1"],["(\\d)(\\d{5})(\\d{4})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[0,["(?:17[01]|9(?:2(?:[0-4]|[56]\\d\\d)|(?:3(?:[0-36]|4\\d)|(?:6\\d|8[89]|9[4-8])\\d|7(?:3|40|[5-9]\\d))\\d|4(?:(?:[0245]\\d|[1379])\\d|88)|5[0-6])\\d)\\d{4}|9[69]1\\d{6}|9(?:[68]\\d|9[089])\\d{5}",[7,8,9,10]]]],MN:["976","001","[12]\\d{7,9}|[5-9]\\d{7}",[8,9,10],[["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["[12]1"],"0$1"],["(\\d{4})(\\d{4})","$1 $2",["[5-9]"]],["(\\d{3})(\\d{5,6})","$1 $2",["[12]2[1-3]"],"0$1"],["(\\d{4})(\\d{5,6})","$1 $2",["[12](?:27|3[2-8]|4[2-68]|5[1-4689])","[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]"],"0$1"],["(\\d{5})(\\d{4,5})","$1 $2",["[12]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:83[01]|92[039])\\d{5}|(?:5[05]|6[069]|8[015689]|9[013-9])\\d{6}",[8]]]],MO:["853","00","0800\\d{3}|(?:28|[68]\\d)\\d{6}",[7,8],[["(\\d{4})(\\d{3})","$1 $2",["0"]],["(\\d{4})(\\d{4})","$1 $2",["[268]"]]],0,0,0,0,0,0,[0,["6800[0-79]\\d{3}|6(?:[235]\\d\\d|6(?:0[0-5]|[1-9]\\d)|8(?:0[1-9]|[14-8]\\d|2[5-9]|[39][0-4]))\\d{4}",[8]]]],MP:["1","011","[58]\\d{9}|(?:67|90)0\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","670$1",0,"670",[0,["670(?:2(?:3[3-7]|56|8[4-8])|32[1-38]|4(?:33|8[348])|5(?:32|55|88)|6(?:64|70|82)|78[3589]|8[3-9]8|989)\\d{4}"]]],MQ:["596","00","596\\d{6}|(?:69|80|9\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[569]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[0,["69(?:6(?:[0-46-9]\\d|5[0-6])|727)\\d{4}"]]],MR:["222","00","(?:[2-4]\\d\\d|800)\\d{5}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-48]"]]],0,0,0,0,0,0,[0,["[2-4][0-46-9]\\d{6}"]]],MS:["1","011","(?:[58]\\d\\d|664|900)\\d{7}",[10],0,"1",0,"([34]\\d{6})$|1","664$1",0,"664",[0,["664(?:3(?:49|9[1-6])|49[2-6])\\d{4}"]]],MT:["356","00","3550\\d{4}|(?:[2579]\\d\\d|800)\\d{5}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[2357-9]"]]],0,0,0,0,0,0,[0,["(?:7(?:210|[79]\\d\\d)|9(?:[29]\\d\\d|69[67]|8(?:1[1-3]|89|97)))\\d{4}"]]],MU:["230","0(?:0|[24-7]0|3[03])","(?:[57]|8\\d\\d)\\d{7}|[2-468]\\d{6}",[7,8,10],[["(\\d{3})(\\d{4})","$1 $2",["[2-46]|8[013]"]],["(\\d{4})(\\d{4})","$1 $2",["[57]"]],["(\\d{5})(\\d{5})","$1 $2",["8"]]],0,0,0,0,0,0,[0,["5(?:4(?:2[1-389]|7[1-9])|87[15-8])\\d{4}|(?:5(?:2[5-9]|4[3-689]|[57]\\d|8[0-689]|9[0-8])|7(?:0[0-3]|3[013]))\\d{5}",[8]]],"020"],MV:["960","0(?:0|19)","(?:800|9[0-57-9]\\d)\\d{7}|[34679]\\d{6}",[7,10],[["(\\d{3})(\\d{4})","$1-$2",["[34679]"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"]]],0,0,0,0,0,0,[0,["(?:46[46]|[79]\\d\\d)\\d{4}",[7]]],"00"],MW:["265","00","(?:[1289]\\d|31|77)\\d{7}|1\\d{6}",[7,9],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["1[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[137-9]"],"0$1"]],"0",0,0,0,0,0,[0,["111\\d{6}|(?:31|77|[89][89])\\d{7}",[9]]]],MX:["52","0[09]","[2-9]\\d{9}",[10],[["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["33|5[56]|81"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[2-9]"]]],0,0,0,0,0,0,[0,["657[12]\\d{6}|(?:2(?:2\\d|3[1-35-8]|4[13-9]|7[1-689]|8[1-578]|9[467])|3(?:1[1-79]|[2458][1-9]|3\\d|7[1-8]|9[1-5])|4(?:1[1-57-9]|[25-7][1-9]|3[1-8]|4\\d|8[1-35-9]|9[2-689])|5(?:[56]\\d|88|9[1-79])|6(?:1[2-68]|[2-4][1-9]|5[1-3689]|6[1-57-9]|7[1-7]|8[67]|9[4-8])|7(?:[1346][1-9]|[27]\\d|5[13-9]|8[1-69]|9[17])|8(?:1\\d|2[13-689]|3[1-6]|4[124-6]|6[1246-9]|7[0-378]|9[12479])|9(?:1[346-9]|2[1-4]|3[2-46-8]|5[1348]|[69]\\d|7[12]|8[1-8]))\\d{7}"]],"00"],MY:["60","00","1\\d{8,9}|(?:3\\d|[4-9])\\d{7}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1-$2 $3",["[4-79]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1-$2 $3",["1(?:[02469]|[378][1-9]|53)|8","1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8"],"0$1"],["(\\d)(\\d{4})(\\d{4})","$1-$2 $3",["3"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{4})","$1-$2-$3-$4",["1(?:[367]|80)"]],["(\\d{3})(\\d{3})(\\d{4})","$1-$2 $3",["15"],"0$1"],["(\\d{2})(\\d{4})(\\d{4})","$1-$2 $3",["1"],"0$1"]],"0",0,0,0,0,0,[0,["1(?:1888[689]|4400|8(?:47|8[27])[0-4])\\d{4}|1(?:0(?:[23568]\\d|4[0-6]|7[016-9]|9[0-8])|1(?:[1-5]\\d\\d|6(?:0[5-9]|[1-9]\\d)|7(?:[0-4]\\d|5[0-7]))|(?:[269]\\d|[37][1-9]|4[235-9])\\d|5(?:31|9\\d\\d)|8(?:1[23]|[236]\\d|4[06]|5(?:46|[7-9])|7[016-9]|8[01]|9[0-8]))\\d{5}",[9,10]]]],MZ:["258","00","(?:2|8\\d)\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["2|8[2-79]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["8[2-79]\\d{7}",[9]]]],NA:["264","00","[68]\\d{7,8}",[8,9],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["88"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["6"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["87"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[0,["(?:60|8[1245])\\d{7}",[9]]]],NC:["687","00","(?:050|[2-57-9]\\d\\d)\\d{3}",[6],[["(\\d{2})(\\d{2})(\\d{2})","$1.$2.$3",["[02-57-9]"]]],0,0,0,0,0,0,[0,["(?:5[0-4]|[79]\\d|8[0-79])\\d{4}"]]],NE:["227","00","[027-9]\\d{7}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["08"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[089]|2[013]|7[0467]"]]],0,0,0,0,0,0,[0,["(?:23|7[0467]|[89]\\d)\\d{6}"]]],NF:["672","00","[13]\\d{5}",[6],[["(\\d{2})(\\d{4})","$1 $2",["1[0-3]"]],["(\\d)(\\d{5})","$1 $2",["[13]"]]],0,0,"([0-258]\\d{4})$","3$1",0,0,[0,["(?:14|3[58])\\d{4}"]]],NG:["234","009","2[0-24-9]\\d{8}|[78]\\d{10,13}|[7-9]\\d{9}|[1-9]\\d{7}|[124-7]\\d{6}",[7,8,10,11,12,13,14],[["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["78"],"0$1"],["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[12]|9(?:0[3-9]|[1-9])"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["[3-6]|7(?:0[0-689]|[1-79])|8[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[7-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["20[129]"],"0$1"],["(\\d{4})(\\d{2})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{3})(\\d{4})(\\d{4,5})","$1 $2 $3",["[78]"],"0$1"],["(\\d{3})(\\d{5})(\\d{5,6})","$1 $2 $3",["[78]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:702[0-24-9]|819[01])\\d{6}|(?:7(?:0[13-9]|[12]\\d)|8(?:0[1-9]|1[0-8])|9(?:0[1-9]|1[1-6]))\\d{7}",[10]]]],NI:["505","00","(?:1800|[25-8]\\d{3})\\d{4}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[125-8]"]]],0,0,0,0,0,0,[0,["(?:5(?:5[0-7]|[78]\\d)|6(?:20|3[035]|4[045]|5[05]|77|8[1-9]|9[059])|(?:7[5-8]|8\\d)\\d)\\d{5}"]]],NL:["31","00","(?:[124-7]\\d\\d|3(?:[02-9]\\d|1[0-8]))\\d{6}|8\\d{6,9}|9\\d{6,10}|1\\d{4,5}",[5,6,7,8,9,10,11],[["(\\d{3})(\\d{4,7})","$1 $2",["[89]0"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["66"],"0$1"],["(\\d)(\\d{8})","$1 $2",["6"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["1[16-8]|2[259]|3[124]|4[17-9]|5[124679]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-578]|91"],"0$1"],["(\\d{3})(\\d{3})(\\d{5})","$1 $2 $3",["9"],"0$1"]],"0",0,0,0,0,0,[0,["(?:6[1-58]|970\\d)\\d{7}",[9,11]]]],NO:["47","00","(?:0|[2-9]\\d{3})\\d{4}",[5,8],[["(\\d{3})(\\d{2})(\\d{3})","$1 $2 $3",["8"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2-79]"]]],0,0,0,0,0,"[02-689]|7[0-8]",[0,["(?:4[015-8]|9\\d)\\d{6}",[8]]]],NP:["977","00","(?:1\\d|9)\\d{9}|[1-9]\\d{7}",[8,10,11],[["(\\d)(\\d{7})","$1-$2",["1[2-6]"],"0$1"],["(\\d{2})(\\d{6})","$1-$2",["1[01]|[2-8]|9(?:[1-59]|[67][2-6])"],"0$1"],["(\\d{3})(\\d{7})","$1-$2",["9"]]],"0",0,0,0,0,0,[0,["9(?:00|6[0-3]|7[024-6]|8[0-24-68])\\d{7}",[10]]]],NR:["674","00","(?:444|(?:55|8\\d)\\d|666)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[4-68]"]]],0,0,0,0,0,0,[0,["(?:55[3-9]|666|8\\d\\d)\\d{4}"]]],NU:["683","00","(?:[4-7]|888\\d)\\d{3}",[4,7],[["(\\d{3})(\\d{4})","$1 $2",["8"]]],0,0,0,0,0,0,[0,["(?:[56]|888[1-9])\\d{3}"]]],NZ:["64","0(?:0|161)","[1289]\\d{9}|50\\d{5}(?:\\d{2,3})?|[27-9]\\d{7,8}|(?:[34]\\d|6[0-35-9])\\d{6}|8\\d{4,6}",[5,6,7,8,9,10],[["(\\d{2})(\\d{3,8})","$1 $2",["8[1-79]"],"0$1"],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["50[036-8]|8|90","50(?:[0367]|88)|8|90"],"0$1"],["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["24|[346]|7[2-57-9]|9[2-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["2(?:10|74)|[589]"],"0$1"],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["1|2[028]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,5})","$1 $2 $3",["2(?:[169]|7[0-35-9])|7"],"0$1"]],"0",0,0,0,0,0,[0,["2(?:[0-27-9]\\d|6)\\d{6,7}|2(?:1\\d|75)\\d{5}",[8,9,10]]],"00"],OM:["968","00","(?:1505|[279]\\d{3}|500)\\d{4}|800\\d{5,6}",[7,8,9],[["(\\d{3})(\\d{4,6})","$1 $2",["[58]"]],["(\\d{2})(\\d{6})","$1 $2",["2"]],["(\\d{4})(\\d{4})","$1 $2",["[179]"]]],0,0,0,0,0,0,[0,["(?:1505|90[1-9]\\d)\\d{4}|(?:7[126-9]|9[1-9])\\d{6}",[8]]]],PA:["507","00","(?:00800|8\\d{3})\\d{6}|[68]\\d{7}|[1-57-9]\\d{6}",[7,8,10,11],[["(\\d{3})(\\d{4})","$1-$2",["[1-57-9]"]],["(\\d{4})(\\d{4})","$1-$2",["[68]"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["(?:1[16]1|21[89]|6\\d{3}|8(?:1[01]|7[23]))\\d{4}",[7,8]]]],PE:["51","00|19(?:1[124]|77|90)00","(?:[14-8]|9\\d)\\d{7}",[8,9],[["(\\d{3})(\\d{5})","$1 $2",["80"],"(0$1)"],["(\\d)(\\d{7})","$1 $2",["1"],"(0$1)"],["(\\d{2})(\\d{6})","$1 $2",["[4-8]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"]]],"0",0,0,0,0,0,[0,["9\\d{8}",[9]]],"00"," Anexo "],PF:["689","00","4\\d{5}(?:\\d{2})?|8\\d{7,8}",[6,8,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["44"]],["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["4|8[7-9]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]]],0,0,0,0,0,0,[0,["8[7-9]\\d{6}",[8]]]],PG:["675","00|140[1-3]","(?:180|[78]\\d{3})\\d{4}|(?:[2-589]\\d|64)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["18|[2-69]|85"]],["(\\d{4})(\\d{4})","$1 $2",["[78]"]]],0,0,0,0,0,0,[0,["(?:7\\d|8[1-38])\\d{6}",[8]]],"00"],PH:["63","00","(?:[2-7]|9\\d)\\d{8}|2\\d{5}|(?:1800|8)\\d{7,9}",[6,8,9,10,11,12,13],[["(\\d)(\\d{5})","$1 $2",["2"],"(0$1)"],["(\\d{4})(\\d{4,6})","$1 $2",["3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2","3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))"],"(0$1)"],["(\\d{5})(\\d{4})","$1 $2",["346|4(?:27|9[35])|883","3469|4(?:279|9(?:30|56))|8834"],"(0$1)"],["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["2"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[3-7]|8[2-8]"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]],["(\\d{4})(\\d{1,2})(\\d{3})(\\d{4})","$1 $2 $3 $4",["1"]]],"0",0,0,0,0,0,[0,["(?:8(?:1[37]|9[5-8])|9(?:0[5-9]|1[0-24-9]|[235-7]\\d|4[2-9]|8[135-9]|9[1-9]))\\d{7}",[10]]]],PK:["92","00","122\\d{6}|[24-8]\\d{10,11}|9(?:[013-9]\\d{8,10}|2(?:[01]\\d\\d|2(?:[06-8]\\d|1[01]))\\d{7})|(?:[2-8]\\d{3}|92(?:[0-7]\\d|8[1-9]))\\d{6}|[24-9]\\d{8}|[89]\\d{7}",[8,9,10,11,12],[["(\\d{3})(\\d{3})(\\d{2,7})","$1 $2 $3",["[89]0"],"0$1"],["(\\d{4})(\\d{5})","$1 $2",["1"]],["(\\d{3})(\\d{6,7})","$1 $2",["2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])","9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]"],"(0$1)"],["(\\d{2})(\\d{7,8})","$1 $2",["(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]"],"(0$1)"],["(\\d{5})(\\d{5})","$1 $2",["58"],"(0$1)"],["(\\d{3})(\\d{7})","$1 $2",["3"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91"],"(0$1)"],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[24-9]"],"(0$1)"]],"0",0,0,0,0,0,[0,["3(?:[0-247]\\d|3[0-79]|55|64)\\d{7}",[10]]]],PL:["48","00","(?:6|8\\d\\d)\\d{7}|[1-9]\\d{6}(?:\\d{2})?|[26]\\d{5}",[6,7,8,9,10],[["(\\d{5})","$1",["19"]],["(\\d{3})(\\d{3})","$1 $2",["11|20|64"]],["(\\d{2})(\\d{2})(\\d{3})","$1 $2 $3",["(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1","(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19"]],["(\\d{3})(\\d{2})(\\d{2,3})","$1 $2 $3",["64"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)"]],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["1[2-8]|[2-7]|8[1-79]|9[145]"]],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["8"]]],0,0,0,0,0,0,[0,["21(?:1[013-5]|2\\d)\\d{5}|(?:45|5[0137]|6[069]|7[2389]|88)\\d{7}",[9]]]],PM:["508","00","[45]\\d{5}|(?:708|80\\d)\\d{6}",[6,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["[45]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["7"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"],"0$1"]],"0",0,0,0,0,0,[0,["(?:4[02-4]|5[056]|708[45][0-5])\\d{4}"]]],PR:["1","011","(?:[589]\\d\\d|787)\\d{7}",[10],0,"1",0,0,0,0,"787|939",[0,["(?:787|939)[2-9]\\d{6}"]]],PS:["970","00","[2489]2\\d{6}|(?:1\\d|5)\\d{8}",[8,9,10],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["[2489]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["5"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[0,["5[69]\\d{7}",[9]]]],PT:["351","00","1693\\d{5}|(?:[26-9]\\d|30)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["2[12]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["16|[236-9]"]]],0,0,0,0,0,0,[0,["6(?:[06]92(?:30|9\\d)|[35]92(?:[049]\\d|3[034]))\\d{3}|(?:(?:16|6[0356])93|9(?:[1-36]\\d\\d|480))\\d{5}"]]],PW:["680","01[12]","(?:[24-8]\\d\\d|345|900)\\d{4}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[2-9]"]]],0,0,0,0,0,0,[0,["(?:(?:46|83)[0-5]|6[2-4689]0)\\d{4}|(?:45|77|88)\\d{5}"]]],PY:["595","00","59\\d{4,6}|9\\d{5,10}|(?:[2-46-8]\\d|5[0-8])\\d{4,7}",[6,7,8,9,10,11],[["(\\d{3})(\\d{3,6})","$1 $2",["[2-9]0"],"0$1"],["(\\d{2})(\\d{5})","$1 $2",["[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]"],"(0$1)"],["(\\d{3})(\\d{4,5})","$1 $2",["2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["87"]],["(\\d{3})(\\d{6})","$1 $2",["9(?:[5-79]|8[1-7])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[2-8]"],"0$1"],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["9"]]],"0",0,0,0,0,0,[0,["9(?:51|6[129]|7[1-6]|8[1-7]|9[1-5])\\d{6}",[9]]]],QA:["974","00","800\\d{4}|(?:2|800)\\d{6}|(?:0080|[3-7])\\d{7}",[7,8,9,11],[["(\\d{3})(\\d{4})","$1 $2",["2[16]|8"]],["(\\d{4})(\\d{4})","$1 $2",["[3-7]"]]],0,0,0,0,0,0,[0,["[35-7]\\d{7}",[8]]]],RE:["262","00","(?:26|[689]\\d)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2689]"],"0$1"]],"0",0,0,0,0,0,[0,["69(?:2\\d\\d|3(?:[06][0-6]|1[013]|2[0-2]|3[0-39]|4\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))\\d{4}"]]],RO:["40","00","(?:[236-8]\\d|90)\\d{7}|[23]\\d{5}",[6,9],[["(\\d{3})(\\d{3})","$1 $2",["2[3-6]","2[3-6]\\d9"],"0$1"],["(\\d{2})(\\d{4})","$1 $2",["219|31"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[23]1"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[236-9]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:630|702)0\\d{5}|(?:6(?:[04]0|2\\d)|7(?:0[013-9]|1[0-3]|[2-7]\\d|8[03-8]|9[0-39]))\\d{6}",[9]]],0," int "],RS:["381","00","38[02-9]\\d{6,9}|6\\d{7,9}|90\\d{4,8}|38\\d{5,6}|(?:7\\d\\d|800)\\d{3,9}|(?:[12]\\d|3[0-79])\\d{5,10}",[6,7,8,9,10,11,12],[["(\\d{3})(\\d{3,9})","$1 $2",["(?:2[389]|39)0|[7-9]"],"0$1"],["(\\d{2})(\\d{5,10})","$1 $2",["[1-36]"],"0$1"]],"0",0,0,0,0,0,[0,["6(?:[0-689]|7\\d)\\d{6,7}",[8,9,10]]]],RU:["7","810","8\\d{13}|[347-9]\\d{9}",[10,14],[["(\\d{4})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["7(?:1[0-8]|2[1-9])","7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))","7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2"],"8 ($1)",1],["(\\d{5})(\\d)(\\d{2})(\\d{2})","$1 $2 $3 $4",["7(?:1[0-68]|2[1-9])","7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))","7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]"],"8 ($1)",1],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"8 ($1)",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2-$3-$4",["[349]|8(?:[02-7]|1[1-8])"],"8 ($1)",1],["(\\d{4})(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3 $4",["8"],"8 ($1)"]],"8",0,0,0,0,"3[04-689]|[489]",[0,["9\\d{9}",[10]]],"8~10"],RW:["250","00","(?:06|[27]\\d\\d|[89]00)\\d{6}",[8,9],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["0"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[7-9]"],"0$1"]],"0",0,0,0,0,0,[0,["7[237-9]\\d{7}",[9]]]],SA:["966","00","92\\d{7}|(?:[15]|8\\d)\\d{8}",[9,10],[["(\\d{4})(\\d{5})","$1 $2",["9"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["1"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["5"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["81"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]]],"0",0,0,0,0,0,[0,["579[01]\\d{5}|5(?:[013-689]\\d|7[0-8])\\d{6}",[9]]]],SB:["677","0[01]","[6-9]\\d{6}|[1-6]\\d{4}",[5,7],[["(\\d{2})(\\d{5})","$1 $2",["6[89]|7|8[4-9]|9(?:[1-8]|9[0-8])"]]],0,0,0,0,0,0,[0,["48\\d{3}|(?:(?:6[89]|7[1-9]|8[4-9])\\d|9(?:1[2-9]|2[013-9]|3[0-2]|[46]\\d|5[0-46-9]|7[0-689]|8[0-79]|9[0-8]))\\d{4}"]]],SC:["248","010|0[0-2]","800\\d{4}|(?:[249]\\d|64)\\d{5}",[7],[["(\\d)(\\d{3})(\\d{3})","$1 $2 $3",["[246]|9[57]"]]],0,0,0,0,0,0,[0,["2[125-8]\\d{5}"]],"00"],SD:["249","00","[19]\\d{8}",[9],[["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[19]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:1[0-2]|9[0-3569])\\d{7}"]]],SE:["46","00","(?:[26]\\d\\d|9)\\d{9}|[1-9]\\d{8}|[1-689]\\d{7}|[1-4689]\\d{6}|2\\d{5}",[6,7,8,9,10],[["(\\d{2})(\\d{2,3})(\\d{2})","$1-$2 $3",["20"],"0$1",0,"$1 $2 $3"],["(\\d{3})(\\d{4})","$1-$2",["9(?:00|39|44|9)"],"0$1",0,"$1 $2"],["(\\d{2})(\\d{3})(\\d{2})","$1-$2 $3",["[12][136]|3[356]|4[0246]|6[03]|90[1-9]"],"0$1",0,"$1 $2 $3"],["(\\d)(\\d{2,3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["8"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2,3})(\\d{2})","$1-$2 $3",["1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])"],"0$1",0,"$1 $2 $3"],["(\\d{3})(\\d{2,3})(\\d{3})","$1-$2 $3",["9(?:00|39|44)"],"0$1",0,"$1 $2 $3"],["(\\d{2})(\\d{2,3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]"],"0$1",0,"$1 $2 $3 $4"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1-$2 $3 $4",["10|7"],"0$1",0,"$1 $2 $3 $4"],["(\\d)(\\d{3})(\\d{3})(\\d{2})","$1-$2 $3 $4",["8"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1-$2 $3 $4",["[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{2})(\\d{3})","$1-$2 $3 $4",["9"],"0$1",0,"$1 $2 $3 $4"],["(\\d{3})(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1-$2 $3 $4 $5",["[26]"],"0$1",0,"$1 $2 $3 $4 $5"]],"0",0,0,0,0,0,[0,["7[02369]\\d{7}",[9]]]],SG:["65","0[0-3]\\d","(?:(?:1\\d|8)\\d\\d|7000)\\d{7}|[3689]\\d{7}",[8,10,11],[["(\\d{4})(\\d{4})","$1 $2",["[369]|8(?:0[1-9]|[1-9])"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"]],["(\\d{4})(\\d{4})(\\d{3})","$1 $2 $3",["7"]],["(\\d{4})(\\d{3})(\\d{4})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[0,["8(?:09[0-6]|95[0-2])\\d{4}|(?:8(?:0[1-8]|[1-8]\\d|9[0-4])|9[0-8]\\d)\\d{5}",[8]]]],SH:["290","00","(?:[256]\\d|8)\\d{3}",[4,5],0,0,0,0,0,0,"[256]",[0,["[56]\\d{4}",[5]]]],SI:["386","00|10(?:22|66|88|99)","[1-7]\\d{7}|8\\d{4,7}|90\\d{4,6}",[5,6,7,8],[["(\\d{2})(\\d{3,6})","$1 $2",["8[09]|9"],"0$1"],["(\\d{3})(\\d{5})","$1 $2",["59|8"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[37][01]|4[0139]|51|6"],"0$1"],["(\\d)(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[1-57]"],"(0$1)"]],"0",0,0,0,0,0,[0,["65(?:[178]\\d|5[56]|6[01])\\d{4}|(?:[37][01]|4[0139]|51|6[489])\\d{6}",[8]]],"00"],SJ:["47","00","0\\d{4}|(?:[489]\\d|79)\\d{6}",[5,8],0,0,0,0,0,0,"79",[0,["(?:4[015-8]|9\\d)\\d{6}",[8]]]],SK:["421","00","[2-689]\\d{8}|[2-59]\\d{6}|[2-5]\\d{5}",[6,7,9],[["(\\d)(\\d{2})(\\d{3,4})","$1 $2 $3",["21"],"0$1"],["(\\d{2})(\\d{2})(\\d{2,3})","$1 $2 $3",["[3-5][1-8]1","[3-5][1-8]1[67]"],"0$1"],["(\\d)(\\d{3})(\\d{3})(\\d{2})","$1/$2 $3 $4",["2"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[689]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1/$2 $3 $4",["[3-5]"],"0$1"]],"0",0,0,0,0,0,[0,["909[1-9]\\d{5}|9(?:0[1-8]|1[0-24-9]|4[03-57-9]|5\\d)\\d{6}",[9]]]],SL:["232","00","(?:[237-9]\\d|66)\\d{6}",[8],[["(\\d{2})(\\d{6})","$1 $2",["[236-9]"],"(0$1)"]],"0",0,0,0,0,0,[0,["(?:25|3[0-5]|66|7[2-9]|8[08]|9[09])\\d{6}"]]],SM:["378","00","(?:0549|[5-7]\\d)\\d{6}",[8,10],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[5-7]"]],["(\\d{4})(\\d{6})","$1 $2",["0"]]],0,0,"([89]\\d{5})$","0549$1",0,0,[0,["6[16]\\d{6}",[8]]]],SN:["221","00","(?:[378]\\d|93)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[379]"]]],0,0,0,0,0,0,[0,["7(?:(?:[06-8]\\d|21|90)\\d|5(?:01|[19]0|25|[38]3|[4-7]\\d))\\d{5}"]]],SO:["252","00","[346-9]\\d{8}|[12679]\\d{7}|[1-5]\\d{6}|[1348]\\d{5}",[6,7,8,9],[["(\\d{2})(\\d{4})","$1 $2",["8[125]"]],["(\\d{6})","$1",["[134]"]],["(\\d)(\\d{6})","$1 $2",["[15]|2[0-79]|3[0-46-8]|4[0-7]"]],["(\\d)(\\d{7})","$1 $2",["(?:2|90)4|[67]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[348]|64|79|90"]],["(\\d{2})(\\d{5,7})","$1 $2",["1|28|6[0-35-9]|77|9[2-9]"]]],"0",0,0,0,0,0,[0,["(?:(?:15|(?:3[59]|4[89]|6\\d|7[79]|8[08])\\d|9(?:0\\d|[2-9]))\\d|2(?:4\\d|8))\\d{5}|(?:[67]\\d\\d|904)\\d{5}",[7,8,9]]]],SR:["597","00","(?:[2-5]|68|[78]\\d)\\d{5}",[6,7],[["(\\d{2})(\\d{2})(\\d{2})","$1-$2-$3",["56"]],["(\\d{3})(\\d{3})","$1-$2",["[2-5]"]],["(\\d{3})(\\d{4})","$1-$2",["[6-8]"]]],0,0,0,0,0,0,[0,["(?:7[124-7]|8[124-9])\\d{5}",[7]]]],SS:["211","00","[19]\\d{8}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[19]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:12|9[1257-9])\\d{7}"]]],ST:["239","00","(?:22|9\\d)\\d{5}",[7],[["(\\d{3})(\\d{4})","$1 $2",["[29]"]]],0,0,0,0,0,0,[0,["900[5-9]\\d{3}|9(?:0[1-9]|[89]\\d)\\d{4}"]]],SV:["503","00","[267]\\d{7}|(?:80\\d|900)\\d{4}(?:\\d{4})?",[7,8,11],[["(\\d{3})(\\d{4})","$1 $2",["[89]"]],["(\\d{4})(\\d{4})","$1 $2",["[267]"]],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["[89]"]]],0,0,0,0,0,0,[0,["[67]\\d{7}",[8]]]],SX:["1","011","7215\\d{6}|(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"(5\\d{6})$|1","721$1",0,"721",[0,["7215(?:1[02]|2\\d|5[034679]|8[014-8])\\d{4}"]]],SY:["963","00","[1-39]\\d{8}|[1-5]\\d{7}",[8,9],[["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-5]"],"0$1",1],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1",1]],"0",0,0,0,0,0,[0,["9[1-689]\\d{7}",[9]]]],SZ:["268","00","0800\\d{4}|(?:[237]\\d|900)\\d{6}",[8,9],[["(\\d{4})(\\d{4})","$1 $2",["[0237]"]],["(\\d{5})(\\d{4})","$1 $2",["9"]]],0,0,0,0,0,0,[0,["7[6-9]\\d{6}",[8]]]],TA:["290","00","8\\d{3}",[4],0,0,0,0,0,0,"8"],TC:["1","011","(?:[58]\\d\\d|649|900)\\d{7}",[10],0,"1",0,"([2-479]\\d{6})$|1","649$1",0,"649",[0,["649(?:2(?:3[129]|4[1-79])|3\\d\\d|4[34][1-3])\\d{4}"]]],TD:["235","00|16","(?:22|[69]\\d|77)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[2679]"]]],0,0,0,0,0,0,[0,["(?:6[0-689]|77|9\\d)\\d{6}"]],"00"],TG:["228","00","[279]\\d{7}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[279]"]]],0,0,0,0,0,0,[0,["(?:7[019]|9[0-36-9])\\d{6}"]]],TH:["66","00[1-9]","(?:001800|[2-57]|[689]\\d)\\d{7}|1\\d{7,9}",[8,9,10,13],[["(\\d)(\\d{3})(\\d{4})","$1 $2 $3",["2"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[13-9]"],"0$1"],["(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1"]]],"0",0,0,0,0,0,[0,["67(?:1[0-8]|2[4-7])\\d{5}|(?:14|6[1-6]|[89]\\d)\\d{7}",[9]]]],TJ:["992","810","[0-57-9]\\d{8}",[9],[["(\\d{6})(\\d)(\\d{2})","$1 $2 $3",["331","3317"]],["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["44[02-479]|[34]7"]],["(\\d{4})(\\d)(\\d{4})","$1 $2 $3",["3[1-5]"]],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[0-57-9]"]]],0,0,0,0,0,0,[0,["(?:4(?:1[18]|4[02-479])|81[1-9])\\d{6}|(?:0[0-57-9]|1[017]|2[02]|[34]0|5[05]|7[01578]|8[078]|9\\d)\\d{7}"]],"8~10"],TK:["690","00","[2-47]\\d{3,6}",[4,5,6,7],0,0,0,0,0,0,0,[0,["7[2-4]\\d{2,5}"]]],TL:["670","00","7\\d{7}|(?:[2-47]\\d|[89]0)\\d{5}",[7,8],[["(\\d{3})(\\d{4})","$1 $2",["[2-489]|70"]],["(\\d{4})(\\d{4})","$1 $2",["7"]]],0,0,0,0,0,0,[0,["7[2-8]\\d{6}",[8]]]],TM:["993","810","(?:[1-6]\\d|71)\\d{6}",[8],[["(\\d{2})(\\d{2})(\\d{2})(\\d{2})","$1 $2-$3-$4",["12"],"(8 $1)"],["(\\d{3})(\\d)(\\d{2})(\\d{2})","$1 $2-$3-$4",["[1-5]"],"(8 $1)"],["(\\d{2})(\\d{6})","$1 $2",["[67]"],"8 $1"]],"8",0,0,0,0,0,[0,["(?:6\\d|71)\\d{6}"]],"8~10"],TN:["216","00","[2-57-9]\\d{7}",[8],[["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2-57-9]"]]],0,0,0,0,0,0,[0,["3(?:001|[12]40)\\d{4}|(?:(?:[259]\\d|4[0-8])\\d|3(?:1[1-35]|6[0-4]|91))\\d{5}"]]],TO:["676","00","(?:0800|(?:[5-8]\\d\\d|999)\\d)\\d{3}|[2-8]\\d{4}",[5,7],[["(\\d{2})(\\d{3})","$1-$2",["[2-4]|50|6[09]|7[0-24-69]|8[05]"]],["(\\d{4})(\\d{3})","$1 $2",["0"]],["(\\d{3})(\\d{4})","$1 $2",["[5-9]"]]],0,0,0,0,0,0,[0,["(?:5(?:4[0-5]|5[4-6])|6(?:[09]\\d|3[02]|8[15-9])|(?:7\\d|8[46-9])\\d|999)\\d{4}",[7]]]],TR:["90","00","4\\d{6}|8\\d{11,12}|(?:[2-58]\\d\\d|900)\\d{7}",[7,10,12,13],[["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["512|8[01589]|90"],"0$1",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["5(?:[0-59]|61)","5(?:[0-59]|61[06])","5(?:[0-59]|61[06]1)"],"0$1",1],["(\\d{3})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[24][1-8]|3[1-9]"],"(0$1)",1],["(\\d{3})(\\d{3})(\\d{6,7})","$1 $2 $3",["80"],"0$1",1]],"0",0,0,0,0,0,[0,["561(?:011|61\\d)\\d{4}|5(?:0[15-7]|1[06]|24|[34]\\d|5[1-59]|9[46])\\d{7}",[10]]]],TT:["1","011","(?:[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-46-8]\\d{6})$|1","868$1",0,"868",[0,["868(?:(?:2[5-9]|3\\d)\\d|4(?:3[0-6]|[6-9]\\d)|6(?:20|78|8\\d)|7(?:0[1-9]|1[02-9]|[2-9]\\d))\\d{4}"]]],TV:["688","00","(?:2|7\\d\\d|90)\\d{4}",[5,6,7],[["(\\d{2})(\\d{3})","$1 $2",["2"]],["(\\d{2})(\\d{4})","$1 $2",["90"]],["(\\d{2})(\\d{5})","$1 $2",["7"]]],0,0,0,0,0,0,[0,["(?:7[01]\\d|90)\\d{4}",[6,7]]]],TW:["886","0(?:0[25-79]|19)","[2-689]\\d{8}|7\\d{9,10}|[2-8]\\d{7}|2\\d{6}",[7,8,9,10,11],[["(\\d{2})(\\d)(\\d{4})","$1 $2 $3",["202"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["[258]0"],"0$1"],["(\\d)(\\d{3,4})(\\d{4})","$1 $2 $3",["[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]","[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[49]"],"0$1"],["(\\d{2})(\\d{4})(\\d{4,5})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[0,["(?:40001[0-2]|9[0-8]\\d{4})\\d{3}",[9]]],0,"#"],TZ:["255","00[056]","(?:[25-8]\\d|41|90)\\d{7}",[9],[["(\\d{3})(\\d{2})(\\d{4})","$1 $2 $3",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[24]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["5"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[67]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:6[125-9]|7[13-9])\\d{7}"]]],UA:["380","00","[89]\\d{9}|[3-9]\\d{8}",[9,10],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]","6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]"],"0$1"],["(\\d{4})(\\d{5})","$1 $2",["3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])","3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[3-7]|89|9[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["[89]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:39|50|6[36-8]|7[1-357]|9[1-9])\\d{7}",[9]]],"0~0"],UG:["256","00[057]","800\\d{6}|(?:[29]0|[347]\\d)\\d{7}",[9],[["(\\d{4})(\\d{5})","$1 $2",["202","2024"],"0$1"],["(\\d{3})(\\d{6})","$1 $2",["[27-9]|4(?:6[45]|[7-9])"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["[34]"],"0$1"]],"0",0,0,0,0,0,[0,["72(?:[48]0|6[01])\\d{5}|7(?:[015-8]\\d|20|36|4[0-5]|9[89])\\d{6}"]]],US:["1","011","[2-9]\\d{9}|3\\d{6}",[10],[["(\\d{3})(\\d{4})","$1-$2",["310"],0,1],["(\\d{3})(\\d{3})(\\d{4})","($1) $2-$3",["[2-9]"],0,1,"$1-$2-$3"]],"1",0,0,0,0,0,[0,["(?:5056(?:[0-35-9]\\d|4[468])|7302[0-4]\\d)\\d{4}|(?:472[24]|505[2-57-9]|7306|983[2-47-9])\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[1459]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-57-9]|1[02-9]|2[013569]|3[0-24679]|4[167]|5[0-2]|6[01349]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|3[016]|4[0156]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-8]|3[1247]|4[037]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[068]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-389]|8[04-69]))[2-9]\\d{6}"]]],UY:["598","0(?:0|1[3-9]\\d)","0004\\d{2,9}|[1249]\\d{7}|(?:[49]\\d|80)\\d{5}",[6,7,8,9,10,11,12,13],[["(\\d{3})(\\d{3,4})","$1 $2",["0"]],["(\\d{3})(\\d{4})","$1 $2",["[49]0|8"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["9"],"0$1"],["(\\d{4})(\\d{4})","$1 $2",["[124]"]],["(\\d{3})(\\d{3})(\\d{2,4})","$1 $2 $3",["0"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{2,4})","$1 $2 $3 $4",["0"]]],"0",0,0,0,0,0,[0,["9[1-9]\\d{6}",[8]]],"00"," int. "],UZ:["998","00","(?:20|33|[5-79]\\d|88)\\d{7}",[9],[["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["[235-9]"]]],0,0,0,0,0,0,[0,["(?:(?:[25]0|33|88|9[0-57-9])\\d{3}|6(?:1(?:2(?:2[01]|98)|35[0-4]|50\\d|61[23]|7(?:[01][017]|4\\d|55|9[5-9]))|2(?:(?:11|7\\d)\\d|2(?:[12]1|9[01379])|5(?:[126]\\d|3[0-4]))|5(?:19[01]|2(?:27|9[26])|(?:30|59|7\\d)\\d)|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|(?:3[79]|9[0-3])\\d|4(?:56|83)|7(?:[07]\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79]))|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\d|[39][07])|9(?:0\\d|7[079]))|9(?:2(?:1[1267]|3[01]|5\\d|7[0-4])|(?:5[67]|7\\d)\\d|6(?:2[0-26]|8\\d)))|7(?:[07]\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\d|7(?:0\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|(?:33|9[4-6])\\d|5(?:0[0-4]|5[579]|9\\d)|7(?:[0-3579]\\d|4[0467]|6[67]|8[078]))|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\d|9[5-9])|7(?:0\\d|1[024589]|2[0-27]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\d))|5(?:112|2(?:0\\d|2[29]|[49]4)|3[1568]\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\d|8[78]|9[079]))|9(?:22[128]|3(?:2[0-4]|7\\d)|57[02569]|7(?:2[05-9]|3[37]|4\\d|60|7[2579]|87|9[07]))))\\d{4}"]]],VA:["39","00","0\\d{5,10}|3[0-8]\\d{7,10}|55\\d{8}|8\\d{5}(?:\\d{2,4})?|(?:1\\d|39)\\d{7,8}",[6,7,8,9,10,11],0,0,0,0,0,0,"06698",[0,["3[1-9]\\d{8}|3[2-9]\\d{7}",[9,10]]]],VC:["1","011","(?:[58]\\d\\d|784|900)\\d{7}",[10],0,"1",0,"([2-7]\\d{6})$|1","784$1",0,"784",[0,["784(?:4(?:3[0-5]|5[45]|89|9[0-8])|5(?:2[6-9]|3[0-4])|720)\\d{4}"]]],VE:["58","00","[68]00\\d{7}|(?:[24]\\d|[59]0)\\d{8}",[10],[["(\\d{3})(\\d{7})","$1-$2",["[24-689]"],"0$1"]],"0",0,0,0,0,0,[0,["4(?:1[24-8]|2[46])\\d{7}"]]],VG:["1","011","(?:284|[58]\\d\\d|900)\\d{7}",[10],0,"1",0,"([2-578]\\d{6})$|1","284$1",0,"284",[0,["284(?:245|3(?:0[0-3]|4[0-7]|68|9[34])|4(?:4[0-6]|68|9[69])|5(?:4[0-7]|68|9[69]))\\d{4}"]]],VI:["1","011","[58]\\d{9}|(?:34|90)0\\d{7}",[10],0,"1",0,"([2-9]\\d{6})$|1","340$1",0,"340",[0,["340(?:2(?:0\\d|2[06-8]|4[49]|77)|3(?:32|44)|4(?:2[23]|44|7[34]|89)|5(?:1[34]|55)|6(?:2[56]|4[23]|77|9[023])|7(?:1[2-57-9]|2[57]|7\\d)|884|998)\\d{4}"]]],VN:["84","00","[12]\\d{9}|[135-9]\\d{8}|[16]\\d{7}|[16-8]\\d{6}",[7,8,9,10],[["(\\d{2})(\\d{5})","$1 $2",["80"],"0$1",1],["(\\d{4})(\\d{4,6})","$1 $2",["1"],0,1],["(\\d{2})(\\d{3})(\\d{2})(\\d{2})","$1 $2 $3 $4",["6"],"0$1",1],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[357-9]"],"0$1",1],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["2[48]"],"0$1",1],["(\\d{3})(\\d{4})(\\d{3})","$1 $2 $3",["2"],"0$1",1]],"0",0,0,0,0,0,[0,["(?:5(?:2[238]|59)|89[6-9]|99[013-9])\\d{6}|(?:3\\d|5[1689]|7[06-9]|8[1-8]|9[0-8])\\d{7}",[9]]]],VU:["678","00","[57-9]\\d{6}|(?:[238]\\d|48)\\d{3}",[5,7],[["(\\d{3})(\\d{4})","$1 $2",["[57-9]"]]],0,0,0,0,0,0,[0,["(?:[58]\\d|7[013-7])\\d{5}",[7]]]],WF:["681","00","(?:40|72)\\d{4}|8\\d{5}(?:\\d{3})?",[6,9],[["(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3",["[478]"]],["(\\d{3})(\\d{2})(\\d{2})(\\d{2})","$1 $2 $3 $4",["8"]]],0,0,0,0,0,0,[0,["(?:72|8[23])\\d{4}",[6]]]],WS:["685","0","(?:[2-6]|8\\d{5})\\d{4}|[78]\\d{6}|[68]\\d{5}",[5,6,7,10],[["(\\d{5})","$1",["[2-5]|6[1-9]"]],["(\\d{3})(\\d{3,7})","$1 $2",["[68]"]],["(\\d{2})(\\d{5})","$1 $2",["7"]]],0,0,0,0,0,0,[0,["(?:7[1-35-7]|8(?:[3-7]|9\\d{3}))\\d{5}",[7,10]]]],XK:["383","00","2\\d{7,8}|3\\d{7,11}|(?:4\\d\\d|[89]00)\\d{5}",[8,9,10,11,12],[["(\\d{3})(\\d{5})","$1 $2",["[89]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3})","$1 $2 $3",["[2-4]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["2|39"],"0$1"],["(\\d{2})(\\d{7,10})","$1 $2",["3"],"0$1"]],"0",0,0,0,0,0,[0,["4[3-9]\\d{6}",[8]]]],YE:["967","00","(?:1|7\\d)\\d{7}|[1-7]\\d{6}",[7,8,9],[["(\\d)(\\d{3})(\\d{3,4})","$1 $2 $3",["[1-6]|7(?:[24-6]|8[0-7])"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["7"],"0$1"]],"0",0,0,0,0,0,[0,["7[01378]\\d{7}",[9]]]],YT:["262","00","(?:80|9\\d)\\d{7}|(?:26|63)9\\d{6}",[9],0,"0",0,0,0,0,0,[0,["639(?:0[0-79]|1[019]|[267]\\d|3[09]|40|5[05-9]|9[04-79])\\d{4}"]]],ZA:["27","00","[1-79]\\d{8}|8\\d{4,9}",[5,6,7,8,9,10],[["(\\d{2})(\\d{3,4})","$1 $2",["8[1-4]"],"0$1"],["(\\d{2})(\\d{3})(\\d{2,3})","$1 $2 $3",["8[1-4]"],"0$1"],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["860"],"0$1"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["[1-9]"],"0$1"],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8"],"0$1"]],"0",0,0,0,0,0,[0,["(?:1(?:3492[0-25]|4495[0235]|549(?:20|5[01]))|4[34]492[01])\\d{3}|8[1-4]\\d{3,7}|(?:2[27]|47|54)4950\\d{3}|(?:1(?:049[2-4]|9[12]\\d\\d)|(?:6\\d|7[0-46-9])\\d{3}|8(?:5\\d{3}|7(?:08[67]|158|28[5-9]|310)))\\d{4}|(?:1[6-8]|28|3[2-69]|4[025689]|5[36-8])4920\\d{3}|(?:12|[2-5]1)492\\d{4}",[5,6,7,8,9]]]],ZM:["260","00","800\\d{6}|(?:21|63|[79]\\d)\\d{7}",[9],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[28]"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["[79]"],"0$1"]],"0",0,0,0,0,0,[0,["(?:7[5-79]|9[5-8])\\d{7}"]]],ZW:["263","00","2(?:[0-57-9]\\d{6,8}|6[0-24-9]\\d{6,7})|[38]\\d{9}|[35-8]\\d{8}|[3-6]\\d{7}|[1-689]\\d{6}|[1-3569]\\d{5}|[1356]\\d{4}",[5,6,7,8,9,10],[["(\\d{3})(\\d{3,5})","$1 $2",["2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]"],"0$1"],["(\\d)(\\d{3})(\\d{2,4})","$1 $2 $3",["[49]"],"0$1"],["(\\d{3})(\\d{4})","$1 $2",["80"],"0$1"],["(\\d{2})(\\d{7})","$1 $2",["24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2","2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]"],"(0$1)"],["(\\d{2})(\\d{3})(\\d{4})","$1 $2 $3",["7"],"0$1"],["(\\d{3})(\\d{3})(\\d{3,4})","$1 $2 $3",["2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)","2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)"],"0$1"],["(\\d{4})(\\d{6})","$1 $2",["8"],"0$1"],["(\\d{2})(\\d{3,5})","$1 $2",["1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]"],"0$1"],["(\\d{2})(\\d{3})(\\d{3,4})","$1 $2 $3",["29[013-9]|39|54"],"0$1"],["(\\d{4})(\\d{3,5})","$1 $2",["(?:25|54)8","258|5483"],"0$1"]],"0",0,0,0,0,0,[0,["7(?:[1278]\\d|3[1-9])\\d{6}",[9]]]]},nonGeographic:{800:["800",0,"(?:00|[1-9]\\d)\\d{6}",[8],[["(\\d{4})(\\d{4})","$1 $2",["\\d"]]],0,0,0,0,0,0,[0,0,["(?:00|[1-9]\\d)\\d{6}"]]],808:["808",0,"[1-9]\\d{7}",[8],[["(\\d{4})(\\d{4})","$1 $2",["[1-9]"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,0,["[1-9]\\d{7}"]]],870:["870",0,"7\\d{11}|[35-7]\\d{8}",[9,12],[["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["[35-7]"]]],0,0,0,0,0,0,[0,["(?:[356]|774[45])\\d{8}|7[6-8]\\d{7}"]]],878:["878",0,"10\\d{10}",[12],[["(\\d{2})(\\d{5})(\\d{5})","$1 $2 $3",["1"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,["10\\d{10}"]]],881:["881",0,"6\\d{9}|[0-36-9]\\d{8}",[9,10],[["(\\d)(\\d{3})(\\d{5})","$1 $2 $3",["[0-37-9]"]],["(\\d)(\\d{3})(\\d{5,6})","$1 $2 $3",["6"]]],0,0,0,0,0,0,[0,["6\\d{9}|[0-36-9]\\d{8}"]]],882:["882",0,"[13]\\d{6}(?:\\d{2,5})?|[19]\\d{7}|(?:[25]\\d\\d|4)\\d{7}(?:\\d{2})?",[7,8,9,10,11,12],[["(\\d{2})(\\d{5})","$1 $2",["16|342"]],["(\\d{2})(\\d{6})","$1 $2",["49"]],["(\\d{2})(\\d{2})(\\d{4})","$1 $2 $3",["1[36]|9"]],["(\\d{2})(\\d{4})(\\d{3})","$1 $2 $3",["3[23]"]],["(\\d{2})(\\d{3,4})(\\d{4})","$1 $2 $3",["16"]],["(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["10|23|3(?:[15]|4[57])|4|51"]],["(\\d{3})(\\d{4})(\\d{4})","$1 $2 $3",["34"]],["(\\d{2})(\\d{4,5})(\\d{5})","$1 $2 $3",["[1-35]"]]],0,0,0,0,0,0,[0,["342\\d{4}|(?:337|49)\\d{6}|(?:3(?:2|47|7\\d{3})|50\\d{3})\\d{7}",[7,8,9,10,12]],0,0,0,0,0,0,["1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\d{4}|6\\d{5,10})|(?:345\\d|9[89])\\d{6}|(?:10|2(?:3|85\\d)|3(?:[15]|[69]\\d\\d)|4[15-8]|51)\\d{8}"]]],883:["883",0,"(?:[1-4]\\d|51)\\d{6,10}",[8,9,10,11,12],[["(\\d{3})(\\d{3})(\\d{2,8})","$1 $2 $3",["[14]|2[24-689]|3[02-689]|51[24-9]"]],["(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3",["510"]],["(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["21"]],["(\\d{4})(\\d{4})(\\d{4})","$1 $2 $3",["51[13]"]],["(\\d{3})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["[235]"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,["(?:2(?:00\\d\\d|10)|(?:370[1-9]|51\\d0)\\d)\\d{7}|51(?:00\\d{5}|[24-9]0\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\d{5,9}"]]],888:["888",0,"\\d{11}",[11],[["(\\d{3})(\\d{3})(\\d{5})","$1 $2 $3"]],0,0,0,0,0,0,[0,0,0,0,0,0,["\\d{11}"]]],979:["979",0,"[1359]\\d{8}",[9],[["(\\d)(\\d{4})(\\d{4})","$1 $2 $3",["[1359]"]]],0,0,0,0,0,0,[0,0,0,["[1359]\\d{8}"]]]}};function Zu(i,r){var s=Array.prototype.slice.call(r);return s.push(_9),i.apply(this,s)}function Ou(i){"@babel/helpers - typeof";return Ou=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Ou(i)}function M9(i,r,s){return Object.defineProperty(i,"prototype",{writable:!1}),i}function D9(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function U9(i,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");i.prototype=Object.create(r&&r.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),r&&oi(i,r)}function z9(i){var r=M2();return function(){var u=ci(i),c;if(r){var m=ci(this).constructor;c=Reflect.construct(u,arguments,m)}else c=u.apply(this,arguments);return L9(this,c)}}function L9(i,r){if(r&&(Ou(r)==="object"||typeof r=="function"))return r;if(r!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _2(i)}function _2(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}function Ru(i){var r=typeof Map=="function"?new Map:void 0;return Ru=function(u){if(u===null||!B9(u))return u;if(typeof u!="function")throw new TypeError("Super expression must either be null or a function");if(typeof r<"u"){if(r.has(u))return r.get(u);r.set(u,c)}function c(){return Dr(u,arguments,ci(this).constructor)}return c.prototype=Object.create(u.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),oi(c,u)},Ru(i)}function Dr(i,r,s){return M2()?Dr=Reflect.construct:Dr=function(c,m,g){var v=[null];v.push.apply(v,m);var x=Function.bind.apply(c,v),y=new x;return g&&oi(y,g.prototype),y},Dr.apply(null,arguments)}function M2(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function B9(i){return Function.toString.call(i).indexOf("[native code]")!==-1}function oi(i,r){return oi=Object.setPrototypeOf||function(u,c){return u.__proto__=c,u},oi(i,r)}function ci(i){return ci=Object.setPrototypeOf?Object.getPrototypeOf:function(s){return s.__proto__||Object.getPrototypeOf(s)},ci(i)}var aa=function(i){U9(s,i);var r=z9(s);function s(u){var c;return D9(this,s),c=r.call(this,u),Object.setPrototypeOf(_2(c),s.prototype),c.name=c.constructor.name,c}return M9(s)}(Ru(Error)),ku=2,H9=17,q9=3,_t="0-9０-９٠-٩۰-۹",G9="-‐-―−ー－",P9="／/",Y9="．.",Z9="  ­​⁠　",k9="()（）［］\\[\\]",F9="~⁓∼～",Hr="".concat(G9).concat(P9).concat(Y9).concat(Z9).concat(k9).concat(F9),Fu="+＋";function Of(i,r){i=i.split("-"),r=r.split("-");for(var s=i[0].split("."),u=r[0].split("."),c=0;c<3;c++){var m=Number(s[c]),g=Number(u[c]);if(m>g)return 1;if(g>m)return-1;if(!isNaN(m)&&isNaN(g))return 1;if(isNaN(m)&&!isNaN(g))return-1}return i[1]&&r[1]?i[1]>r[1]?1:i[1]<r[1]?-1:0:!i[1]&&r[1]?1:i[1]&&!r[1]?-1:0}var V9={}.constructor;function Ur(i){return i!=null&&i.constructor===V9}function _u(i){"@babel/helpers - typeof";return _u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_u(i)}function Pr(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function I9(i,r){for(var s=0;s<r.length;s++){var u=r[s];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(i,u.key,u)}}function Yr(i,r,s){return r&&I9(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}var X9="1.2.0",Q9="1.7.35",Rf=" ext. ",K9=/^\d+$/,Ke=function(){function i(r){Pr(this,i),t6(r),this.metadata=r,D2.call(this,r)}return Yr(i,[{key:"getCountries",value:function(){return Object.keys(this.metadata.countries).filter(function(s){return s!=="001"})}},{key:"getCountryMetadata",value:function(s){return this.metadata.countries[s]}},{key:"nonGeographic",value:function(){if(!(this.v1||this.v2||this.v3))return this.metadata.nonGeographic||this.metadata.nonGeographical}},{key:"hasCountry",value:function(s){return this.getCountryMetadata(s)!==void 0}},{key:"hasCallingCode",value:function(s){if(this.getCountryCodesForCallingCode(s))return!0;if(this.nonGeographic()){if(this.nonGeographic()[s])return!0}else{var u=this.countryCallingCodes()[s];if(u&&u.length===1&&u[0]==="001")return!0}}},{key:"isNonGeographicCallingCode",value:function(s){return this.nonGeographic()?!!this.nonGeographic()[s]:!this.getCountryCodesForCallingCode(s)}},{key:"country",value:function(s){return this.selectNumberingPlan(s)}},{key:"selectNumberingPlan",value:function(s,u){if(s&&K9.test(s)&&(u=s,s=null),s&&s!=="001"){if(!this.hasCountry(s))throw new Error("Unknown country: ".concat(s));this.numberingPlan=new _f(this.getCountryMetadata(s),this)}else if(u){if(!this.hasCallingCode(u))throw new Error("Unknown calling code: ".concat(u));this.numberingPlan=new _f(this.getNumberingPlanMetadata(u),this)}else this.numberingPlan=void 0;return this}},{key:"getCountryCodesForCallingCode",value:function(s){var u=this.countryCallingCodes()[s];if(u)return u.length===1&&u[0].length===3?void 0:u}},{key:"getCountryCodeForCallingCode",value:function(s){var u=this.getCountryCodesForCallingCode(s);if(u)return u[0]}},{key:"getNumberingPlanMetadata",value:function(s){var u=this.getCountryCodeForCallingCode(s);if(u)return this.getCountryMetadata(u);if(this.nonGeographic()){var c=this.nonGeographic()[s];if(c)return c}else{var m=this.countryCallingCodes()[s];if(m&&m.length===1&&m[0]==="001")return this.metadata.countries["001"]}}},{key:"countryCallingCode",value:function(){return this.numberingPlan.callingCode()}},{key:"IDDPrefix",value:function(){return this.numberingPlan.IDDPrefix()}},{key:"defaultIDDPrefix",value:function(){return this.numberingPlan.defaultIDDPrefix()}},{key:"nationalNumberPattern",value:function(){return this.numberingPlan.nationalNumberPattern()}},{key:"possibleLengths",value:function(){return this.numberingPlan.possibleLengths()}},{key:"formats",value:function(){return this.numberingPlan.formats()}},{key:"nationalPrefixForParsing",value:function(){return this.numberingPlan.nationalPrefixForParsing()}},{key:"nationalPrefixTransformRule",value:function(){return this.numberingPlan.nationalPrefixTransformRule()}},{key:"leadingDigits",value:function(){return this.numberingPlan.leadingDigits()}},{key:"hasTypes",value:function(){return this.numberingPlan.hasTypes()}},{key:"type",value:function(s){return this.numberingPlan.type(s)}},{key:"ext",value:function(){return this.numberingPlan.ext()}},{key:"countryCallingCodes",value:function(){return this.v1?this.metadata.country_phone_code_to_countries:this.metadata.country_calling_codes}},{key:"chooseCountryByCountryCallingCode",value:function(s){return this.selectNumberingPlan(s)}},{key:"hasSelectedNumberingPlan",value:function(){return this.numberingPlan!==void 0}}]),i}(),_f=function(){function i(r,s){Pr(this,i),this.globalMetadataObject=s,this.metadata=r,D2.call(this,s.metadata)}return Yr(i,[{key:"callingCode",value:function(){return this.metadata[0]}},{key:"getDefaultCountryMetadataForRegion",value:function(){return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode())}},{key:"IDDPrefix",value:function(){if(!(this.v1||this.v2))return this.metadata[1]}},{key:"defaultIDDPrefix",value:function(){if(!(this.v1||this.v2))return this.metadata[12]}},{key:"nationalNumberPattern",value:function(){return this.v1||this.v2?this.metadata[1]:this.metadata[2]}},{key:"possibleLengths",value:function(){if(!this.v1)return this.metadata[this.v2?2:3]}},{key:"_getFormats",value:function(s){return s[this.v1?2:this.v2?3:4]}},{key:"formats",value:function(){var s=this,u=this._getFormats(this.metadata)||this._getFormats(this.getDefaultCountryMetadataForRegion())||[];return u.map(function(c){return new J9(c,s)})}},{key:"nationalPrefix",value:function(){return this.metadata[this.v1?3:this.v2?4:5]}},{key:"_getNationalPrefixFormattingRule",value:function(s){return s[this.v1?4:this.v2?5:6]}},{key:"nationalPrefixFormattingRule",value:function(){return this._getNationalPrefixFormattingRule(this.metadata)||this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion())}},{key:"_nationalPrefixForParsing",value:function(){return this.metadata[this.v1?5:this.v2?6:7]}},{key:"nationalPrefixForParsing",value:function(){return this._nationalPrefixForParsing()||this.nationalPrefix()}},{key:"nationalPrefixTransformRule",value:function(){return this.metadata[this.v1?6:this.v2?7:8]}},{key:"_getNationalPrefixIsOptionalWhenFormatting",value:function(){return!!this.metadata[this.v1?7:this.v2?8:9]}},{key:"nationalPrefixIsOptionalWhenFormattingInNationalFormat",value:function(){return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata)||this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion())}},{key:"leadingDigits",value:function(){return this.metadata[this.v1?8:this.v2?9:10]}},{key:"types",value:function(){return this.metadata[this.v1?9:this.v2?10:11]}},{key:"hasTypes",value:function(){return this.types()&&this.types().length===0?!1:!!this.types()}},{key:"type",value:function(s){if(this.hasTypes()&&Mf(this.types(),s))return new e6(Mf(this.types(),s),this)}},{key:"ext",value:function(){return this.v1||this.v2?Rf:this.metadata[13]||Rf}}]),i}(),J9=function(){function i(r,s){Pr(this,i),this._format=r,this.metadata=s}return Yr(i,[{key:"pattern",value:function(){return this._format[0]}},{key:"format",value:function(){return this._format[1]}},{key:"leadingDigitsPatterns",value:function(){return this._format[2]||[]}},{key:"nationalPrefixFormattingRule",value:function(){return this._format[3]||this.metadata.nationalPrefixFormattingRule()}},{key:"nationalPrefixIsOptionalWhenFormattingInNationalFormat",value:function(){return!!this._format[4]||this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat()}},{key:"nationalPrefixIsMandatoryWhenFormattingInNationalFormat",value:function(){return this.usesNationalPrefix()&&!this.nationalPrefixIsOptionalWhenFormattingInNationalFormat()}},{key:"usesNationalPrefix",value:function(){return!!(this.nationalPrefixFormattingRule()&&!W9.test(this.nationalPrefixFormattingRule()))}},{key:"internationalFormat",value:function(){return this._format[5]||this.format()}}]),i}(),W9=/^\(?\$1\)?$/,e6=function(){function i(r,s){Pr(this,i),this.type=r,this.metadata=s}return Yr(i,[{key:"pattern",value:function(){return this.metadata.v1?this.type:this.type[0]}},{key:"possibleLengths",value:function(){if(!this.metadata.v1)return this.type[1]||this.metadata.possibleLengths()}}]),i}();function Mf(i,r){switch(r){case"FIXED_LINE":return i[0];case"MOBILE":return i[1];case"TOLL_FREE":return i[2];case"PREMIUM_RATE":return i[3];case"PERSONAL_NUMBER":return i[4];case"VOICEMAIL":return i[5];case"UAN":return i[6];case"PAGER":return i[7];case"VOIP":return i[8];case"SHARED_COST":return i[9]}}function t6(i){if(!i)throw new Error("[libphonenumber-js] `metadata` argument not passed. Check your arguments.");if(!Ur(i)||!Ur(i.countries))throw new Error("[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got ".concat(Ur(i)?"an object of shape: { "+Object.keys(i).join(", ")+" }":"a "+a6(i)+": "+i,"."))}var a6=function(r){return _u(r)};function Zr(i,r){if(r=new Ke(r),r.hasCountry(i))return r.country(i).countryCallingCode();throw new Error("Unknown country: ".concat(i))}function l6(i,r){return r.countries.hasOwnProperty(i)}function D2(i){var r=i.version;typeof r=="number"?(this.v1=r===1,this.v2=r===2,this.v3=r===3,this.v4=r===4):r?Of(r,X9)===-1?this.v2=!0:Of(r,Q9)===-1?this.v3=!0:this.v4=!0:this.v1=!0}var n6=";ext=",Vl=function(r){return"([".concat(_t,"]{1,").concat(r,"})")};function U2(i){var r="20",s="15",u="9",c="6",m="[  \\t,]*",g="[:\\.．]?[  \\t,-]*",v="#?",x="(?:e?xt(?:ensi(?:ó?|ó))?n?|ｅ?ｘｔｎ?|доб|anexo)",y="(?:[xｘ#＃~～]|int|ｉｎｔ)",$="[- ]+",j="[  \\t]*",A="(?:,{2}|;)",O=n6+Vl(r),B=m+x+g+Vl(r)+v,z=m+y+g+Vl(u)+v,P=$+Vl(c)+"#",L=j+A+g+Vl(s)+v,U=j+"(?:,)+"+g+Vl(u)+v;return O+"|"+B+"|"+z+"|"+P+"|"+L+"|"+U}var i6="["+_t+"]{"+ku+"}",r6="["+Fu+"]{0,1}(?:["+Hr+"]*["+_t+"]){3,}["+Hr+_t+"]*",s6=new RegExp("^["+Fu+"]{0,1}(?:["+Hr+"]*["+_t+"]){1,2}$","i"),d6=r6+"(?:"+U2()+")?",u6=new RegExp("^"+i6+"$|^"+d6+"$","i");function o6(i){return i.length>=ku&&u6.test(i)}function c6(i){return s6.test(i)}var Df=new RegExp("(?:"+U2()+")$","i");function f6(i){var r=i.search(Df);if(r<0)return{};for(var s=i.slice(0,r),u=i.match(Df),c=1;c<u.length;){if(u[c])return{number:s,ext:u[c]};c++}}var m6={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"};function h6(i){return m6[i]}function g6(i,r){var s=typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s)return(s=s.call(i)).next.bind(s);if(Array.isArray(i)||(s=p6(i))||r){s&&(i=s);var u=0;return function(){return u>=i.length?{done:!0}:{done:!1,value:i[u++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function p6(i,r){if(i){if(typeof i=="string")return Uf(i,r);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Uf(i,r)}}function Uf(i,r){(r==null||r>i.length)&&(r=i.length);for(var s=0,u=new Array(r);s<r;s++)u[s]=i[s];return u}function zf(i){for(var r="",s=g6(i.split("")),u;!(u=s()).done;){var c=u.value;r+=$6(c,r)||""}return r}function $6(i,r,s){return i==="+"?r?void 0:"+":h6(i)}function Vu(i,r){return v6(i,void 0,r)}function v6(i,r,s){var u=s.type(r),c=u&&u.possibleLengths()||s.possibleLengths();if(!c)return"IS_POSSIBLE";var m=i.length,g=c[0];return g===m?"IS_POSSIBLE":g>m?"TOO_SHORT":c[c.length-1]<m?"TOO_LONG":c.indexOf(m,1)>=0?"IS_POSSIBLE":"INVALID_LENGTH"}function y6(i,r,s){if(r===void 0&&(r={}),s=new Ke(s),r.v2){if(!i.countryCallingCode)throw new Error("Invalid phone number object passed");s.selectNumberingPlan(i.countryCallingCode)}else{if(!i.phone)return!1;if(i.country){if(!s.hasCountry(i.country))throw new Error("Unknown country: ".concat(i.country));s.country(i.country)}else{if(!i.countryCallingCode)throw new Error("Invalid phone number object passed");s.selectNumberingPlan(i.countryCallingCode)}}if(s.possibleLengths())return z2(i.phone||i.nationalNumber,s);if(i.countryCallingCode&&s.isNonGeographicCallingCode(i.countryCallingCode))return!0;throw new Error('Missing "possibleLengths" in metadata. Perhaps the metadata has been generated before v1.0.18.')}function z2(i,r){switch(Vu(i,r)){case"IS_POSSIBLE":return!0;default:return!1}}function na(i,r){return i=i||"",new RegExp("^(?:"+r+")$").test(i)}function x6(i,r){var s=typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s)return(s=s.call(i)).next.bind(s);if(Array.isArray(i)||(s=b6(i))||r){s&&(i=s);var u=0;return function(){return u>=i.length?{done:!0}:{done:!1,value:i[u++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function b6(i,r){if(i){if(typeof i=="string")return Lf(i,r);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Lf(i,r)}}function Lf(i,r){(r==null||r>i.length)&&(r=i.length);for(var s=0,u=new Array(r);s<r;s++)u[s]=i[s];return u}var S6=["MOBILE","PREMIUM_RATE","TOLL_FREE","SHARED_COST","VOIP","PERSONAL_NUMBER","PAGER","UAN","VOICEMAIL"];function Iu(i,r,s){if(r=r||{},!(!i.country&&!i.countryCallingCode)){s=new Ke(s),s.selectNumberingPlan(i.country,i.countryCallingCode);var u=r.v2?i.nationalNumber:i.phone;if(na(u,s.nationalNumberPattern())){if(xu(u,"FIXED_LINE",s))return s.type("MOBILE")&&s.type("MOBILE").pattern()===""||!s.type("MOBILE")||xu(u,"MOBILE",s)?"FIXED_LINE_OR_MOBILE":"FIXED_LINE";for(var c=x6(S6),m;!(m=c()).done;){var g=m.value;if(xu(u,g,s))return g}}}}function xu(i,r,s){return r=s.type(r),!r||!r.pattern()||r.possibleLengths()&&r.possibleLengths().indexOf(i.length)<0?!1:na(i,r.pattern())}function N6(i,r,s){if(r=r||{},s=new Ke(s),s.selectNumberingPlan(i.country,i.countryCallingCode),s.hasTypes())return Iu(i,r,s.metadata)!==void 0;var u=r.v2?i.nationalNumber:i.phone;return na(u,s.nationalNumberPattern())}function C6(i,r,s){var u=new Ke(s),c=u.getCountryCodesForCallingCode(i);return c?c.filter(function(m){return w6(r,m,s)}):[]}function w6(i,r,s){var u=new Ke(s);return u.selectNumberingPlan(r),u.numberingPlan.possibleLengths().indexOf(i.length)>=0}function E6(i){return i.replace(new RegExp("[".concat(Hr,"]+"),"g")," ").trim()}var j6=/(\$\d)/;function A6(i,r,s){var u=s.useInternationalFormat,c=s.withNationalPrefix,m=i.replace(new RegExp(r.pattern()),u?r.internationalFormat():c&&r.nationalPrefixFormattingRule()?r.format().replace(j6,r.nationalPrefixFormattingRule()):r.format());return u?E6(m):m}var T6=/^[\d]+(?:[~\u2053\u223C\uFF5E][\d]+)?$/;function O6(i,r,s){var u=new Ke(s);if(u.selectNumberingPlan(i,r),u.defaultIDDPrefix())return u.defaultIDDPrefix();if(T6.test(u.IDDPrefix()))return u.IDDPrefix()}function R6(i){var r=i.number,s=i.ext;if(!r)return"";if(r[0]!=="+")throw new Error('"formatRFC3966()" expects "number" to be in E.164 format.');return"tel:".concat(r).concat(s?";ext="+s:"")}function _6(i,r){var s=typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s)return(s=s.call(i)).next.bind(s);if(Array.isArray(i)||(s=M6(i))||r){s&&(i=s);var u=0;return function(){return u>=i.length?{done:!0}:{done:!1,value:i[u++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function M6(i,r){if(i){if(typeof i=="string")return Bf(i,r);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Bf(i,r)}}function Bf(i,r){(r==null||r>i.length)&&(r=i.length);for(var s=0,u=new Array(r);s<r;s++)u[s]=i[s];return u}function Hf(i,r){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);r&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),s.push.apply(s,u)}return s}function qf(i){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Hf(Object(s),!0).forEach(function(u){D6(i,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):Hf(Object(s)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(s,u))})}return i}function D6(i,r,s){return r in i?Object.defineProperty(i,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[r]=s,i}var Gf={formatExtension:function(r,s,u){return"".concat(r).concat(u.ext()).concat(s)}};function U6(i,r,s,u){if(s?s=qf(qf({},Gf),s):s=Gf,u=new Ke(u),i.country&&i.country!=="001"){if(!u.hasCountry(i.country))throw new Error("Unknown country: ".concat(i.country));u.country(i.country)}else if(i.countryCallingCode)u.selectNumberingPlan(i.countryCallingCode);else return i.phone||"";var c=u.countryCallingCode(),m=s.v2?i.nationalNumber:i.phone,g;switch(r){case"NATIONAL":return m?(g=qr(m,i.carrierCode,"NATIONAL",u,s),bu(g,i.ext,u,s.formatExtension)):"";case"INTERNATIONAL":return m?(g=qr(m,null,"INTERNATIONAL",u,s),g="+".concat(c," ").concat(g),bu(g,i.ext,u,s.formatExtension)):"+".concat(c);case"E.164":return"+".concat(c).concat(m);case"RFC3966":return R6({number:"+".concat(c).concat(m),ext:i.ext});case"IDD":if(!s.fromCountry)return;var v=L6(m,i.carrierCode,c,s.fromCountry,u);return bu(v,i.ext,u,s.formatExtension);default:throw new Error('Unknown "format" argument passed to "formatNumber()": "'.concat(r,'"'))}}function qr(i,r,s,u,c){var m=z6(u.formats(),i);return m?A6(i,m,{useInternationalFormat:s==="INTERNATIONAL",withNationalPrefix:!(m.nationalPrefixIsOptionalWhenFormattingInNationalFormat()&&c&&c.nationalPrefix===!1)}):i}function z6(i,r){for(var s=_6(i),u;!(u=s()).done;){var c=u.value;if(c.leadingDigitsPatterns().length>0){var m=c.leadingDigitsPatterns()[c.leadingDigitsPatterns().length-1];if(r.search(m)!==0)continue}if(na(r,c.pattern()))return c}}function bu(i,r,s,u){return r?u(i,r,s):i}function L6(i,r,s,u,c){var m=Zr(u,c.metadata);if(m===s){var g=qr(i,r,"NATIONAL",c);return s==="1"?s+" "+g:g}var v=O6(u,void 0,c.metadata);if(v)return"".concat(v," ").concat(s," ").concat(qr(i,null,"INTERNATIONAL",c))}function Pf(i,r){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);r&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),s.push.apply(s,u)}return s}function Yf(i){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Pf(Object(s),!0).forEach(function(u){B6(i,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):Pf(Object(s)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(s,u))})}return i}function B6(i,r,s){return r in i?Object.defineProperty(i,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[r]=s,i}function H6(i,r){if(!(i instanceof r))throw new TypeError("Cannot call a class as a function")}function q6(i,r){for(var s=0;s<r.length;s++){var u=r[s];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(i,u.key,u)}}function G6(i,r,s){return r&&q6(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}var P6=function(){function i(r,s,u){if(H6(this,i),!r)throw new TypeError("`country` or `countryCallingCode` not passed");if(!s)throw new TypeError("`nationalNumber` not passed");if(!u)throw new TypeError("`metadata` not passed");var c=Z6(r,u),m=c.country,g=c.countryCallingCode;this.country=m,this.countryCallingCode=g,this.nationalNumber=s,this.number="+"+this.countryCallingCode+this.nationalNumber,this.getMetadata=function(){return u}}return G6(i,[{key:"setExt",value:function(s){this.ext=s}},{key:"getPossibleCountries",value:function(){return this.country?[this.country]:C6(this.countryCallingCode,this.nationalNumber,this.getMetadata())}},{key:"isPossible",value:function(){return y6(this,{v2:!0},this.getMetadata())}},{key:"isValid",value:function(){return N6(this,{v2:!0},this.getMetadata())}},{key:"isNonGeographic",value:function(){var s=new Ke(this.getMetadata());return s.isNonGeographicCallingCode(this.countryCallingCode)}},{key:"isEqual",value:function(s){return this.number===s.number&&this.ext===s.ext}},{key:"getType",value:function(){return Iu(this,{v2:!0},this.getMetadata())}},{key:"format",value:function(s,u){return U6(this,s,u?Yf(Yf({},u),{},{v2:!0}):{v2:!0},this.getMetadata())}},{key:"formatNational",value:function(s){return this.format("NATIONAL",s)}},{key:"formatInternational",value:function(s){return this.format("INTERNATIONAL",s)}},{key:"getURI",value:function(s){return this.format("RFC3966",s)}}]),i}(),Y6=function(r){return/^[A-Z]{2}$/.test(r)};function Z6(i,r){var s,u,c=new Ke(r);return Y6(i)?(s=i,c.selectNumberingPlan(s),u=c.countryCallingCode()):u=i,{country:s,countryCallingCode:u}}var k6=new RegExp("(["+_t+"])");function F6(i,r,s,u){if(r){var c=new Ke(u);c.selectNumberingPlan(r,s);var m=new RegExp(c.IDDPrefix());if(i.search(m)===0){i=i.slice(i.match(m)[0].length);var g=i.match(k6);if(!(g&&g[1]!=null&&g[1].length>0&&g[1]==="0"))return i}}}function V6(i,r){if(i&&r.numberingPlan.nationalPrefixForParsing()){var s=new RegExp("^(?:"+r.numberingPlan.nationalPrefixForParsing()+")"),u=s.exec(i);if(u){var c,m,g=u.length-1,v=g>0&&u[g];if(r.nationalPrefixTransformRule()&&v)c=i.replace(s,r.nationalPrefixTransformRule()),g>1&&(m=u[1]);else{var x=u[0];c=i.slice(x.length),v&&(m=u[1])}var y;if(v){var $=i.indexOf(u[1]),j=i.slice(0,$);j===r.numberingPlan.nationalPrefix()&&(y=r.numberingPlan.nationalPrefix())}else y=u[0];return{nationalNumber:c,nationalPrefix:y,carrierCode:m}}}return{nationalNumber:i}}function Mu(i,r){var s=V6(i,r),u=s.carrierCode,c=s.nationalNumber;if(c!==i){if(!I6(i,c,r))return{nationalNumber:i};if(r.possibleLengths()&&!X6(c,r))return{nationalNumber:i}}return{nationalNumber:c,carrierCode:u}}function I6(i,r,s){return!(na(i,s.nationalNumberPattern())&&!na(r,s.nationalNumberPattern()))}function X6(i,r){switch(Vu(i,r)){case"TOO_SHORT":case"INVALID_LENGTH":return!1;default:return!0}}function Q6(i,r,s,u){var c=r?Zr(r,u):s;if(i.indexOf(c)===0){u=new Ke(u),u.selectNumberingPlan(r,s);var m=i.slice(c.length),g=Mu(m,u),v=g.nationalNumber,x=Mu(i,u),y=x.nationalNumber;if(!na(y,u.nationalNumberPattern())&&na(v,u.nationalNumberPattern())||Vu(y,u)==="TOO_LONG")return{countryCallingCode:c,number:m}}return{number:i}}function K6(i,r,s,u){if(!i)return{};var c;if(i[0]!=="+"){var m=F6(i,r,s,u);if(m&&m!==i)c=!0,i="+"+m;else{if(r||s){var g=Q6(i,r,s,u),v=g.countryCallingCode,x=g.number;if(v)return{countryCallingCodeSource:"FROM_NUMBER_WITHOUT_PLUS_SIGN",countryCallingCode:v,number:x}}return{number:i}}}if(i[1]==="0")return{};u=new Ke(u);for(var y=2;y-1<=q9&&y<=i.length;){var $=i.slice(1,y);if(u.hasCallingCode($))return u.selectNumberingPlan($),{countryCallingCodeSource:c?"FROM_NUMBER_WITH_IDD":"FROM_NUMBER_WITH_PLUS_SIGN",countryCallingCode:$,number:i.slice(y)};y++}return{}}function J6(i,r){var s=typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s)return(s=s.call(i)).next.bind(s);if(Array.isArray(i)||(s=W6(i))||r){s&&(i=s);var u=0;return function(){return u>=i.length?{done:!0}:{done:!1,value:i[u++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function W6(i,r){if(i){if(typeof i=="string")return Zf(i,r);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Zf(i,r)}}function Zf(i,r){(r==null||r>i.length)&&(r=i.length);for(var s=0,u=new Array(r);s<r;s++)u[s]=i[s];return u}function e5(i,r){var s=r.countries,u=r.defaultCountry,c=r.metadata;c=new Ke(c);for(var m=[],g=J6(s),v;!(v=g()).done;){var x=v.value;if(c.country(x),c.leadingDigits()){if(i&&i.search(c.leadingDigits())===0)return x}else if(Iu({phone:i,country:x},void 0,c.metadata))if(u){if(x===u)return x;m.push(x)}else return x}if(m.length>0)return m[0]}function t5(i,r){var s=r.nationalNumber,u=r.defaultCountry,c=r.metadata,m=c.getCountryCodesForCallingCode(i);if(m)return m.length===1?m[0]:e5(s,{countries:m,defaultCountry:u,metadata:c.metadata})}var L2="+",a5="[\\-\\.\\(\\)]?",kf="(["+_t+"]|"+a5+")",l5="^\\"+L2+kf+"*["+_t+"]"+kf+"*$",n5=new RegExp(l5,"g"),Du=_t,i5="["+Du+"]+((\\-)*["+Du+"])*",r5="a-zA-Z",s5="["+r5+"]+((\\-)*["+Du+"])*",d5="^("+i5+"\\.)*"+s5+"\\.?$",u5=new RegExp(d5,"g"),Ff="tel:",Uu=";phone-context=",o5=";isub=";function c5(i){var r=i.indexOf(Uu);if(r<0)return null;var s=r+Uu.length;if(s>=i.length)return"";var u=i.indexOf(";",s);return u>=0?i.substring(s,u):i.substring(s)}function f5(i){return i===null?!0:i.length===0?!1:n5.test(i)||u5.test(i)}function m5(i,r){var s=r.extractFormattedPhoneNumber,u=c5(i);if(!f5(u))throw new aa("NOT_A_NUMBER");var c;if(u===null)c=s(i)||"";else{c="",u.charAt(0)===L2&&(c+=u);var m=i.indexOf(Ff),g;m>=0?g=m+Ff.length:g=0;var v=i.indexOf(Uu);c+=i.substring(g,v)}var x=c.indexOf(o5);if(x>0&&(c=c.substring(0,x)),c!=="")return c}var h5=250,g5=new RegExp("["+Fu+_t+"]"),p5=new RegExp("[^"+_t+"#]+$");function $5(i,r,s){if(r=r||{},s=new Ke(s),r.defaultCountry&&!s.hasCountry(r.defaultCountry))throw r.v2?new aa("INVALID_COUNTRY"):new Error("Unknown country: ".concat(r.defaultCountry));var u=y5(i,r.v2,r.extract),c=u.number,m=u.ext,g=u.error;if(!c){if(r.v2)throw g==="TOO_SHORT"?new aa("TOO_SHORT"):new aa("NOT_A_NUMBER");return{}}var v=b5(c,r.defaultCountry,r.defaultCallingCode,s),x=v.country,y=v.nationalNumber,$=v.countryCallingCode,j=v.countryCallingCodeSource,A=v.carrierCode;if(!s.hasSelectedNumberingPlan()){if(r.v2)throw new aa("INVALID_COUNTRY");return{}}if(!y||y.length<ku){if(r.v2)throw new aa("TOO_SHORT");return{}}if(y.length>H9){if(r.v2)throw new aa("TOO_LONG");return{}}if(r.v2){var O=new P6($,y,s.metadata);return x&&(O.country=x),A&&(O.carrierCode=A),m&&(O.ext=m),O.__countryCallingCodeSource=j,O}var B=(r.extended?s.hasSelectedNumberingPlan():x)?na(y,s.nationalNumberPattern()):!1;return r.extended?{country:x,countryCallingCode:$,carrierCode:A,valid:B,possible:B?!0:!!(r.extended===!0&&s.possibleLengths()&&z2(y,s)),phone:y,ext:m}:B?x5(x,y,m):{}}function v5(i,r,s){if(i){if(i.length>h5){if(s)throw new aa("TOO_LONG");return}if(r===!1)return i;var u=i.search(g5);if(!(u<0))return i.slice(u).replace(p5,"")}}function y5(i,r,s){var u=m5(i,{extractFormattedPhoneNumber:function(g){return v5(g,s,r)}});if(!u)return{};if(!o6(u))return c6(u)?{error:"TOO_SHORT"}:{};var c=f6(u);return c.ext?c:{number:u}}function x5(i,r,s){var u={country:i,phone:r};return s&&(u.ext=s),u}function b5(i,r,s,u){var c=K6(zf(i),r,s,u.metadata),m=c.countryCallingCodeSource,g=c.countryCallingCode,v=c.number,x;if(g)u.selectNumberingPlan(g);else if(v&&(r||s))u.selectNumberingPlan(r,s),r&&(x=r),g=s||Zr(r,u.metadata);else return{};if(!v)return{countryCallingCodeSource:m,countryCallingCode:g};var y=Mu(zf(v),u),$=y.nationalNumber,j=y.carrierCode,A=t5(g,{nationalNumber:$,defaultCountry:r,metadata:u});return A&&(x=A,A==="001"||u.country(x)),{country:x,countryCallingCode:g,countryCallingCodeSource:m,nationalNumber:$,carrierCode:j}}function Vf(i,r){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);r&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),s.push.apply(s,u)}return s}function If(i){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Vf(Object(s),!0).forEach(function(u){S5(i,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):Vf(Object(s)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(s,u))})}return i}function S5(i,r,s){return r in i?Object.defineProperty(i,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[r]=s,i}function N5(i,r,s){return $5(i,If(If({},r),{},{v2:!0}),s)}function Xf(i,r){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);r&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),s.push.apply(s,u)}return s}function C5(i){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Xf(Object(s),!0).forEach(function(u){w5(i,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):Xf(Object(s)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(s,u))})}return i}function w5(i,r,s){return r in i?Object.defineProperty(i,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[r]=s,i}function E5(i,r){return O5(i)||T5(i,r)||A5(i,r)||j5()}function j5(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function A5(i,r){if(i){if(typeof i=="string")return Qf(i,r);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Qf(i,r)}}function Qf(i,r){(r==null||r>i.length)&&(r=i.length);for(var s=0,u=new Array(r);s<r;s++)u[s]=i[s];return u}function T5(i,r){var s=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s!=null){var u=[],c=!0,m=!1,g,v;try{for(s=s.call(i);!(c=(g=s.next()).done)&&(u.push(g.value),!(r&&u.length===r));c=!0);}catch(x){m=!0,v=x}finally{try{!c&&s.return!=null&&s.return()}finally{if(m)throw v}}return u}}function O5(i){if(Array.isArray(i))return i}function R5(i){var r=Array.prototype.slice.call(i),s=E5(r,4),u=s[0],c=s[1],m=s[2],g=s[3],v,x,y;if(typeof u=="string")v=u;else throw new TypeError("A text for parsing must be a string.");if(!c||typeof c=="string")g?(x=m,y=g):(x=void 0,y=m),c&&(x=C5({defaultCountry:c},x));else if(Ur(c))m?(x=c,y=m):y=c;else throw new Error("Invalid second argument: ".concat(c));return{text:v,options:x,metadata:y}}function Kf(i,r){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);r&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),s.push.apply(s,u)}return s}function Jf(i){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Kf(Object(s),!0).forEach(function(u){_5(i,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):Kf(Object(s)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(s,u))})}return i}function _5(i,r,s){return r in i?Object.defineProperty(i,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[r]=s,i}function M5(i,r,s){r&&r.defaultCountry&&!l6(r.defaultCountry,s)&&(r=Jf(Jf({},r),{},{defaultCountry:void 0}));try{return N5(i,r,s)}catch(u){if(!(u instanceof aa))throw u}}function D5(){var i=R5(arguments),r=i.text,s=i.options,u=i.metadata;return M5(r,s,u)}function U5(i){return new Ke(i).getCountries()}function z5(){return Zu(D5,arguments)}function L5(){return Zu(U5,arguments)}function Wf(){return Zu(Zr,arguments)}const B5=({value:i,onChange:r,placeholder:s,disabled:u=!1,size:c="large",defaultCountry:m="US",className:g="",inputClassName:v="",error:x,style:y})=>{const{t:$,language:j}=Se(),[A,O]=G.useState(m),[B,z]=G.useState(""),P=G.useMemo(()=>{const ie=L5(),$e=["US","CN","CA","GB","AU","JP","KR","SG","HK","TW"];return[...$e.filter(K=>ie.includes(K)),...ie.filter(K=>!$e.includes(K))].map(K=>{const oe=Wf(K);return{value:K,label:`${K} (+${oe})`,callingCode:oe}}).filter(Boolean)},[j]);G.useEffect(()=>{if(i)try{const{phone:ie,phoneCountry:$e}=w2(i);ie&&(O($e),z(ie.toString()))}catch(ie){console.warn("解析手机号失败",ie)}},[]);const L=ie=>{if(O(ie),B)try{r?.(`${B}~${ie}`)}catch($e){console.warn("更新国家后手机号失败:",$e)}},U=ie=>{const K=ie.target.value.replace(/[^\d\s\-\(\)]/g,"").replace(/\D/g,"");if(z(K),K)try{r?.(`${K}~${A}`)}catch(oe){console.warn("更新手机号失败:",oe)}else r?.(void 0)},q=(ie,$e)=>($e.label||"").toLowerCase().includes(ie.toLowerCase()),fe=ie=>{const $e=Wf(ie.value);return o.jsx("div",{children:`+${$e}`})};return o.jsx("div",{className:`phone-input-container ${g} flex gap-15px`,style:y,children:o.jsxs(mi,{theme:{components:{Select:{activeBorderColor:"var(--color-form-item)",selectorBg:"var(--color-form-item)",colorBorder:"var(--color-form-item)",optionSelectedBg:"var(--color-form-item)",borderRadius:6,optionLineHeight:"54px",optionHeight:54,colorText:"#000000",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)"}}},children:[o.jsx(la,{showSearch:!0,value:A,onChange:L,size:c||"large",disabled:u,placeholder:$("common.form.selectCountry"),optionFilterProp:"children",filterOption:q,options:P,labelRender:fe,popupMatchSelectWidth:110,className:"!h-54px text-14px flex-shrink-0 flex-basis-88px"}),o.jsx(se,{value:B,onChange:U,placeholder:s||$("common.form.phonePlaceholder"),className:v||"s-form-input",size:c,disabled:u,status:x?"error":void 0})]})})};function H5(i){const{phone:r,phoneCountry:s}=w2(i),u=z5(`${r}`,s.toUpperCase());return u?u.isValid():!1}const q5=i=>{const{t:r,isZhCN:s}=Se(),u=X.useFormInstance();return G.useEffect(()=>{i&&u.setFieldsValue({phone:i})},[i]),o.jsx(X.Item,{label:r("auth.register.step3.form.phone"),name:"phone",rules:[{validator:(c,m)=>(console.log("value----",m),m?H5(`${m}`)?Promise.resolve():Promise.reject(new Error(r("common.form.phoneInvalid"))):Promise.resolve())}],children:o.jsx(B5,{placeholder:r("common.form.phonePlaceholder"),size:"large",defaultCountry:s?"CN":"US",inputClassName:"s-form-input"})})},G5=()=>{const i=ia(),{t:r,isEnUS:s}=Se(),{formData:u,updateFormData:c}=Gr(),[m]=X.useForm(),[g,v]=G.useState([]),[x,y]=G.useState(!1);G.useEffect(()=>{(async()=>{try{const O=await Ae.meta.getDefaultRoles();O.code===200?v(O.body||[]):Z.error(r("common.messages.loadRolesFailed"))}catch(O){console.error("获取角色列表失败:",O),Z.error(O?.message||r("common.messages.loadRolesFailed"))}})()},[r]);const $=A=>{console.log("form values----",m.getFieldsValue()),c(A),i("/register/password")};ee.useEffect(()=>{const A={firstName:u.firstName,lastName:u.lastName,phone:u.phone,address:u.address,country:u.country||(u.countryRegion&&Array.isArray(u.countryRegion)?u.countryRegion[0]:void 0),state:u.state||u.state||(u.countryRegion&&Array.isArray(u.countryRegion)?u.countryRegion[1]:void 0),postalZipCode:u.postalZipCode};m.setFieldsValue(A)},[u,m]),ee.useEffect(()=>{u.email?u.alias||(Z.warning(r("auth.register.step4.messages.aliasRequired")),i("/register/alias")):(Z.warning(r("auth.register.step4.messages.emailVerificationRequired")),i("/register/email"))},[u.email,u.alias,i]);const j=()=>{const A=[{min:2,message:r("auth.register.step3.validation.nameLength",{min:2,max:12})},{max:12,message:r("auth.register.step3.validation.nameLength",{min:2,max:12})},{pattern:/^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,message:r("auth.register.step3.validation.namePattern")}],O=o.jsx(X.Item,{label:r("auth.register.step3.form.firstName"),name:"firstName",rules:[{required:!0,message:r("auth.register.step3.form.firstNameRequired")},...A],children:o.jsx(se,{placeholder:r("auth.register.step3.form.firstNamePlaceholder"),className:"s-form-input",size:"large"})},"firstName"),B=o.jsx(X.Item,{label:r("auth.register.step3.form.lastName"),name:"lastName",rules:[{required:!0,message:r("auth.register.step3.form.lastNameRequired")},...A],children:o.jsx(se,{placeholder:r("auth.register.step3.form.lastNamePlaceholder"),className:"s-form-input",size:"large"})},"lastName");return s?[O,B]:[B,O]};return o.jsxs(o.Fragment,{children:[o.jsx("h1",{className:"font-arial mb-2 text-center text-[32px] text-[#ff5e13] font-bold",children:r("auth.register.step3.title")}),o.jsxs(X,{form:m,layout:"vertical",onFinish:$,initialValues:{defaultRole:"account.role.investor"},autoComplete:"off",children:[j(),o.jsx(q5,{}),o.jsx(X.Item,{label:r("auth.register.step3.form.address"),name:"address",children:o.jsx(se,{placeholder:r("auth.register.step3.form.addressPlaceholder"),className:"s-form-input",size:"large"})}),o.jsx(X.Item,{label:r("auth.register.step3.form.city"),name:"city",children:o.jsx(se,{placeholder:r("auth.register.step3.form.cityPlaceholder"),className:"s-form-input",size:"large"})}),o.jsx(X.Item,{label:r("auth.register.step3.form.country"),name:"country",children:o.jsx(T2,{placeholder:r("common.form.selectCountry"),size:"large"})}),o.jsx(X.Item,{label:r("common.form.selectState"),name:"state",children:o.jsx(O2,{form:m,placeholder:r("common.form.selectState"),size:"large"})}),o.jsx(X.Item,{label:r("auth.register.step3.form.postalZipCode"),name:"postalZipCode",children:o.jsx(se,{placeholder:r("auth.register.step3.form.postalZipCodePlaceholder"),className:"s-form-input",size:"large"})}),o.jsx(X.Item,{label:r("common.defaultRole"),name:"defaultRole",children:o.jsx(k1.Group,{buttonStyle:"solid",size:"large",children:g.map(A=>o.jsx(k1,{value:A.code,children:A.name},A.code))})}),o.jsx(Da,{text:r("auth.register.step3.buttons.next")})]})]})},P5=()=>{const i=ia(),{t:r}=Se(),{formData:s,clearFormData:u}=Gr(),{setAuthData:c}=Ht(),[m]=X.useForm(),[g,v]=ee.useState(!1),[x,y]=ee.useState(""),[$,j]=ee.useState(!1),A=z=>{y(z)},O=async z=>{if(!$){Z.error(r("auth.register.step4.form.agreeTermsRequired"));return}const P={firstName:s.firstName||"",lastName:s.lastName||"",addressLine1:s.addressLine1||s.address||"",addressLine2:s.addressLine2||s.city||"",stateProvince:s.stateProvince||s.state||"",countryCode:s.countryCode||s.country||"",postalZipCode:s.postalZipCode||"",avatarUrl:s.avatarUrl||"",stageName:s.stageName||"",bio:s.bio||""},L={username:s.email||"",password:z.password,alias:s.alias||"",profile:P,defaultRoleId:s.defaultRole||"account.role.investor"};console.log("注册数据:",L),v(!0);try{const U=await Ae.auth.signup(L);if(U.code===200&&U.body.token){const{token:q,accountId:fe,alias:ie,displayName:$e,avatarUrl:re,roles:K}=U.body,oe={accountId:fe,email:s.email||"",alias:ie,firstName:s.firstName||"",lastName:s.lastName||"",displayName:$e,avatarUrl:re,stageName:s.stageName,roles:K.map(Te=>({id:Te.id,name:Te.name,realm:Te.realm}))};c(q,oe),Z.success(r("auth.register.success")),u(),i("/")}else Z.error(U.message||r("auth.register.error"))}catch(U){console.error("注册失败:",U),Z.error(U?.message||r("auth.register.error"))}finally{v(!1)}},B=G.useMemo(()=>!$||!x.trim(),[$,x]);return ee.useEffect(()=>{s.email?s.alias?(!s.firstName||!s.lastName)&&(Z.warning(r("auth.register.step4.messages.personalInfoRequired")),i("/register/personal-info")):(Z.warning(r("auth.register.step4.messages.aliasRequired")),i("/register/alias")):(Z.warning(r("auth.register.step4.messages.emailVerificationRequired")),i("/register/email"))},[s,i,r]),o.jsxs(o.Fragment,{children:[o.jsx("h1",{className:"font-arial mb-13 text-center text-[32px] text-[#ff5e13] font-bold",children:r("auth.register.step4.title")}),o.jsxs(X,{form:m,layout:"vertical",onFinish:O,autoComplete:"off",size:"large",children:[o.jsx(A2,{form:m,password:x,onPasswordChange:A,agreeTerms:$,onAgreeTermsChange:j}),o.jsx(Da,{loading:g,disabled:B,children:r("auth.register.step4.form.signUp")})]})]})},Y5="/assets/cover2-CjhLzioL.png",Z5=[{id:"pop",name:"Pop",tracks:[{id:1,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:2,title:"Cultural Music Exchange",artist:"",cover:"/api/placeholder/150/150"},{id:3,title:"Electronic",artist:"",cover:"/api/placeholder/150/150"},{id:4,title:"Music In Everyday Life",artist:"",cover:"/api/placeholder/150/150"},{id:5,title:"MONTERO (Call Me by Your Name)",artist:"",cover:"/api/placeholder/150/150"}]},{id:"rap",name:"Rap",tracks:[{id:6,title:"Midnight City",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:7,title:"创建音乐作品集",artist:"今夜天堂重新开启",cover:"/api/placeholder/150/150"},{id:8,title:"Super Freaky Girl",artist:"",cover:"/api/placeholder/150/150"},{id:9,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:10,title:"She Loves You",artist:"",cover:"/api/placeholder/150/150"},{id:11,title:"As It Was",artist:"",cover:"/api/placeholder/150/150"}]},{id:"bass-music",name:"Bass Music",tracks:[{id:12,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:13,title:"Super Freaky Girl",artist:"",cover:"/api/placeholder/150/150"},{id:14,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:15,title:"She Loves You",artist:"",cover:"/api/placeholder/150/150"},{id:16,title:"As It Was",artist:"",cover:"/api/placeholder/150/150"}]},{id:"jazz",name:"Jazz",tracks:[{id:17,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:18,title:"Quevedo: Bzrp Music Sessions, Vol. 52",artist:"",cover:"/api/placeholder/150/150"},{id:19,title:"I'm Good (Blue)",artist:"",cover:"/api/placeholder/150/150"},{id:20,title:"I'm Good (Blue)",artist:"",cover:"/api/placeholder/150/150"},{id:21,title:"As It Was",artist:"",cover:"/api/placeholder/150/150"}]},{id:"ambient",name:"Ambient",tracks:[{id:22,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:23,title:"I Like You",artist:"",cover:"/api/placeholder/150/150"},{id:24,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:25,title:"Quevedo: Bzrp Music Sessions, Vol. 52",artist:"",cover:"/api/placeholder/150/150"},{id:26,title:"As It Was",artist:"",cover:"/api/placeholder/150/150"}]},{id:"noise",name:"Noise",tracks:[{id:27,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:28,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:29,title:"Super Freaky Girl",artist:"",cover:"/api/placeholder/150/150"},{id:30,title:"Shut Down",artist:"",cover:"/api/placeholder/150/150"},{id:31,title:"She Loves You",artist:"",cover:"/api/placeholder/150/150"}]},{id:"jungle",name:"Jungle",tracks:[{id:32,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:33,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:34,title:"I'm Good (Blue)",artist:"",cover:"/api/placeholder/150/150"},{id:35,title:"Shut Down",artist:"",cover:"/api/placeholder/150/150"},{id:36,title:"About Damn",artist:"",cover:"/api/placeholder/150/150"}]},{id:"ambient2",name:"Ambient",tracks:[{id:37,title:"Midnight City Lights",artist:"Beats by Juniper",cover:"/api/placeholder/150/150"},{id:38,title:"I Like You",artist:"",cover:"/api/placeholder/150/150"},{id:39,title:"I Ain't Worried",artist:"",cover:"/api/placeholder/150/150"},{id:40,title:"Quevedo: Bzrp Music Sessions, Vol. 52",artist:"",cover:"/api/placeholder/150/150"},{id:41,title:"About Damn",artist:"",cover:"/api/placeholder/150/150"},{id:42,title:"I'm Good (Blue)",artist:"",cover:"/api/placeholder/150/150"}]}],k5=[{title:"Quevedo: Bzrp Music Sessions, Vo...",genre:"Pop",revenue:"19024.30"},{title:"I'm Good (Blue)",genre:"Jazz",revenue:"15978.80"},{title:"月夜舞·光影中的我们音乐体验",genre:"Bass Music",revenue:"10103.00"},{title:"MONTERO (Call Me by Your Name)",genre:"Ambient",revenue:"8124.31"},{title:"Cultural Music Exchange",genre:"Jungle",revenue:"7009.00"},{title:"MONTERO (Call Me by Your Name)",genre:"Noise",revenue:"5000.11"},{title:"Quevedo: Bzrp Music Sessions, Vo...",genre:"Pop",revenue:"3002.21"}],F5=[{title:"Quevedo: Bzrp Music Sessions, Vo...",genre:"Pop",revenue:"19024.30"},{title:"I'm Good (Blue)",genre:"Jazz",revenue:"15978.80"},{title:"月夜舞·光影中的我们音乐体验",genre:"Bass Music",revenue:"10103.00"},{title:"MONTERO (Call Me by Your Name)",genre:"Ambient",revenue:"8124.31"},{title:"Cultural Music Exchange",genre:"Jungle",revenue:"7009.00"},{title:"MONTERO (Call Me by Your Name)",genre:"Noise",revenue:"5000.11"},{title:"Quevedo: Bzrp Music Sessions, Vo...",genre:"Pop",revenue:"3002.21"}],V5=()=>(Se(),o.jsxs("div",{className:"flex bg-[#0d0d0d]",children:[o.jsx("div",{className:"flex-1 p-8",children:o.jsx("div",{className:"grid grid-cols-4 gap-8",children:Z5.map(i=>o.jsxs("div",{className:"space-y-4 bg-gray-800/60 backdrop-blur-md rounded-20px p-4 ",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("h2",{className:"text-white text-20px font-bold",children:i.name}),o.jsx(ht,{type:"text",children:"See all"})]}),o.jsxs("div",{className:"flex gap-4",children:[o.jsx("img",{src:Y5,alt:"cover",className:"w-105px h-105px rounded-10px"}),o.jsxs("div",{className:"flex flex-col gap-2",children:[o.jsx("div",{className:"text-white text-16px font-bold",children:"Midnight City Lights"}),o.jsx("div",{className:"text-label",children:"Beats by Juniper"})]})]}),o.jsx("div",{className:"space-y-3",children:i.tracks.map(r=>o.jsx("div",{className:"flex items-center space-x-3 group cursor-pointer",children:o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsx("h3",{className:"text-white text-14px font-medium truncate group-hover:text-[#ff6b35] transition-colors",children:r.title}),r.artist&&o.jsx("p",{className:"text-[#999] text-12px truncate mt-1",children:r.artist})]})},r.id))})]},i.id))})}),o.jsxs("div",{className:"w-80 bg-[#0d0d0d] p-6 space-y-8",children:[o.jsxs("div",{children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"text-white text-16px font-semibold",children:"Weekly Revenue Chart"}),o.jsx("button",{className:"text-[#ff6b35] text-12px hover:underline",children:"See all"})]}),o.jsx("div",{className:"space-y-3",children:k5.map((i,r)=>o.jsxs("div",{className:"flex items-center justify-between py-2",children:[o.jsxs("div",{className:"flex-1 min-w-0 pr-4",children:[o.jsx("div",{className:"text-white text-12px font-medium truncate",children:i.title}),o.jsx("div",{className:"text-[#999] text-10px mt-1",children:i.genre})]}),o.jsx("div",{className:"text-white text-12px font-semibold",children:i.revenue})]},r))})]}),o.jsxs("div",{children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"text-white text-16px font-semibold",children:"Weekly Streaming Chart"}),o.jsx("button",{className:"text-[#ff6b35] text-12px hover:underline",children:"See all"})]}),o.jsx("div",{className:"space-y-3",children:F5.map((i,r)=>o.jsxs("div",{className:"flex items-center justify-between py-2",children:[o.jsxs("div",{className:"flex-1 min-w-0 pr-4",children:[o.jsx("div",{className:"text-white text-12px font-medium truncate",children:i.title}),o.jsx("div",{className:"text-[#999] text-10px mt-1",children:i.genre})]}),o.jsx("div",{className:"text-white text-12px font-semibold",children:i.revenue})]},r))})]})]})]})),I5=()=>{const{user:i,isAuthenticated:r}=Ht();if(!r)return o.jsx(V5,{});const s=Z4(i);return o.jsx(zu,{to:s,replace:!0})},X5=()=>l9().map(s=>{if(Ef[s])return{path:s,element:o.jsx(R2,{children:ee.createElement(Ef[s])})}}).filter(Boolean),Q5=()=>{const i=X5(),r=G.useMemo(()=>dh([{path:"/login",element:o.jsx(Tf,{children:o.jsx(E9,{})})},{path:"/register",element:o.jsx(Tf,{children:o.jsx(A9,{})}),children:[{path:"email",element:o.jsx(O9,{})},{path:"alias",element:o.jsx(R9,{})},{path:"personal-info",element:o.jsx(G5,{})},{path:"password",element:o.jsx(P5,{})}]},{path:"/",element:o.jsx(b9,{}),children:[{index:!0,element:o.jsx(I5,{})},...i]},{path:"*",element:o.jsx(R2,{children:o.jsx(j9,{})})}]),[i]);return o.jsx(uh,{router:r})},K5=()=>{const[i,r]=G.useState(()=>Ua.getState());return G.useEffect(()=>Ua.subscribe(r),[]),i},J5=()=>{const{isLoading:i,loadingText:r}=K5();return i?o.jsxs("div",{className:"fixed left-0 top-0 z-888 h-screen w-screen flex flex-col items-center justify-center gap-3 bg-black/80 pl-10",children:[o.jsx(m2,{size:"large",indicator:o.jsx(o2,{spin:!0})}),o.jsx("div",{className:"ml-2 color-primary",children:r})]}):null},W5=()=>{const{language:i}=qu(),{t:r}=Se(),s=i==="zh"?Gh:Ih,u={required:r("common.form.requiredMsg",{label:"${label}"})};return o.jsx(mi,{locale:s,form:{validateMessages:u},theme:{cssVar:!0,token:{colorPrimary:"#ff5e13",colorTextBase:"#656565",fontFamily:"var(--font-family)",fontFamilyCode:"var(--font-family)"},components:{Modal:{contentBg:"var(--color-page-bg)",headerBg:"var(--color-page-bg)",boxShadow:"0 1px 0 0 rgba(255, 255, 255, 0.25) inset, 0 13px 9px -3px rgba(0, 0, 0, 0.25)"},Layout:{headerHeight:120,headerBg:"var(--color-page-bg)",headerColor:"var(--color-label)",headerPadding:0,bodyBg:"var(--color-page-bg)",siderBg:"var(--color-page-bg)"},Button:{colorPrimaryHover:"var(--color-primary-active)",colorPrimaryActive:"var(--color-primary-active)",primaryColor:"var(--color-primary-text)",colorBgContainerDisabled:"var(--color-primary)",borderColorDisabled:"transparent",colorTextDisabled:"var(--color-primary-text)",contentFontSizeLG:18,controlHeightLG:63},Form:{labelColor:"var(--color-label)"},Select:{},Dropdown:{colorPrimary:"var(--color-primary)",controlItemBgHover:"var(--color-primary)",controlItemBgActiveHover:"var(--color-primary)",colorText:"var(--color-label)",colorBgElevated:"rgba(0,0,0,0.5)",fontSize:16},Space:{colorText:"var(--color-label)"}}},children:o.jsxs("div",{className:"App",children:[o.jsx(G.Suspense,{fallback:o.jsx("div",{className:"min-h-screen flex items-center justify-center",children:o.jsx(m2,{size:"large"})}),children:o.jsx(Q5,{})}),o.jsx(J5,{})]})})};var Mr={},e2;function e8(){if(e2)return Mr;e2=1,Object.defineProperty(Mr,"__esModule",{value:!0});var i=function(s){console.warn("[react-gtm]",s)};return Mr.default=i,Mr}var Su,t2;function t8(){if(t2)return Su;t2=1;var i=e8(),r=s(i);function s(c){return c&&c.__esModule?c:{default:c}}var u={tags:function(m){var g=m.id,v=m.events,x=m.dataLayer,y=m.dataLayerName,$=m.preview,j=m.auth,A="&gtm_auth="+j,O="&gtm_preview="+$;g||(0,r.default)("GTM Id is required");var B=`
      <iframe src="https://www.googletagmanager.com/ns.html?id=`+g+A+O+`&gtm_cookies_win=x"
        height="0" width="0" style="display:none;visibility:hidden" id="tag-manager"></iframe>`,z=`
      (function(w,d,s,l,i){w[l]=w[l]||[];
        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js', `+JSON.stringify(v).slice(1,-1)+`});
        var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
        j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'`+A+O+`&gtm_cookies_win=x';
        f.parentNode.insertBefore(j,f);
      })(window,document,'script','`+y+"','"+g+"');",P=this.dataLayer(x,y);return{iframe:B,script:z,dataLayerVar:P}},dataLayer:function(m,g){return`
      window.`+g+" = window."+g+` || [];
      window.`+g+".push("+JSON.stringify(m)+")"}};return Su=u,Su}var Nu,a2;function a8(){if(a2)return Nu;a2=1;var i=t8(),r=s(i);function s(c){return c&&c.__esModule?c:{default:c}}var u={dataScript:function(m){var g=document.createElement("script");return g.innerHTML=m,g},gtm:function(m){var g=r.default.tags(m),v=function(){var j=document.createElement("noscript");return j.innerHTML=g.iframe,j},x=function(){var j=document.createElement("script");return j.innerHTML=g.script,j},y=this.dataScript(g.dataLayerVar);return{noScript:v,script:x,dataScript:y}},initialize:function(m){var g=m.gtmId,v=m.events,x=v===void 0?{}:v,y=m.dataLayer,$=m.dataLayerName,j=$===void 0?"dataLayer":$,A=m.auth,O=A===void 0?"":A,B=m.preview,z=B===void 0?"":B,P=this.gtm({id:g,events:x,dataLayer:y||void 0,dataLayerName:j,auth:O,preview:z});y&&document.head.appendChild(P.dataScript),document.head.insertBefore(P.script(),document.head.childNodes[0]),document.body.insertBefore(P.noScript(),document.body.childNodes[0])},dataLayer:function(m){var g=m.dataLayer,v=m.dataLayerName,x=v===void 0?"dataLayer":v;if(window[x])return window[x].push(g);var y=r.default.dataLayer(g,x),$=this.dataScript(y);document.head.insertBefore($,document.head.childNodes[0])}};return Nu=u,Nu}var Cu,l2;function l8(){if(l2)return Cu;l2=1;var i=a8(),r=s(i);function s(u){return u&&u.__esModule?u:{default:u}}return Cu=r.default,Cu}l8();wh(function(i,r){r._reactRoot||(r._reactRoot=h2.createRoot(r));var s=r._reactRoot;return s.render(i),function(){return new Promise(function(u){setTimeout(function(){s.unmount(),u()},0)})}});h2.createRoot(document.getElementById("root")).render(o.jsx(W5,{}));

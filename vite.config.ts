import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import UnoCSS from 'unocss/vite';
import { fileURLToPath, URL } from 'url';

export default defineConfig({
  plugins: [react(), UnoCSS()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 React 相关库分离
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // 将 Ant Design 分离
          'antd-vendor': ['antd', '@ant-design/icons'],
          // 将其他大型库分离
          'utils-vendor': ['axios', 'zustand', 'i18next', 'react-i18next'],
        },
      },
    },
    chunkSizeWarningLimit: 1000, // 临时提高警告阈值
  },
  server: {
    proxy: {
      '/api': {
        target: 'https://musicapi.renee-arts.com/api/v1',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
});
